#include "offsets.h"
#include "Syscall.h"
#include "syscall-definitions.h"
#include "Terminal.h"
#include "debug_bochs.h"
#include "VfsSyscall.h"
#include "ProcessRegistry.h"
#include "File.h"
#include "Scheduler.h"
#include "UserThread.h"
#include "Loader.h"
#include "ArchThreads.h"
#include "PageManager.h"
#include "Pipe.h"
#include "ShmObjectFdManager.h"

size_t Syscall::syscallException(size_t syscall_number, size_t arg1, size_t arg2, size_t arg3, size_t arg4, size_t arg5)
{
  auto currentProcess = ((UserThread *)currentThread)->process_;
  size_t return_value = 0;
  if ((syscall_number != sc_sched_yield) && (syscall_number != sc_outline)) // no debug print because these might occur very often
  {
    debug(SYSCALL, "Syscall %zd called with arguments %zd(=%zx) %zd(=%zx) %zd(=%zx) %zd(=%zx) %zd(=%zx)\n",
          syscall_number, arg1, arg1, arg2, arg2, arg3, arg3, arg4, arg4, arg5, arg5);
  }

  if (currentThread->canceled == 1)
  {
    if (currentThread->getCancelState() == PTHREAD_CANCEL_ENABLE)
    {
      debug(SYSCALL, "Syscall::defferred canceling\n");
      exitthread(PTHREAD_CANCELED);
    }
  }

  switch (syscall_number)
  {
  case sc_sched_yield:
    Scheduler::instance()->yield();
    break;
  case sc_createprocess:
    return_value = createprocess(arg1, arg2);
    break;
  case sc_exit:
    exit(arg1);
    break;
  case sc_write:
    return_value = write(arg1, arg2, arg3);
    break;
  case sc_read:
    return_value = read(arg1, arg2, arg3);
    break;
  case sc_open:
    return_value = open(arg1, arg2);
    break;
  case sc_close:
    return_value = close(arg1);
    break;
  case sc_outline:
    outline(arg1, arg2);
    break;
  case sc_trace:
    trace();
    break;
  case sc_pseudols:
    pseudols((const char *)arg1, (char *)arg2, arg3);
    break;
  case sc_pthreadcreate:
    return_value = createthread(arg1, arg2, arg3, arg4, arg5);
    break;
  case sc_pthreadexit:
    exitthread(arg1);
    break;
  case sc_pthreadcancel:
    return_value = cancelthread(arg1);
    break;
  case sc_pthreadsetcancelstate:
    return_value = setcancelstate(arg1, arg2);
    break;
  case sc_pthreadsetcanceltype:
    return_value = setcanceltype(arg1, arg2);
    break;
  case sc_pthreaddetach:
    return_value = detach(arg1);
    break;
  case sc_sleep:
    return_value = syscall_sleep(arg1);
    break;
  case sc_clock:
    return_value = syscall_clock();
    break;
  case sc_pthread_join:
    return_value = jointhread(arg1, arg2);
    break;
  case sc_exec:
    return_value = exec(arg1, (char **)arg2);
    break;
  case sc_waitpid:
    return_value = waitpid(arg1, (size_t *)arg2, arg3);
    break;
  case sc_fork:
    return_value = fork();
    break;
  case sc_getPid:
    return_value = ((UserThread *)currentThread)->process_->pid_;
    break;
  case sc_tortillas_bootup:
    break;
  case sc_tortillas_finished:
    break;
  case sc_pipe:
    return_value = pipe((int *)arg1);
    break;
  case sc_brk:
    currentProcess->program_break_lock.acquire();
    return_value = brk(arg1);
    currentProcess->program_break_lock.release();
    break;
  case sc_sbrk:
    return_value = sbrk(arg1);
    break;
  case sc_shm_open:
    return_value = shm_open((char *)arg1, arg2, (mode_t)arg3);
    break;
  case sc_mmap:
    return_value = mmap(((size_t *)arg1)[0], arg2, arg3, arg4, arg5, ((size_t *)arg1)[1]);
    break;
  case sc_munmap:
    return_value = munmap(arg1, arg2);
    break;
  case sc_shm_unlink:
    return_value = shm_unlink((char *)arg1);
    break;
  case sc_lseek:
    return_value = lseek(arg1, arg2, arg3);
    break;
  case sc_mprotect:
    return_value = mprotect(arg1, arg2, arg3);
    break;
  case sc_swap_page_out:
    return_value = swap_page_out(arg1);
    break;
  case sc_swap_page_in:
    return_value = swap_page_in(arg1);
    break;
  case sc_reserve_pages:
    reserve_pages(arg1);
    break;
  default:
    return_value = -1;
    kprintf("Syscall::syscallException: Unimplemented Syscall Number %zd\n", syscall_number);
  }

  if (currentThread->canceled == 1)
  {
    if (currentThread->getCancelState() == PTHREAD_CANCEL_ENABLE)
    {
      debug(SYSCALL, "Syscall::defferred canceling\n");
      exitthread(PTHREAD_CANCELED);
    }
  }

  return return_value;
}

void Syscall::pseudols(const char *pathname, char *buffer, size_t size)
{
  if (buffer && ((size_t)buffer >= USER_BREAK || (size_t)buffer + size > USER_BREAK))
    return;
  if ((size_t)pathname >= USER_BREAK)
    return;
  VfsSyscall::readdir(pathname, buffer, size);
}

size_t Syscall::syscall_sleep(size_t seconds)
{
  const size_t max_sleep_time = 100000;
  if (seconds > max_sleep_time)
  {
    return -1;
  }

  auto current_ticks = Scheduler::instance()->getTicks();
  debug(SYSCALL, "Syscall::SLEEP:  %zd\n", current_ticks);

  auto goal_ticks = current_ticks + (seconds * 1000000000) / 54925439;
  currentThread->wake_time = goal_ticks;
  currentThread->asleep = true;
  Scheduler::instance()->yield();

  return seconds;
}

size_t Syscall::syscall_clock()
{
  auto currentProcess = ((UserThread *)currentThread)->process_;
  size_t sum = 0;
  currentProcess->thread_vector_lock_.acquire();
  for (auto &thread : currentProcess->userthreads_)
  {
    sum += thread->current_process_time;
  }
  auto start_time_current = currentThread->start_process_time;
  currentProcess->thread_vector_lock_.release();

  uint32 hi, lo;
  asm volatile("rdtsc" : "=a"(lo), "=d"(hi));
  uint64 currentTime = (uint64)hi << 32 | lo;

  sum += currentTime - start_time_current;

  return sum;
}

void Syscall::exit(size_t exit_code)
{
  auto currentProcess = ((UserThread *)currentThread)->process_;

  if (((Thread *)currentThread)->type_ == Thread::USER_THREAD)
  {
    ProcessRegistry::instance()->process_vector_lock_.acquire();
    ((UserThread *)currentThread)->process_->thread_vector_lock_.acquire();
    for (auto &thread : ((UserThread *)currentThread)->process_->userthreads_)
    {
      currentProcess->exit_called = true;
      if (thread->getTID() != currentThread->getTID())
      {
        ((Thread *)thread)->setCancelState(PTHREAD_CANCEL_ENABLE);
        ((Thread *)thread)->setCancelType(PTHREAD_CANCEL_ASYNCHRONOUS);
        ((Thread *)thread)->canceled = 1;
        if (thread->waiting_on_other_thread == true)
        {
          thread->waiting_on_other_thread = false;
          thread->wait_for_thread->signal();
        }
        if (thread->waiting_on_process == true)
        {
          thread->waiting_on_process = false;
          thread->wait_for_process->signal();
        }
      }
    }

    ((UserThread *)currentThread)->process_->thread_vector_lock_.release();
    ProcessRegistry::instance()->process_vector_lock_.release();
  }

  ProcessRegistry::instance()->pipe_vector_lock_.acquire();
  ProcessRegistry::instance()->removePipesFromVectorandDeleteThem(currentProcess);
  ProcessRegistry::instance()->pipe_vector_lock_.release();

  ProcessRegistry::instance()->shm_vector_lock.acquire();
  ProcessRegistry::instance()->removeReferencesShmObj(currentProcess);
  ProcessRegistry::instance()->removeShmObj();
  ProcessRegistry::instance()->shm_vector_lock.release();

  ProcessRegistry::instance()->process_vector_lock_.acquire();
  ProcessRegistry::instance()->processes_retvals.insert({currentProcess->pid_, exit_code});
  ProcessRegistry::instance()->removeProcessFromVector(currentProcess->pid_);
  ProcessRegistry::instance()->process_vector_lock_.release();
  debug(SYSCALL, "Syscall::EXIT: called, exit_code: %zd\n", exit_code);
  //ProcessRegistry::instance()->printIPT();
  exitthread(NULL);
  assert(false && "This should never happen");
}

size_t Syscall::write(size_t fd, pointer buffer, size_t size)
{
  // WARNING: this might fail if Kernel PageFaults are not handled
  if ((buffer >= USER_BREAK) || (buffer + size > USER_BREAK))
  {
    return -1U;
  }
  if (buffer == NULL)
  {
    return -1;
  }

  char buffer2[size + 1];
  memcpy(buffer2, (char *)buffer, size);
  buffer2[size + 1] = '\0';

  ProcessRegistry::instance()->pipe_vector_lock_.acquire();
  auto pipe = ProcessRegistry::instance()->getPipeFromVector(fd);

  if (pipe != NULL)
  {

    if (pipe->is_writing_fd(fd))
    {
      pipe->write((char *)buffer2, size);
      ProcessRegistry::instance()->pipe_vector_lock_.release();
      return 0;
    }
    else
    {
      ProcessRegistry::instance()->pipe_vector_lock_.release();
      return -1;
    }
  }
  ProcessRegistry::instance()->pipe_vector_lock_.release();

  size_t num_written = 0;

  ScopeLock lock(ProcessRegistry::instance()->global_fd_list_lock_);
  fd = ((UserThread *)currentThread)->process_->getGlobalFdNum(fd);
  if ((int)fd == -1)
  {
    return -1;
  }

  if (fd == fd_stdout) // stdout
  {
    debug(SYSCALL, "Syscall::write: %.*s\n", (int)size, (char *)buffer);
    kprintf("%.*s", (int)size, (char *)buffer);
    num_written = size;
  }
  else
  {
    num_written = VfsSyscall::write(fd, (char *)buffer, size);
  }
  return num_written;
}

size_t Syscall::read(size_t fd, pointer buffer, size_t count)
{
  if ((buffer >= USER_BREAK) || (buffer + count > USER_BREAK))
  {
    return -1U;
  }

  if (buffer == NULL)
  {
    return -1;
  }

  char *buffer2 = NULL;

  ProcessRegistry::instance()->pipe_vector_lock_.acquire();
  auto pipe = ProcessRegistry::instance()->getPipeFromVector(fd);

  if (pipe != NULL)
  {
    if (pipe->is_reading_fd(fd))
    {
      buffer2 = new char[count + 1];
      // buffer2[count] ='\0';
      pipe->read((char *)buffer2, count);
      buffer2[count] = '\0';

      // pipe_read= true;
    }
    else
    {
      ProcessRegistry::instance()->pipe_vector_lock_.release();
      return -1;
    }
  }
  ProcessRegistry::instance()->pipe_vector_lock_.release();

  if (buffer2 != NULL)
  {

    memcpy((char *)buffer, buffer2, count);
    delete[] buffer2;
    return 0;
  }

  size_t num_read = 0;

  fd = ((UserThread *)currentThread)->process_->getGlobalFdNum(fd);
  if ((int)fd == -1)
  {
    return -1;
  }

  if (fd == fd_stdin)
  {
    // this doesn't! terminate a string with \0, gotta do that yourself
    num_read = currentThread->getTerminal()->readLine((char *)buffer, count);
    debug(SYSCALL, "Syscall::read: %.*s\n", (int)num_read, (char *)buffer);
  }
  else
  {
    num_read = VfsSyscall::read(fd, (char *)buffer, count);
  }
  return num_read;
}

size_t Syscall::close(size_t fd)
{

  auto currentProcess = ((UserThread *)currentThread)->process_;

  ProcessRegistry::instance()->pipe_vector_lock_.acquire();
  auto pipe = ProcessRegistry::instance()->getPipeFromVector(fd);
  if (pipe != NULL)
  {
    pipe->reset_open_closed_bits(fd, currentProcess);
    ProcessRegistry::instance()->pipe_vector_lock_.release();
    return 0;
  }
  ProcessRegistry::instance()->pipe_vector_lock_.release();

  size_t global_fd_num = ((UserThread *)currentThread)->process_->getGlobalFdNum(fd);
  if ((int)global_fd_num == -1)
  {
    return -1;
  }
  if ((int)(((UserThread *)currentThread)->process_->eraseFdMapping((int)global_fd_num)) == -1)
  {
    return -1;
  }

  return VfsSyscall::close(global_fd_num);
}

size_t Syscall::open(size_t path, size_t flags)
{
  if (path >= USER_BREAK)
  {
    return -1U;
  }
  size_t global_fd_num = VfsSyscall::open((char *)path, flags);
  size_t local_fd_num = ((UserThread *)currentThread)->process_->mapGlobalFdNum(global_fd_num);
  if ((int)local_fd_num == -1)
  {
    return -1;
  }
  return local_fd_num;
}

void Syscall::outline(size_t port, pointer text)
{
  // WARNING: this might fail if Kernel PageFaults are not handled
  if (text >= USER_BREAK)
  {
    return;
  }
  if (port == 0xe9) // debug port
  {
    writeLine2Bochs((const char *)text);
  }
}

size_t Syscall::createprocess(size_t path, size_t sleep)
{
  // THIS METHOD IS FOR TESTING PURPOSES ONLY AND NOT MULTITHREADING SAFE!
  // AVOID USING IT AS SOON AS YOU HAVE AN ALTERNATIVE!

  // parameter check begin
  if (path >= USER_BREAK)
  {
    return -1U;
  }

  debug(SYSCALL, "Syscall::createprocess: path:%s sleep:%zd\n", (char *)path, sleep);
  ssize_t fd = VfsSyscall::open((const char *)path, O_RDONLY);
  if (fd == -1)
  {
    return -1U;
  }
  VfsSyscall::close(fd);
  // parameter check end

  size_t process_count = ProcessRegistry::instance()->processCount();
  ProcessRegistry::instance()->createProcess((const char *)path);
  if (sleep)
  {
    while (ProcessRegistry::instance()->processCount() > process_count) // please note that this will fail ;)
    {
      Scheduler::instance()->yield();
    }
  }
  return 0;
}

void Syscall::trace()
{
  currentThread->printBacktrace();
}

size_t Syscall::createthread(size_t thread_id, size_t attr, size_t startroutine, size_t arg, size_t wrapper)
{
  if (thread_id == NULL || thread_id > USER_BREAK)
  {
    return -1;
  }
  if (attr >= USER_BREAK)
  {
    return -1;
  }
  if (startroutine == NULL || startroutine > USER_BREAK)
  {
    return -1;
  }

  auto currentProcess = ((UserThread *)currentThread)->process_;
  if (currentProcess->exec == 1)
  {
    return -1;
  }

  UserThread *userThread = new UserThread(currentProcess, currentProcess->file_name_, startroutine, arg, 0, wrapper);

  auto TID = userThread->getTID();

  currentProcess->thread_vector_lock_.acquire();
  currentProcess->userthreads_.push_back(userThread);
  currentProcess->thread_vector_lock_.release();

  if (thread_id <= USER_BREAK)
  {
    *reinterpret_cast<size_t *>(thread_id) = TID;
  }

  Scheduler::instance()->addNewThread(userThread);
  debug(SYSCALL, "Syscall::createthread: Tid:%zd\n", TID);

  return 0;
}

void Syscall::exitthread(size_t retval)
{
  auto userthread = ((UserThread *)currentThread);
  size_t ret = retval;
  retval = ret;
  // test
  for (int i = 0; i < 16; i++)
  {
    if (userthread->stack_bits[i] == true)
    {
      ProcessRegistry::instance()->ipt_lock_.acquire();
      currentThread->loader_->arch_memory_.archmemory_lock_.acquire();

      auto mapping =  currentThread->loader_->arch_memory_.resolveMapping(userthread->stack_start_ / PAGE_SIZE - i);

      if(mapping.pt && mapping.pt[mapping.pti].present)
      {
        currentThread->loader_->arch_memory_.unmapPage(userthread->stack_start_ / PAGE_SIZE - i);
      }
      
      if(mapping.pt && mapping.pt[mapping.pti].swapped)
      {
          Scheduler::instance()->swap_thread_.swap_Table_.removeReference( mapping.pt[mapping.pti].page_ppn, userthread->process_->pid_);
      }  

      currentThread->loader_->arch_memory_.archmemory_lock_.release();
      ProcessRegistry::instance()->ipt_lock_.release();
    }
  }
  auto userprocess = ((UserThread *)currentThread)->process_;

  userprocess->thread_vector_lock_.acquire();
  if (currentThread->detached == 0)
  {
    debug(SYSCALL, "insert in for joining %zd", currentThread->getTID());
    userprocess->thread_retvals.insert({userthread->getTID(), retval});
  }
  userprocess->removeThreadFromVector(userthread->getTID());

  if (userprocess->userthreads_.size() == 1 && userprocess->exec == 1)
  {
    userprocess->only_one_thread_living.signal();
  }
  userprocess->thread_vector_lock_.release();

  ProcessRegistry::instance()->process_vector_lock_.acquire();
  userprocess->thread_vector_lock_.acquire();
  if (ProcessRegistry::instance()->getProcessfromVector(userprocess->pid_) != NULL && userprocess->userthreads_.size() == 0)
  {
    userprocess->thread_vector_lock_.release();
    ProcessRegistry::instance()->processes_retvals.insert({userprocess->pid_, 0});
    ProcessRegistry::instance()->removeProcessFromVector(userprocess->pid_);
  }
  else
  {
    userprocess->thread_vector_lock_.release();
  }
  ProcessRegistry::instance()->process_vector_lock_.release();
  delete userthread->wait_for_thread;
  delete userthread->wait_for_process;
  
  currentThread->kill();
}

size_t Syscall::cancelthread(size_t threadid)
{
  debug(SYSCALL, "Syscall::cancelthread: called\n");

  auto currentProcess = ((UserThread *)currentThread)->process_;
  currentProcess->thread_vector_lock_.acquire();
  auto thread = currentProcess->getThreadfromVector(threadid);
  if (!thread)
  {
    debug(SYSCALL, "Syscall::cancelthread: No thread with the ID %ld could be found.\n", threadid);

    currentProcess->thread_vector_lock_.release();
    return ESRCH; // ESRCH  No thread with the ID thread could be found.
  }
  // state check unnecessary cause we have undefinned behaviour
  thread->canceled = 1;

  currentProcess->thread_vector_lock_.release();
  debug(SYSCALL, "Syscall::cancelthread: cancelation request send\n");
  return 0;
}

size_t Syscall::setcancelstate(size_t state, size_t oldstate)
{
  if (state != PTHREAD_CANCEL_ENABLE && state != PTHREAD_CANCEL_DISABLE)
  {
    return -1;
  }
  auto previous_state = currentThread->getCancelState();
  if (oldstate != 0 && oldstate <= USER_BREAK)
  {
    *reinterpret_cast<size_t *>(oldstate) = previous_state;
  }

  currentThread->setCancelState((CancelState)state);

  return 0;
}
size_t Syscall::setcanceltype(size_t type, size_t oldtype)
{
  if (type != PTHREAD_CANCEL_DEFERRED && type != PTHREAD_CANCEL_ASYNCHRONOUS)
  {
    return -1;
  }
  auto previous_type = currentThread->getCancelType();
  if (oldtype != 0 && oldtype <= USER_BREAK)
  {
    *reinterpret_cast<size_t *>(oldtype) = previous_type;
  }
  currentThread->setCancelType((CancelType)type);

  return 0;
}

size_t Syscall::detach(size_t threadid)
{
  //    The pthread_detach() function marks the thread identified by
  //  thread as detached.  When a detached thread terminates, its
  //  resources are automatically released back to the system without
  //  the need for another thread to join with the terminated thread.

  //  Attempting to detach an already detached thread results in
  //  unspecified behavior.

  // Once a thread has been detached, it can't be joined with
  // pthread_join(3) or be made joinable again.

  auto currentProcess = ((UserThread *)currentThread)->process_;
  currentProcess->thread_vector_lock_.acquire();
  auto thread = currentProcess->getThreadfromVector(threadid);
  if (!thread)
  {
    debug(SYSCALL, "Syscall::detach: No thread with the ID %ld could be found.\n", threadid);

    currentProcess->thread_vector_lock_.release();
    return ESRCH; //  ESRCH  No thread with the ID thread could be found.
  }

  if (thread->detached)
  {
    debug(SYSCALL, "Syscall::detach: thread is not a joinable thread.\n");

    currentProcess->thread_vector_lock_.release();
    return EINVAL; // EINVAL thread is not a joinable thread.
  }
  thread->detached = 1;
  currentProcess->thread_vector_lock_.release();

  return NOERROR;
}

int Syscall::jointhread(size_t thread_id, size_t ret_val)
{

  auto currentProcess = ((UserThread *)currentThread)->process_;
  auto userthread = ((UserThread *)currentThread);
  assert(currentProcess != NULL);
  assert(userthread != NULL);

  size_t ret_val_copy;
  currentProcess->thread_vector_lock_.acquire();
  if (userthread->join_thread(thread_id, ret_val_copy) == 0)
  {
    currentProcess->thread_vector_lock_.release();
    if (ret_val != 0 && ret_val <= USER_BREAK)
    {
      *(size_t *)ret_val = ret_val_copy;
    }
    return 0;
  }
  else
  {
    currentProcess->thread_vector_lock_.release();
    return -1;
  }
}

size_t Syscall::exec(size_t path, char **argv)
{
  
  auto userthread = (UserThread *)currentThread;
  auto process = userthread->process_;
  size_t argc;

  if (!userthread->checkExecparameters(path, argv, argc))
  {
    return -1;
  }

  int32 fd = VfsSyscall::open((char *)path, O_RDONLY);

  if (fd == -1)
  {
    return -1;
  }

  if (process->exec == 1)
  {
    exitthread(NULL);
  }

  process->lock_for_exec_flag.acquire();
  if (process->exec == 1)
  {
    process->lock_for_exec_flag.release();
    exitthread(NULL);
  }
  process->exec = 1;

  process->lock_for_exec_flag.release();

  Loader *loader = new Loader(fd);

  if (!loader || !loader->loadExecutableAndInitProcess())
  {
    debug(USERPROCESS, "Error: loading %s failed!\n", process->file_name_.c_str());
    process->exec = 0;
    VfsSyscall::close(fd);

    if (loader)
    {
      delete loader;
    }
    return -1;
  }

  process->thread_vector_lock_.acquire();
  for (auto &thread : process->userthreads_)
  {
    if (thread->getTID() != currentThread->getTID())
    {
      thread->setCancelState(PTHREAD_CANCEL_ENABLE);
      thread->setCancelType(PTHREAD_CANCEL_ASYNCHRONOUS);
      thread->canceled = 1;
      if (thread->waiting_on_other_thread)
      {

        thread->waiting_on_other_thread = false;
        thread->wait_for_thread->signal();
      }
    }
  }

  while (process->userthreads_.size() != 1)
  {
    process->only_one_thread_living.wait();
  }
  process->thread_vector_lock_.release();

  // process->process_lock_.acquire();
  if (process->fd_ > 0)
  {
    VfsSyscall::close(process->fd_);
  }
  process->file_name_ = (char *)path;
  process->fd_ = fd;
  process->loader_ = loader; 
  // process->process_lock_.release();

  debug(USERPROCESS, "%d", process->userthreads_.size());

  assert(process->userthreads_.size() == 1 && "other threads still alive ");

  UserThread *thread = new UserThread(process, process->file_name_, (size_t)process->loader_->getEntryFunction(), size_t(nullptr), 0, size_t(nullptr));
  loader->arch_memory_.setPid(process->pid_);

  char **array = thread->copyStringsOnPage(argc, argv);

  thread->user_registers_->rdi = argc;
  thread->user_registers_->rsi = (size_t)array;
  process->thread_vector_lock_.acquire();
  process->userthreads_.push_back(thread);
  process->thread_vector_lock_.release();

  userthread->delete_old_loader = true;
  Scheduler::instance()->addNewThread(thread);
  exitthread(NULL);

  return 0;
}

size_t Syscall::waitpid(size_t pid, size_t *status, size_t options)
{
  debug(SYSCALL, "Syscall::waitpid::  path: %d called with state %p %d\n", pid, status, options);
  if ((size_t)status >= USER_BREAK)
  {
    return -1;
  }

  auto userthread = (UserThread *)currentThread;
  auto process = userthread->process_;

  if (process->pid_ == pid)
  {
    return -1;
  }

  size_t retval_copy;
  ProcessRegistry::instance()->process_vector_lock_.acquire();
  if (ProcessRegistry::instance()->processes_retvals.find(pid) != ProcessRegistry::instance()->processes_retvals.end())
  {
    retval_copy = ProcessRegistry::instance()->processes_retvals[pid];
    ProcessRegistry::instance()->processes_retvals.erase(pid);
    ProcessRegistry::instance()->process_vector_lock_.release();

    if (status != NULL && (size_t)status <= USER_BREAK)
    {
      if (retval_copy == 9999)
      {
        *status = 0x7F00 | 11;
      }
      else
      {
        *status = retval_copy & 0xFF;
      }
    }
    return pid;
  }

  if (ProcessRegistry::instance()->getProcessfromVector(pid) != NULL)
  {

    userthread->waiting_on_process = true;
    userthread->waiting_pid = pid;

    while (userthread->waiting_on_process)
    {
      userthread->wait_for_process->wait();
    }
    retval_copy = ProcessRegistry::instance()->processes_retvals[pid];
    ProcessRegistry::instance()->processes_retvals.erase(pid);

    ProcessRegistry::instance()->process_vector_lock_.release();
    if (status != NULL && (size_t)status <= USER_BREAK)
    {
      if (retval_copy == 9999)
      {
        *status = 0x7F00 | 11;
      }
      else
      {
        *status = retval_copy & 0xFF;
      }
    }
    return pid;
  }
  else
  {
    ProcessRegistry::instance()->process_vector_lock_.release();
    return -1;
  }
}

size_t Syscall::fork()
{
  UserProcess *parent = ((UserThread *)currentThread)->process_;
  UserProcess *child = new UserProcess(*parent);
  if (child == nullptr)
  {
    return -1;
  }

  ProcessRegistry::instance()->process_vector_lock_.acquire();
  ProcessRegistry::instance()->processes_vector.push_back(child);
  ProcessRegistry::instance()->process_vector_lock_.release();

  UserThread *child_thread = new UserThread(*((UserThread *)currentThread), child);
  if (child_thread == nullptr)
  {
    return -1;
  }
  child->userthreads_.clear();

  child->thread_vector_lock_.acquire();
  child->userthreads_.push_back(child_thread);
  child->thread_vector_lock_.release();

  size_t child_pid = child->pid_;
  Scheduler::instance()->addNewThread((Thread *)child_thread);

  return child_pid;
}

int Syscall::pipe(int *fildes)
{

  if ((size_t)fildes >= USER_BREAK || fildes == NULL)
  {
    return -1;
  }

  auto userthread = (UserThread *)currentThread;

  auto pipe = new Pipe(userthread->process_);

  fildes[0] = pipe->fds_[0];
  fildes[1] = pipe->fds_[1];

  ProcessRegistry::instance()->pipe_vector_lock_.acquire();
  ProcessRegistry::instance()->pipe_vector.push_back(pipe);
  ProcessRegistry::instance()->pipe_vector_lock_.release();

  return 0;
}

void Syscall::reserve_pages(size_t amount)
{
   //uint64 start_vpn = 0x30000000 / PAGE_SIZE;

  for (size_t y = 0; y < amount ; y++)
  {
    PageManager::instance()->allocPPN(PAGE_SIZE);
  }
 /**    for (size_t i = 0; i < amount; i++)
  {
    uint32 ppn = PageManager::instance()->allocPPN(PAGE_SIZE);
    uint64 pdpt_ppn = PageManager::instance()->allocPPN();
    uint64 pd_ppn = PageManager::instance()->allocPPN();
    uint64 pt_ppn = PageManager::instance()->allocPPN();
    ((UserThread *)currentThread)->process_->loader_->arch_memory_.archmemory_lock_.acquire();
    bool page_mapped = currentThread->loader_->arch_memory_.mapPage(start_vpn + i * PAGE_SIZE, pdpt_ppn, pd_ppn, pt_ppn, ppn, 1);
    ((UserThread *)currentThread)->process_->loader_->arch_memory_.archmemory_lock_.release();

    ppn = ppn;
    page_mapped = page_mapped;
  }**/
}

size_t Syscall::brk(size_t adress)
{
  auto userthread = (UserThread *)currentThread;
  auto process = userthread->process_;
  size_t unmodified_programm_break = userthread->process_->current_program_break;

  if (adress == userthread->process_->current_program_break)
  {
    return 0;
  }

  if (adress < userthread->process_->initial_program_break)
  {
    return -1;
  }

  else if (adress > userthread->process_->current_program_break)
  {
    while (adress > userthread->process_->current_program_break)
    {
      if (userthread->loader_->arch_memory_.checkAddressValid(userthread->process_->current_program_break))
      {
        process->current_program_break = unmodified_programm_break;
        return -1;
      }

      userthread->process_->current_program_break += PAGE_SIZE;
    }
  }
  else if (adress < userthread->process_->current_program_break)
  {
    ProcessRegistry::instance()->ipt_lock_.acquire();
    process->loader_->arch_memory_.archmemory_lock_.acquire();
    while (adress < userthread->process_->current_program_break)
    {

      userthread->process_->current_program_break -= 4096;
      auto mapping = userthread->loader_->arch_memory_.resolveMapping(userthread->process_->current_program_break / PAGE_SIZE);
      if (mapping.pt && mapping.pt[mapping.pti].present)
      {

        currentThread->loader_->arch_memory_.unmapPage(((size_t)userthread->process_->current_program_break / PAGE_SIZE));
        debug(USERPROCESS, "%p\n", userthread->process_->current_program_break / PAGE_SIZE);
      }
    }
    process->loader_->arch_memory_.archmemory_lock_.release();
    ProcessRegistry::instance()->ipt_lock_.release();
  }

  return 0;
}

size_t Syscall::sbrk(int increment)
{
  auto userthread = (UserThread *)currentThread;
  auto process = userthread->process_;
  process->program_break_lock.acquire();
  auto program_break = userthread->process_->current_program_break;
  auto initial_program_break = userthread->process_->initial_program_break;

  if (increment == 0)
  {
    process->program_break_lock.release();
    return program_break;
  }

  if (program_break + increment < initial_program_break)
  {
    process->program_break_lock.release();
    return -1;
  }
  else
  {
    brk(program_break + increment);
    process->program_break_lock.release();
    return program_break;
  }

  return 0;
}

size_t Syscall::shm_open(char *name, int oflag, mode_t mode)
{

  if ((size_t)name >= USER_BREAK || (size_t)name == NULL)
  {
    return -1;
  }

  if ((oflag & O_RDONLY) && (oflag & O_RDWR))
  {
    return -1;
  }

  if ((oflag & O_RDONLY) && (oflag & O_TRUNC))
  {
    return -1;
  }

  auto userthread = (UserThread *)currentThread;

  size_t len = strlen(name);
  char buffer[len + 1];
  memcpy(buffer, name, len);
  buffer[len] = '\0';
  ProcessRegistry::instance()->shm_vector_lock.acquire();
  auto shm_object = ProcessRegistry::instance()->getShmObjByName(buffer);

  if (shm_object != NULL)
  {

    if ((oflag & O_CREAT) && (oflag & O_EXCL))
    {
      ProcessRegistry::instance()->shm_vector_lock.release();
      return -1;
    }

    if ((oflag & O_TRUNC) && (oflag & O_RDWR))
    {
      shm_object->size = 0;
    }

    shm_object->addReference(userthread->process_);
    size_t fd = shm_object->generateFd();
    ProcessRegistry::instance()->shm_vector_lock.release();
    return fd;
  }

  if (!(oflag & O_CREAT))
  {
    ProcessRegistry::instance()->shm_vector_lock.release();
    return -1;
  }

  shm_object = new ShmObject(name, mode);
  shm_object->addReference(userthread->process_);
  ProcessRegistry::instance()->shm_objects.push_back(shm_object);

  size_t fd = shm_object->generateFd();
  ProcessRegistry::instance()->shm_vector_lock.release();
  return fd;
}
size_t Syscall::mmap(size_t addr, size_t len, int prot, unsigned int flags, int fildes, off_t off)
{
  debug(USERPROCESS, "%d %d %d %d %d %d %d", addr, len, prot, flags, fildes, off);
  auto userthread = (UserThread *)currentThread;

  if (len == 0)
  {
    return -1;
  }

  /**  if(!userthread->process_->checkPermissionbitsMmap(prot))
   {
     return -1;
   }**/
  if (off < 0)
  {
    return -1;
  }

  if (((flags & MAP_PRIVATE) == 0) && ((flags & MAP_SHARED) == 0))
  {
    return -1;
  }

  if ((flags == MAP_PRIVATE || flags == MAP_SHARED) && fildes == -1)
  {
    return -1;
  }

  userthread->process_->shared_memory_break_lock.acquire();
  size_t addr_to_return = userthread->process_->shared_memory_break;
  ProcessRegistry::instance()->shm_vector_lock.acquire();

  switch (flags)
  {
  case (MAP_ANONYMOUS | MAP_PRIVATE):
  {

    userthread->process_->reserveSpaceForMmapCall(len);
    MemoryMappedRegion *mapped_region = new MemoryMappedRegion{addr_to_return, userthread->process_->shared_memory_break, prot, 0, off};
    userthread->process_->memory_mapped_regions.push_back(mapped_region);
    ProcessRegistry::instance()->shm_vector_lock.release();
    userthread->process_->shared_memory_break_lock.release();
    return addr_to_return;
  }

  case (MAP_PRIVATE):
  {

    userthread->process_->reserveSpaceForMmapCall(len);
    MemoryMappedRegion *mapped_region = new MemoryMappedRegion{addr_to_return, userthread->process_->shared_memory_break, prot, fildes, off};
    userthread->process_->memory_mapped_regions.push_back(mapped_region);
    ProcessRegistry::instance()->shm_vector_lock.release();
    userthread->process_->shared_memory_break_lock.release();
    return addr_to_return;
  }

  case (MAP_ANONYMOUS | MAP_SHARED):
  {
    auto shm_object = new ShmObject(Anonymous);
    userthread->process_->reserveSpaceForMmapCall(len);
    MemoryMappedRegion *mapped_region = new MemoryMappedRegion{addr_to_return, userthread->process_->shared_memory_break, prot, 0, off};
    ustl::vector<MemoryMappedRegion *> memory_mapped_regions;
    memory_mapped_regions.push_back(mapped_region);
    shm_object->memory_mappings.insert({userthread->process_, memory_mapped_regions});

    ProcessRegistry::instance()->shm_objects.push_back(shm_object);

    ProcessRegistry::instance()->shm_vector_lock.release();
    userthread->process_->shared_memory_break_lock.release();

    return addr_to_return;
  }
  case (MAP_SHARED):
  {

    auto shm_object = ProcessRegistry::instance()->getShmObjByFd(fildes);
    // assert(shm_object != NULL);

    if (shm_object != NULL)
    {

      userthread->process_->reserveSpaceForMmapCall(len);
      MemoryMappedRegion *mapped_region = new MemoryMappedRegion{addr_to_return, userthread->process_->shared_memory_break, prot, fildes, off};

      if (shm_object->hasReference(userthread->process_))
      {
        shm_object->addMemoryMapping(userthread->process_, mapped_region);
      }
      else
      {
        ustl::vector<MemoryMappedRegion *> memory_mapped_regions;
        memory_mapped_regions.push_back(mapped_region);
        shm_object->memory_mappings.insert({userthread->process_, memory_mapped_regions});
      }
    }
    else
    {
      size_t fd = userthread->process_->getGlobalFdNum(fildes);

      assert((int)fd != -1);

      auto fd_object = VfsSyscall::getFileDescriptor(fd);
      userthread->process_->reserveSpaceForMmapCall(len);
      shm_object = ProcessRegistry::instance()->getShmObjByInode(fd_object->getFile()->getInode());
      if (shm_object != NULL)
      {
        MemoryMappedRegion *mapped_region = new MemoryMappedRegion{addr_to_return, userthread->process_->shared_memory_break, prot, fildes, off};

        if (shm_object->hasReference(userthread->process_))
        {
          shm_object->addMemoryMapping(userthread->process_, mapped_region);
        }
        else
        {
          ustl::vector<MemoryMappedRegion *> memory_mapped_regions;
          memory_mapped_regions.push_back(mapped_region);
          shm_object->memory_mappings.insert({userthread->process_, memory_mapped_regions});
        }
        shm_object->fds_.push_back(fildes);
      }

      else
      {

        auto shm_object = new ShmObject(fd_object->getFile()->getInode());
        MemoryMappedRegion *mapped_region = new MemoryMappedRegion{addr_to_return, userthread->process_->shared_memory_break, prot, fildes, off};
        ustl::vector<MemoryMappedRegion *> memory_mapped_regions;
        memory_mapped_regions.push_back(mapped_region);
        shm_object->memory_mappings.insert({userthread->process_, memory_mapped_regions});
        ProcessRegistry::instance()->shm_objects.push_back(shm_object);
        shm_object->fds_.push_back(fildes);
      }
    }
    ProcessRegistry::instance()->shm_vector_lock.release();
    userthread->process_->shared_memory_break_lock.release();

    return addr_to_return;
  }

  default:
    assert(false);
    break;
  }

  assert(false);

  return 0;
}

size_t Syscall::munmap(size_t adress, size_t len)
{
  auto userthread = (UserThread *)currentThread;
  if (len == 0 || adress >= USER_BREAK || adress + len >= USER_BREAK)
  {
    return -1;
  }

  ProcessRegistry::instance()->shm_vector_lock.acquire();
  ShmObject *shm_object = ProcessRegistry::instance()->getShmObjByAdress(userthread->process_, adress);

  if (shm_object != NULL)
  {
    for (size_t size = 0; size < len; size += PAGE_SIZE)
    {
      ProcessRegistry::instance()->ipt_lock_.acquire();
      userthread->loader_->arch_memory_.archmemory_lock_.acquire();
      if (userthread->loader_->arch_memory_.checkAddressValid(adress + size))
      {
        shm_object->performWriteback(userthread->process_, adress + size);
        userthread->loader_->arch_memory_.unmapPage((adress + size) / PAGE_SIZE);
      }
      userthread->loader_->arch_memory_.archmemory_lock_.release();
      ProcessRegistry::instance()->ipt_lock_.release();
      shm_object->removeMemoryMapping(userthread->process_,adress + size);
    
  }
  }

  for (size_t size = 0; size < len; size += PAGE_SIZE)
  {
    userthread->process_->removeMemoryMappedRegion(adress+size); 
     ProcessRegistry::instance()->ipt_lock_.acquire();
     userthread->loader_->arch_memory_.archmemory_lock_.acquire();
    if(userthread->loader_->arch_memory_.checkAddressValid(adress + size))
    {
      userthread->loader_->arch_memory_.unmapPage((adress + size) / PAGE_SIZE);
    }
    userthread->loader_->arch_memory_.archmemory_lock_.release();
     ProcessRegistry::instance()->ipt_lock_.release();
    
  }
  ProcessRegistry::instance()->shm_vector_lock.release();

  return 0;
}

size_t Syscall::shm_unlink(char *name)
{
  if ((size_t)name >= USER_BREAK || (size_t)name == NULL)
  {
    return -1;
  }

  size_t len = strlen(name);
  char *buffer = new char[len + 1];
  memcpy(buffer, name, len);
  buffer[len] = '\0';

  ProcessRegistry::instance()->shm_vector_lock.acquire();

  auto shm_obj = ProcessRegistry::instance()->getShmObjByName(buffer);

  if (shm_obj == NULL)
  {
    ProcessRegistry::instance()->shm_vector_lock.release();
    return -1;
  }
  else
  {
    shm_obj->name_ = NULL;
  }
  ProcessRegistry::instance()->shm_vector_lock.release();
  delete[] buffer;

  return 0;
}

size_t Syscall::lseek(int fildes, off_t offset, int whence)
{
  size_t fd = ((UserThread *)currentThread)->process_->getGlobalFdNum(fildes);
  if ((int)fd == -1)
  {
    return -1;
  }

  if (whence != SEEK_SET && whence != SEEK_END && whence != SEEK_CUR)
  {
    return -1;
  }

  if (offset < 0)
  {
    return -1;
  }

  if (!VfsSyscall::lseek(fd, offset, whence))
  {
    return -1;
  }
  return 0;
}

size_t Syscall::mprotect(size_t addr, size_t len, int prot)
{
  size_t size = 0;
  auto userthread = (UserThread *)currentThread;

  if (addr >= USER_BREAK || len == 0 || addr + len >= USER_BREAK)
  {
    return -1;
  }

  /**if(!userthread->process_->checkPermissionbitsMmap(prot))
  {
    return -1;
  }**/

  userthread->loader_->arch_memory_.archmemory_lock_.acquire();
  ustl::vector<size_t> vpns;
  while (size < len)
  {

    if (userthread->loader_->arch_memory_.checkAddressValid(addr + size))
    {
      vpns.push_back((addr + size) / PAGE_SIZE);
      debug(THREAD, "%p\n", (addr + size) / PAGE_SIZE);
    }
    else
    {
      userthread->loader_->arch_memory_.archmemory_lock_.release();
      return -1;
    }

    size += 4096;
  }
  for (auto vpn : vpns)
  {
    auto mapping = userthread->loader_->arch_memory_.resolveMapping(vpn);
    PageTableEntry *pt_entry = &mapping.pt[mapping.pti];
    userthread->process_->setPermissionbitsMmap(pt_entry, prot, pt_entry->shm);
  }

  userthread->loader_->arch_memory_.archmemory_lock_.release();

  return 0;
}

size_t Syscall::swap_page_out(size_t vpn)
{
  auto userthread = (UserThread *)currentThread;
  auto process = userthread->process_;

  Scheduler::instance()->swap_thread_.swap_request_mutex.acquire();
  Request *new_request = new Request;

  //Mutex *Swap_Request_Condvariable_Lock = new Mutex("Swap_Request_Mutex");
  Condition *Swap_Request_Condvariable = new Condition(&Scheduler::instance()->swap_thread_.swap_request_mutex, "Swap_Request_Mutex");
  //Swap_Request_Condvariable_Lock->acquire();

  new_request->type_ = SWAPOUT;
  new_request->vpn_ = vpn;
  new_request->Swap_Request_Condvariable_ = Swap_Request_Condvariable;
  //new_request->Swap_Request_Condvariable_Lock_ = Swap_Request_Condvariable_Lock;
  new_request->process = process;
  new_request->now_free_ppn_ = 0;
  new_request->finished=false;

  
  Scheduler::instance()->swap_thread_.Swap_Request_List.push_back(new_request);
  Scheduler::instance()->swap_thread_.cond_swap_thread.signal();
  while (!new_request->finished)
  {
    Swap_Request_Condvariable->wait();
  }

  Scheduler::instance()->swap_thread_.swap_request_mutex.release();

  PageManager::instance()->freePPN(new_request->now_free_ppn_);

  return new_request->now_free_ppn_;
}

size_t Syscall::swap_page_in(size_t vpn)
{
  auto userthread = (UserThread *)currentThread;
  auto process = userthread->process_;

  Request* new_request = new Request;

  //Mutex *Swap_Request_Condvariable_Lock = new Mutex("Swap_Request_Mutex");
  Condition* Swap_Request_Condvariable = new Condition(&Scheduler::instance()->swap_thread_.swap_request_mutex, "Swap_Request_Mutex");
 
  Scheduler::instance()->swap_thread_.swap_request_mutex.acquire();
  new_request->type_ = SWAPIN;
  new_request->vpn_ = vpn;
  new_request->Swap_Request_Condvariable_ = Swap_Request_Condvariable;
  //new_request->Swap_Request_Condvariable_Lock_ = Swap_Request_Condvariable_Lock;
  new_request->process = process;
  new_request->now_free_ppn_ = 0;
  new_request->finished=false;
  new_request->ppn_for_swap_in= PageManager::instance()->allocPPN();


  Scheduler::instance()->swap_thread_.Swap_Request_List.push_back(new_request);
  Scheduler::instance()->swap_thread_.cond_swap_thread.signal();
 
  while (!new_request->finished)
  {
    Swap_Request_Condvariable->wait();
  }
  Scheduler::instance()->swap_thread_.swap_request_mutex.release();

  return new_request->now_free_ppn_;
}
