#include "SwapThread.h"
#include "Scheduler.h"
#include "PageManager.h"
#include "ProcessRegistry.h"
#include "ArchCommon.h"
#include "debug.h"

#define MAX_ATTEMPS 10000
#define SEED 3
#define CODE_END 0x9000000 // is ne einfache annahme hoffentlich reicht das

int swapped = 0;

SwapThread::SwapThread() : Thread(0, "SwapThread", Thread::KERNEL_THREAD), swap_request_mutex("SWAPTHREAD::swap_request_mutex"), cond_swap_thread(&swap_request_mutex, "cond_swap_thread"), random_pra_seed(SEED)
{
}

void SwapThread::Run()
{
  Device = BDManager::getInstance()->getDeviceByNumber(3);
  Device->setBlockSize(PAGE_SIZE);
  Swap_Bitmap = new Bitmap(Device->getNumBlocks());

  while (1)
  {
    // put to sleep
    // Scheduler::instance()->sleep();
    // Scheduler::instance()->yield();
    // woken up
    swap_request_mutex.acquire();
    while (Swap_Request_List.size() == 0)
    {
      cond_swap_thread.wait();
    }

    while (Swap_Request_List.size() != 0)
    {
      Request *Current_Request = Swap_Request_List.front();
      Swap_Request_List.erase(Swap_Request_List.begin());

      if (Current_Request->type_ == SWAPOUT)
      {
        swapOut(Current_Request);
      }
      else if (Current_Request->type_ == SWAPIN)
      {
        swapIn(Current_Request);
      }

      // wake up thread waiting on the swap to happen
      // Current_Request.swap_request_condvariable->broadcast();

      // Current_Request->Swap_Request_Condvariable_Lock_->acquire();
      Current_Request->finished = true;
      Current_Request->Swap_Request_Condvariable_->signal();
      // Current_Request->Swap_Request_Condvariable_Lock_->release();
    }
    swap_request_mutex.release();
  }
}

void SwapThread::swapOut(Request *request)
{
  // debug(SWAP_OUT, "Swap_Out start\n");
  request->type_ = request->type_;

  size_t free_snp = Swap_Bitmap->get_Free_Bit();
  Swap_Bitmap->setBit(free_snp);
  // debug(SWAP_OUT, "Free snp %d\n", free_snp);

  ProcessRegistry::instance()->ipt_lock_.acquire();
  debug(SWAP_OUT, "size ipt: %d\n", ProcessRegistry::instance()->getsizeIpt());

  size_t IPT_index = 0;

  if (request->vpn_ == 0)
  {
    // assert(ProcessRegistry::instance()->ppns_second_chance.size()>0);
    // IPT_index = ProcessRegistry::instance()->ppns_second_chance.at(0);
    //  ProcessRegistry::instance()->ppns_second_chance.pop_front();
    IPT_index = randomPRA();
  }
  else
  {
    bool found = false;
    for (size_t i = 0; i < 2018; i++)
    {
      InvertedPageTableEntry &entry = ProcessRegistry::instance()->inverted_page_table[i];
      for (auto tuble : entry.tuples)
      {
        if ((size_t)tuble.vpn == request->vpn_ && tuble.type == PAGE)
        {
          IPT_index = i;
          debug(SWAP_OUT, "Sinlge Page Swap triggered for page %d, and vpn %ld\n", i, request->vpn_);
          found = true;
        }
      }
      if (found)
      {
        break;
      }
    }
    if (found == false)
    {
      debug(SWAP_OUT, "Error: no Ipt entry found for vpn: %ln\n", request->vpn_);
    }
  }

  // debug(SWAP_OUT, "Swapout_IPT_Index %d\n", IPT_index);
  size_t vpns_on_page_total = 0;
  size_t vpns_on_page = 0;
  bool page_stored = false;
  bool never_dirty = false;
  bool cow_pages_present = false;
  char *Page_to_swap_out = (char *)request->process->loader_->arch_memory_.getIdentAddressOfPPN((uint64)IPT_index);
  InvertedPageTableEntry Ipt_Entry = ProcessRegistry::instance()->inverted_page_table[IPT_index];
  assert(Ipt_Entry.tuples.size() != 0);
  for (auto IPT_E = Ipt_Entry.tuples.begin(); IPT_E != Ipt_Entry.tuples.end(); IPT_E++)
  {
    if (IPT_E->process == NULL || IPT_E->process->loader_ == NULL)
    {
      debug(SWAP_OUT, "ppn: %d\n", IPT_index);
      ProcessRegistry::instance()->printIPT();
    }
    assert(IPT_E->process != NULL && IPT_E->process->loader_ != NULL);
    IPT_E->process->loader_->arch_memory_.archmemory_lock_.acquire();
    auto mapping = IPT_E->process->loader_->arch_memory_.resolveMapping(IPT_E->vpn);
    assert(mapping.pt_ppn != 0);
    auto mapping_entry = &mapping.pt[mapping.pti];
    if (mapping_entry->cow == 1)
    {
      cow_pages_present = true;
    }
    vpns_on_page_total++;
    IPT_E->process->loader_->arch_memory_.archmemory_lock_.release();
  }

  for (auto tuble = Ipt_Entry.tuples.begin(); tuble != Ipt_Entry.tuples.end(); tuble++)
  {
    assert(tuble->process != NULL && tuble->process->loader_ != NULL);
    // debug(SWAP_OUT, "process swaps out %d\n", IPT_E->process->pid_);
    tuble->process->loader_->arch_memory_.archmemory_lock_.acquire();
    auto mapping = tuble->process->loader_->arch_memory_.resolveMapping(tuble->vpn);
    // debug(SWAP_OUT, "process swaps out %p\n", IPT_E->process->loader_);
    assert(mapping.pt_ppn != 0);

    auto mapping_entry = &mapping.pt[mapping.pti];
    mapping_entry->swapped = 1;
    mapping_entry->present = 0;

    if (mapping_entry->stored == 0 && mapping_entry->dirty == 0)
    {
      // assert(Ipt_Entry.tuples.size() == 1);
      never_dirty = true;
      mapping_entry->swapped = 0;
      mapping_entry->present = 1;
      mapping_entry->writeable = 1;
      mapping_entry->cow = 0;
      request->action = UNMAP_FREE;
    }
    else if (mapping_entry->stored == 1 && mapping_entry->dirty == 0)
    {
      if (cow_pages_present)
      {
       // debug(SWAP_OUT, "Non dirty cow pages!\n");
      }
      vpns_on_page++;
      //debug(SWAP_OUT, "vpns total pages %d, vpns on pages %d\n", vpns_on_page_total, vpns_on_page);
      //debug(SWAP_OUT, "spn is %ld for ipt entry %d\n", Ipt_Entry.spn, IPT_index);
      assert(Swap_Bitmap->getBit(Ipt_Entry.spn) == 1 && "SPN not set!\n");
      page_stored = true;
      mapping_entry->page_ppn = Ipt_Entry.spn;
    }
    else if (mapping_entry->stored == 1 && mapping_entry->dirty == 1)
    {
      //debug(SWAP_OUT, "spn is %ld for ipt entry %d and entry is not stored therefore swap out!\n", Ipt_Entry.spn, IPT_index);
      assert(Swap_Bitmap->getBit(Ipt_Entry.spn) == 1 && "SPN not set!\n");
      assert(Ipt_Entry.spn <= Swap_Bitmap->getSize() && "invalid spn!\n");
      Swap_Bitmap->unsetBit(Ipt_Entry.spn);

      for (auto it = swap_Table_.Table.begin(); it != swap_Table_.Table.end(); ++it)
      {
        if (it->spn_ == Ipt_Entry.spn)
        {
          swap_Table_.Table.erase(it);
         // debug(SWAP_OUT, "Ereased bd entry with spn %ld, because of dirty bit!\n", Ipt_Entry.spn);
          break;
        }
      }
      mapping_entry->stored = 0;
      mapping_entry->page_ppn = free_snp;
    }
    else
    {
      mapping_entry->page_ppn = free_snp;
    }
    tuble->process->loader_->arch_memory_.archmemory_lock_.release();
  }

 // debug(SWAP_OUT, "vpns total pages %d, vpns on pages %d\n", vpns_on_page_total, vpns_on_page);

  if (page_stored == true || never_dirty == true)
  {
    if (page_stored == true)
    {
      assert(vpns_on_page == vpns_on_page);
      debug(SWAP_OUT, "ppn %d is clean and stored and will not be swapped out but only freed!\n", IPT_index);
    }
    else
    {
      debug(SWAP_OUT, "ppn %d will not be swapped because it was never dirty!\n", IPT_index);
    }
    assert(Swap_Bitmap->getBit(free_snp) == 1 && "Bit should be set!\n");
    Swap_Bitmap->unsetBit(free_snp);
  }
  else
  {
    SwapEntry Spn_entry;
    Spn_entry.spn_ = free_snp;
    Spn_entry.IPT_Entry_ = Ipt_Entry;
    swap_Table_.Table.push_back(Spn_entry);
    // debug(SWAP_OUT, "First byte of swapped-in page (vpn %p): %c\n", (uint64)Page_to_swap_out / PAGE_SIZE, (char)Page_to_swap_out[100]);

    auto size_written = Device->writeData(free_snp * PAGE_SIZE, PAGE_SIZE, (char *)Page_to_swap_out);
    memset(Page_to_swap_out, 0xFF, PAGE_SIZE);
    assert(size_written != -1 && "Writeing to Device Failed");
    debug(SWAP_OUT, "Page: %d saved to block device!\n", IPT_index);
  }

  if (never_dirty == false)
  {
    ProcessRegistry::instance()->deleteIPTEntry(IPT_index);
    assert(ProcessRegistry::instance()->inverted_page_table[IPT_index].tuples.size() == 0 && "ipt not cleared!");
  }

  // debug(SWAP_OUT, "Page: %d freed!\n", IPT_index);
  request->now_free_ppn_ = IPT_index;

  ProcessRegistry::instance()->ipt_lock_.release();
}

bool SwapThread::swappablePageCheck(uint64 index)
{
  bool is_safe_to_swap = false;
  auto &entry = ProcessRegistry::instance()->inverted_page_table[index];
  if (entry.tuples.size() > 0 && entry.tuples.front().type == PAGE)
  {
    is_safe_to_swap = true;
  }
  return is_safe_to_swap;
}

size_t SwapThread::randomPRA()
{
  size_t ipt_start_index = 1000;

  size_t ipt_size = 2018;
  size_t valid_range_size = ipt_size - ipt_start_index;

  assert(ProcessRegistry::instance()->getsizeIpt() > 0 && "IPT size is null!");

  for (size_t attemps = 0; attemps < MAX_ATTEMPS; attemps++)
  {
    random_pra_seed = (random_pra_seed * 1103515245 + 12345) % 0x7FFFFFFF;
    size_t random_offset = random_pra_seed % valid_range_size;
    size_t index = ipt_start_index + random_offset;

    if (swappablePageCheck(index) == true)
    {
      return index;
    }
  }
  // ProcessRegistry::instance()->printIPT();
  assert(false && "Could not find anything");
  return 0;

  // size_t half_ipt_size = 900;

  // while (1)
  // {
  //   uint32 hi, lo;
  //   asm volatile("rdtsc" : "=a"(lo), "=d"(hi));
  //   size_t currentTime = (size_t)hi << 32 | lo;

  //   size_t random_ipt_entry = currentTime % half_ipt_size;
  //   size_t index = half_ipt_size + random_ipt_entry;

  //   // if possible_rnty is swapbable return else try again
  //   // for now lets just assume every entry is swappable
  //   auto possible_entry = ProcessRegistry::instance()->inverted_page_table[index];

  //   if (possible_entry.tuples.size() > 0 && possible_entry.tuples[0].type == PAGE)
  //   {
  //     return index;
  //   }

  //   // if possible_rnty is swapbable return else try again
  // }
  // assert("should never get here");
}

void SwapThread::swapIn(Request *request)
{
  //debug(SWAP_IN, "SWAP in started for vpn %ld\n", request->vpn_);

  size_t spn = 0;
  SwapEntry *swap_entry = nullptr;

  request->process->loader_->arch_memory_.archmemory_lock_.acquire();
  auto mapping = request->process->loader_->arch_memory_.resolveMapping(request->vpn_);
  assert(mapping.pt != NULL && "mapping does not exist!");
  if (mapping.pt[mapping.pti].swapped == 1 && mapping.pt[mapping.pti].present == 0)
  {
    spn = mapping.pt[mapping.pti].page_ppn;
    //debug(SWAP_IN, "Found spn %zu in swap page table for vpn %ld\n", spn, request->vpn_);
    swap_entry = swap_Table_.getEntry(spn);
    assert(swap_entry != NULL);
  }
  else
  {
    PageManager::instance()->freePPN(request->ppn_for_swap_in);
    debug(SWAP_IN, "Error: no spn found for vpn %ld\n", request->vpn_);
    request->process->loader_->arch_memory_.archmemory_lock_.release();
    return;
  }
  request->process->loader_->arch_memory_.archmemory_lock_.release();

  uint64 ppn = request->ppn_for_swap_in;
  if (ppn == 0)
  {
    debug(SWAP_IN, "Error: Failed to allocate page\n");
    return;
  }

  char *page_data = (char *)request->process->loader_->arch_memory_.getIdentAddressOfPPN(ppn);
  auto size_read = Device->readData(spn * PAGE_SIZE, PAGE_SIZE, page_data);
  debug(SWAP_IN, "First byte of swapped-in page (vpn %ld): %c\n", request->vpn_, (char)page_data[100]);

  assert(size_read != -1 && "Reading from Device Failed");
  debug(SWAP_IN, "Read page data from swap device from spn %zu\n", spn);

  ProcessRegistry::instance()->ipt_lock_.acquire();
  InvertedPageTableEntry &ipt_entry = ProcessRegistry::instance()->inverted_page_table[ppn];
  assert(ipt_entry.tuples.size() == 0);
  ipt_entry = swap_entry->IPT_Entry_;

  if (ipt_entry.tuples.size() == 0)
  {
    debug(SWAP_OUT, "process %d wants swap in\n", request->process->pid_);
    debug(SWAP_OUT, "Error on ppn %d\n", ppn);
    ProcessRegistry::instance()->printIPT();
    assert(false);
  }

  for (auto &tuple : ipt_entry.tuples)
  {
    if (tuple.process == NULL || tuple.process->loader_ == NULL)
    {
      debug(SWAP_OUT, "Error on ppn: %d\n", ppn);
      ProcessRegistry::instance()->printIPT();
    }
    assert(tuple.process != NULL && tuple.process->loader_ != NULL);
    tuple.process->loader_->arch_memory_.archmemory_lock_.acquire();
    auto tuple_mapping = tuple.process->loader_->arch_memory_.resolveMapping(tuple.vpn);
    assert(tuple_mapping.pt);

    // ProcessRegistry::instance()->process_vector_lock_.acquire();
    // bool found = false;
    // for (auto element : ProcessRegistry::instance()->processes_vector)
    // {
    //   if (element->pid_ == tuple.process->pid_)
    //   {
    //     found = true;
    //   }
    // }
    // if (found == false)
    // {
    //   ProcessRegistry::instance()->printIPT();
    //   debug(SWAP_OUT, "Error on ppn: %d\n", ppn);
    //   debug(SWAP_OUT, "process: %zu  %d\n", tuple.process, tuple.process->pid_);
    //   assert(found);
    // }
    // ProcessRegistry::instance()->process_vector_lock_.release();


    if (tuple_mapping.pt)
    {
      debug(SWAP_IN, "changed tuple mapping for process %d !\n", tuple.process->pid_);
      auto page_table_entry = &tuple_mapping.pt[tuple_mapping.pti];
      page_table_entry->swapped = 0;
      page_table_entry->present = 1;
      page_table_entry->dirty = 0;
      page_table_entry->stored = 1;
      page_table_entry->page_ppn = ppn;
    }
    tuple.process->loader_->arch_memory_.archmemory_lock_.release();
    size_t bitmap_size = Scheduler::instance()->swap_thread_.Swap_Bitmap->getSize();
    assert(spn < bitmap_size && "Invalid spn!\n");
    debug(SWAP_IN, "spn is %ld for ipt entry %d\n", spn, ppn);

    ipt_entry.spn = spn;
  }

  //debug(SWAP_IN, "Succesfull Page swapped in form spn %zu to ppn %zu\n", spn, ppn);

  ProcessRegistry::instance()->ipt_lock_.release();

  return;
}

size_t SwapThread::NFU_PRA()
{

  size_t min_index = 0;
  for (size_t i = 0; i < 2016; ++i)
  {
    auto &ipt_entry = ProcessRegistry::instance()->inverted_page_table[i];
    if (ipt_entry.refcount_nfu != 0)
    {
      for (auto tuple : ipt_entry.tuples)
      {
        if (tuple.type == PAGE)
        {
          min_index = i;
          break;
        }
      }
    }
  }

  for (size_t i = min_index + 1; i < 2016; i++)
  {
    auto ipt_entry = ProcessRegistry::instance()->inverted_page_table[i];

    if (ipt_entry.refcount_nfu != 0 && ipt_entry.refcount_nfu < ProcessRegistry::instance()->inverted_page_table[min_index].refcount_nfu)
    {
      for (auto tuple : ipt_entry.tuples)
      {
        if (tuple.type == PAGE)
        {
          min_index = i;
        }
      }
    }
  }

  ProcessRegistry::instance()->inverted_page_table[min_index].refcount_nfu = 0;

  assert(min_index != 0);

  return min_index;
}

size_t SwapThread::SecondChancePRA()
{

  auto &ppn_list = ProcessRegistry::instance()->ppns_second_chance;
  auto it = ppn_list.begin();
  while (it != ppn_list.end())
  {
    size_t ppn = *it;
    auto &ipt_entry = ProcessRegistry::instance()->inverted_page_table[ppn];

    bool accessed_found = false;

    for (auto &tuple : ipt_entry.tuples)
    {
      auto mapping = tuple.process->loader_->arch_memory_.resolveMapping(tuple.vpn);
      auto &pt_entry = mapping.pt[mapping.pti];

      if (pt_entry.accessed)
      {
        pt_entry.accessed = 0;
        accessed_found = true;
        break;
      }
    }

    if (accessed_found)
    {
      it = ppn_list.erase(it);
      ppn_list.push_back(ppn);
    }
    else
    {
      ppn_list.erase(it);
      return ppn;
    }
  }

  assert(false);

  return 0;
}