#include <mm/KernelMemoryManager.h>
#include "ProcessRegistry.h"
#include "Scheduler.h"
#include "UserProcess.h"
#include "kprintf.h"
#include "VfsSyscall.h"
#include "VirtualFileSystem.h"
#include "PageManager.h"
#include "UserProcess.h"
#include "Loader.h"
#include "UserThread.h"
#include "PidManager.h"

ProcessRegistry *ProcessRegistry::instance_ = 0;

ProcessRegistry::ProcessRegistry(FileSystemInfo *root_fs_info, char const *progs[]) : Thread(root_fs_info, "ProcessRegistry", Thread::KERNEL_THREAD), process_vector_lock_("ProcessRegistry::processes vector lock"), ipt_lock_("ProcessRegistry::ipt_lock_"), global_fd_list_lock_("ProcessRegistry::global_fd_list_lock_"), pipe_vector_lock_("Pipe vector lock"), shm_vector_lock("Shm vector lock"), dedup_lock_("dedup"), dedup_wait(&dedup_lock_, "dedup_cond"),
                                                                                      progs_(progs), progs_running_(0),
                                                                                      counter_lock_("ProcessRegistry::counter_lock_"),
                                                                                      all_processes_killed_(&counter_lock_, "ProcessRegistry::all_processes_killed_")
{
  instance_ = this; // instance_ is static! -> Singleton-like behaviour
}

ProcessRegistry::~ProcessRegistry()
{
}

ProcessRegistry *ProcessRegistry::instance()
{
  return instance_;
}

void ProcessRegistry::Run()
{
  if (!progs_ || !progs_[0])
    return;

  debug(PROCESS_REG, "mounting userprog-partition \n");

  debug(PROCESS_REG, "mkdir /usr\n");
  assert(!VfsSyscall::mkdir("/usr", 0));
  debug(PROCESS_REG, "mount idea1\n");
  assert(!VfsSyscall::mount("idea1", "/usr", "minixfs", 0));

  debug(PROCESS_REG, "mkdir /dev\n");
  assert(!VfsSyscall::mkdir("/dev", 0));
  debug(PROCESS_REG, "mount devicefs\n");
  assert(!VfsSyscall::mount(NULL, "/dev", "devicefs", 0));

  KernelMemoryManager::instance()->startTracing();

  for (uint32 i = 0; progs_[i]; i++)
  {
    createProcess(progs_[i]);
  }

  counter_lock_.acquire();

  while (progs_running_)
    all_processes_killed_.wait();

  counter_lock_.release();

  debug(PROCESS_REG, "unmounting userprog-partition because all processes terminated \n");

  VfsSyscall::umount("/usr", 0);
  VfsSyscall::umount("/dev", 0);
  vfs.rootUmount();

  Scheduler::instance()->printStackTraces();
  Scheduler::instance()->printThreadList();

  PageManager *pm = PageManager::instance();
  if (!DYNAMIC_KMM && pm->getNumFreePages() != pm->getNumPagesForUser())
  {
    PageManager::instance()->printBitmap();
    debug(PM, "WARNING: You might be leaking physical memory pages somewhere\n");
    debug(PM, "%u/%u free physical pages after unmounting detected\n",
          pm->getNumFreePages(),
          pm->getNumPagesForUser());
  }

  kill();
}

void ProcessRegistry::processExit()
{
  counter_lock_.acquire();

  if (--progs_running_ == 0)
    all_processes_killed_.signal();

  counter_lock_.release();
}

void ProcessRegistry::processStart()
{
  counter_lock_.acquire();
  ++progs_running_;
  counter_lock_.release();
}

size_t ProcessRegistry::processCount()
{
  ScopeLock lock(counter_lock_);
  return progs_running_;
}

void ProcessRegistry::createProcess(const char *path)
{
  debug(PROCESS_REG, "create process %s\n", path);
  UserProcess *process = new UserProcess(PidManager::instance()->generatePid(), path, new FileSystemInfo(*working_dir_));
  process_vector_lock_.acquire();
  processes_vector.push_back(process);
  process_vector_lock_.release();

  UserThread *thread = new UserThread(process, process->file_name_, (size_t)process->loader_->getEntryFunction(), size_t(nullptr), 0, size_t(nullptr));
  process->thread_vector_lock_.acquire();
  process->userthreads_.push_back(thread);
  process->thread_vector_lock_.release();

  debug(PROCESS_REG, "created userprocess %s\n", path);
  process->thread_vector_lock_.acquire();
  Scheduler::instance()->addNewThread((Thread *)thread);
  process->thread_vector_lock_.release();
  debug(PROCESS_REG, "added thread %s\n", path);
}

UserProcess *ProcessRegistry::getProcessfromVector(size_t id)
{
  for (auto process : processes_vector)
  {
    if (id == process->pid_)
    {
      return process;
    }
  }
  return NULL;
}
void ProcessRegistry::removeProcessFromVector(size_t id)
{
  int index = -1;

  for (size_t i = 0; i < processes_vector.size(); i++)
  {
    processes_vector.at(i)->thread_vector_lock_.acquire();
    for (auto &thread : processes_vector.at(i)->userthreads_)
    {
      if (thread->waiting_on_process && thread->waiting_pid == id)
      {
        thread->waiting_on_process = false;
        thread->wait_for_process->signal();
      }
    }
    processes_vector.at(i)->thread_vector_lock_.release();

    if (processes_vector.at(i)->pid_ == id)
    {

      index = i;
    }
  }

  if (index >= 0)
  {
    processes_vector.erase(processes_vector.begin() + index);
  }
}

void ProcessRegistry::addToIPT(uint64_t ppn, size_t vpn, UserProcess *process, PageType type)
{
  assert(ppn <= 2018);
  assert(process != nullptr);
  assert(vpn != NULL);
  
  // checking if entry already exits
  InvertedPageTableEntry entry = ProcessRegistry::instance()->inverted_page_table[ppn];
  for (auto tuble : entry.tuples)
  {
    if (tuble.process->pid_ == process->pid_ && tuble.type == type && tuble.vpn == vpn)
    {
      debug(IPT, "Entry with vpn: %ld, ppn %d, process %d already exists!\n", (size_t)vpn, (int)ppn, (int)process->pid_);
      return;
    }
  }

  vpn_pid_tuple tuble;
  tuble.vpn = vpn;
  tuble.process = process;
  tuble.type = type;


  inverted_page_table[ppn].tuples.push_back(tuble);

  /** if(inverted_page_table[ppn].refcount_nfu == 0)
   {
     //inverted_page_table[ppn].refcount_nfu = 1;
   }**/

  debug(IPT, "added to IPT ppn: %ld the vpn: %p by Process: %ld\n", ppn, vpn, process->pid_);
}

// void ProcessRegistry::addSPNToTuble(uint64_t ppn, size_t vpn, UserProcess *process, size_t spn)
// {
//   assert(ppn <= 2018);
//   assert(process != nullptr);
//   assert(vpn != NULL);
//   size_t bitmap_size = Scheduler::instance()->swap_thread_.Swap_Bitmap->getSize();
//   assert(spn < bitmap_size && "Invalid spn!\n");

//   auto &tuple_vec = inverted_page_table[ppn].tuples;
//   auto it = tuple_vec.end();
//   for (auto tuple = tuple_vec.begin(); tuple != tuple_vec.end(); ++tuple)
//   {

//     if (tuple->process->pid_ == process->pid_ && tuple->vpn == vpn)
//     {
//       it->spn = spn;
//     }
//   }

// }

void ProcessRegistry::deleteFromIPT(uint64_t ppn, size_t vpn, size_t pid)
{
  assert(ppn <= 2018);
  assert(pid <= __SIZE_MAX__);
  assert(vpn != NULL);

  // if (inverted_page_table[ppn].page_free == true) // es werden pages nur allokiert ohne mapping zb im loader das triggert das
  // {
  //     //assert(inverted_page_table[ppn].page_free != true); // page schon frei
  // }

  auto &tuple_vec = inverted_page_table[ppn].tuples;
  auto it = tuple_vec.end();
  for (auto tuple = tuple_vec.begin(); tuple != tuple_vec.end(); ++tuple)
  {

    if (tuple->process->pid_ == pid && tuple->vpn == vpn)
    {
      // tuple_vec.erase(tuple);
      it = tuple;
      debug(IPT, "UNMAP; erased from IPT ppn: %ld the vpn: %p by Process: %ld\n", ppn, vpn, pid);
      break;
    }

    if ((int)vpn == DECONSTRUCT)
    {
      if (tuple->process->pid_ == pid)
      {
        // tuple_vec.erase(tuple);
        it = tuple;
        debug(IPT, "DECONSTRUCTOR: erased from IPT ppn: %ld by Process: %ld\n", ppn, pid);
        break;
      }
    }
  }
  if (it != tuple_vec.end())
  {
    tuple_vec.erase(it);
  }

  // if (tuple_vec.empty()) // kein process hat mehr eine vpn auf diese ppn gemappt
  // {
  //   inverted_page_table[ppn].refcount = 0;
  //   inverted_page_table[ppn].page_free = true;
  // }
}

void ProcessRegistry::deleteIPTEntry(uint64_t ppn)
{
  assert(ppn <= 2018);

  auto &tuple_vec = inverted_page_table[ppn].tuples;
  tuple_vec.clear();
  debug(IPT, "Cleared IPTentry ppn: %d\n", ppn);
}

void ProcessRegistry::printIPT()
{
  debug(IPT_PRINT, "/////////////////////////IPT OVERVIEW/////////////////////////////////\n");
  for (size_t i = 0; i < 2018; i++)
  {
    auto entry = inverted_page_table[i];
    if (entry.tuples.size() > 0)
    {
      debug(IPT_PRINT, "\n");
      debug(IPT_PRINT, "[PPN: %d]\n", i);

      for (const auto t : entry.tuples)
      {
        if (t.type == PAGE)
        {
          debug(IPT_PRINT, "[PROCESS: %d mapped VPN: %p TYPE: PAGE]\n", t.process->pid_, t.vpn);
        }
        else if (t.type == PT)
        {
          debug(IPT_PRINT, "[PROCESS: %d mapped VPN: %p TYPE: PT]\n", t.process->pid_, t.vpn);
        }
        else if (t.type == PD)
        {
          debug(IPT_PRINT, "[PROCESS: %d mapped VPN: %p TYPE: PD]\n", t.process->pid_, t.vpn);
        }
        else if (t.type == PDPT)
        {
          debug(IPT_PRINT, "[PROCESS: %d mapped VPN: %p TYPE: PDPT]\n", t.process->pid_, t.vpn);
        }
      }
    }
  }
  debug(IPT_PRINT, "//////////////////////////////////////////////////////////////////////\n");
}

Pipe *ProcessRegistry::getPipeFromVector(size_t fd)
{

  for (auto &pipe : pipe_vector)
  {
    if (pipe->fds_[0] == fd)
    {
      return pipe;
    }
    if (pipe->fds_[1] == fd)
    {
      return pipe;
    }
  }

  return NULL;
}

void ProcessRegistry::removePipesFromVectorandDeleteThem(UserProcess *process)
{
  for (auto it = pipe_vector.begin(); it != pipe_vector.end();)
  {
    if ((*it)->has_reference(process->pid_))
    {
      (*it)->remove_refernce_entry(process);
    }

    if ((*it)->references.size() == 0)
    {

      delete *it;
      it = pipe_vector.erase(it);
    }
    else
    {
      ++it;
    }
  }
}

ShmObject *ProcessRegistry::getShmObjByName(char *name)
{

  size_t len = strlen(name);

  for (auto &shm_object : shm_objects)
  {
    if (shm_object->name_ != NULL)
    {
      if (strlen(shm_object->name_) == len && strcmp(name, shm_object->name_) == 0)
      {
        return shm_object;
      }
    }
  }

  return NULL;
}

ShmObject *ProcessRegistry::getShmObjByFd(int fd)
{

  for (auto &shm_object : shm_objects)
  {
    for (auto &fd_from_shm : shm_object->fds_)
    {
      debug(PAGEFAULT, "%d", fd_from_shm);
      if (fd == fd_from_shm)
      {

        return shm_object;
      }
    }
  }
  // assert(false);

  return NULL;
}

ShmObject *ProcessRegistry::getShmObjByAdress(UserProcess *process, size_t adress)
{

  for (auto &shm_object : shm_objects)
  {

    if (shm_object->getMemoryMapping(process, adress))
    {
      return shm_object;
    }
  }

  return NULL;
}

ShmObject *ProcessRegistry::getShmObjByInode(Inode *inode)
{

  for (auto &shm_object : shm_objects)
  {

    if (shm_object->backing_inode == inode)
    {
      return shm_object;
    }
  }

  return NULL;
}

void ProcessRegistry::copyMemoryMappings(UserProcess *process, UserProcess *child_process)
{

  for (auto &shm_object : shm_objects)
  {

    if (shm_object->hasReference(process))
    {
      debug(PAGEFAULT, "has reference");
      auto memory_mappings = shm_object->memory_mappings[process];
      shm_object->memory_mappings.insert({child_process, memory_mappings});
    }
  }
}

void ProcessRegistry::removeReferencesShmObj(UserProcess *process)
{
  for (auto &shm_object : shm_objects)
  {
    shm_object->removeReference(process);
  }
}

void ProcessRegistry::removeShmObj()
{
  for (auto it = shm_objects.begin(); it != shm_objects.end();)
  {
    if ((*it)->memory_mappings.size() == 0)
    {
      debug(PAGEFAULT, "deleted shm object");
      // delete *it;
      // it = shm_objects.erase(it);
      break;
    }
    else
    {
      ++it;
    }
  }
}

size_t ProcessRegistry::getsizeIpt()
{
  size_t counter = 0;
  for (size_t i = 0; i < 2018; i++)
  {
    if (ProcessRegistry::instance()->inverted_page_table[i].tuples.size() > 0)
    {
      counter++;
    }
  }
  return counter;
}
