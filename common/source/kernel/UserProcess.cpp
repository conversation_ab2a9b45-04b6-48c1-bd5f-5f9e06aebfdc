#include "ProcessRegistry.h"
#include "UserProcess.h"
#include "kprintf.h"
#include "Console.h"
#include "Loader.h"
#include "VfsSyscall.h"
#include "File.h"
#include "PageManager.h"
#include "ArchThreads.h"
#include "offsets.h"
#include "Scheduler.h"
#include "UserThread.h"
#include "PidManager.h"

UserProcess::UserProcess(size_t pid, ustl::string filename, FileSystemInfo *fs_info) : pid_(pid),
                                                                                       fd_(VfsSyscall::open(filename, O_RDONLY)),
                                                                                       thread_vector_lock_("Userprocess::thread_vector_lock"),
                                                                                       process_lock_("Userprocess::process_lock_"),
                                                                                       only_one_thread_living(&thread_vector_lock_, "waiting for only one thread living"),
                                                                                       lock_for_exec_flag("Userprocess::exec_lock_flag"),
                                                                                       local_fd_mapping_lock_("Userprocess::local_fds_lock_"),
                                                                                       current_program_break(0x300000000000),
                                                                                       initial_program_break(0x300000000000),
                                                                                       program_break_lock("Userprocess:: program_break_lock"),
                                                                                       shared_memory_break_lock("Userprocess:: shared_memory_break_lock"),
                                                                                       shared_memory_start(0x600000000000),
                                                                                       shared_memory_break(0x600000000000)

                                                                                       
{
  file_name_ = filename;
  fs_info_ = fs_info;
  if (fd_ >= 0)
  {
    this->loader_ = new Loader(fd_);
    this->loader_->arch_memory_.setPid(pid_);
  }

  if (!loader_ || !loader_->loadExecutableAndInitProcess())
  {
    debug(USERPROCESS, "Error: loading %s failed!\n", filename.c_str());
    return;
  }
  ProcessRegistry::instance()->processStart(); // should also be called if you fork a process
}

UserProcess::~UserProcess()
{
  assert(Scheduler::instance()->isCurrentlyCleaningUp());
  
  delete loader_;
  loader_ = 0;

  for (const auto &pair : fd_mapping_)
  {
    VfsSyscall::close(pair.first);//global fd
  }

  delete fs_info_;
  fs_info_ = 0;
  ProcessRegistry::instance()->processExit();
}

UserProcess::UserProcess(UserProcess &other) : fs_info_(new FileSystemInfo(*other.fs_info_)),
                                               pid_(PidManager::instance()->generatePid()),
                                               fd_(VfsSyscall::open(other.file_name_, O_RDONLY)),
                                               file_name_(other.file_name_),
                                               thread_vector_lock_("Userprocess::thread_vector_lock"),
                                               process_lock_("Userprocess::process_lock_"),
                                               latest_tid_(other.latest_tid_),
                                               only_one_thread_living(&thread_vector_lock_, "waiting for only one thread living"),
                                               lock_for_exec_flag("Userprocess:: exec_lock_flag"),
                                               fd_mapping_(other.fd_mapping_),
                                               local_fd_mapping_lock_("Userprocess::local_fds_lock_"),
                                               current_program_break(other.current_program_break),
                                               initial_program_break(other.initial_program_break),
                                               program_break_lock("Userprocess:: program_break_lock"),
                                               shared_memory_break_lock("Userprocess:: shared_memory_break_lock"),
                                               shared_memory_start(other.shared_memory_start),
                                               shared_memory_break(other.shared_memory_break)                                               
{

  

  loader_ = new Loader(*other.loader_, fd_, this);
  loader_->arch_memory_.setPid(pid_);

  ProcessRegistry::instance()->global_fd_list_lock_.acquire();
  for (const auto &pair : fd_mapping_)
  {
    FileDescriptor* global_fd = global_fd_list.getFileDescriptor(pair.first);
    global_fd->refcounter_++;
  }
  ProcessRegistry::instance()->global_fd_list_lock_.release();

  ProcessRegistry::instance()->pipe_vector_lock_.acquire();
  for(auto pipe : ProcessRegistry::instance()->pipe_vector)
  {
    if(pipe->has_reference(other.pid_))
    {
      pipe->insert_reference_entry(this);
    }
  }
  ProcessRegistry::instance()->pipe_vector_lock_.release();

  ProcessRegistry::instance()->shm_vector_lock.acquire();
  ProcessRegistry::instance()->copyMemoryMappings(&other,this);
  ProcessRegistry::instance()->shm_vector_lock.release();




  if (!loader_ || !loader_->loadExecutableAndInitProcess())
  {
    debug(USERPROCESS, "Error: loading %s failed!\n", file_name_.c_str());
    return;
  }
  ProcessRegistry::instance()->processStart();
}

void UserProcess::Run()
{
  debug(USERPROCESS, "Run: Fail-safe kernel panic - you probably have forgotten to set switch_to_userspace_ = 1\n");
  assert(false);
}

UserThread *UserProcess::getThreadfromVector(size_t id)
{
  for (auto& thread : userthreads_)
  {
    if (id == thread->getTID())
    {
      return thread;
    }
  }
  return NULL;
}

void UserProcess::removeThreadFromVector(size_t tid)
{
  int index = -1;

  for (size_t i = 0; i < userthreads_.size(); i++)
  {
     assert(userthreads_.at(i) !=NULL);
   if(userthreads_.at(i)->waiting_thread_id== tid && userthreads_.at(i)->waiting_on_other_thread)
   {
    
     UserThread* thread_to_wake_up= userthreads_.at(i);
     thread_to_wake_up->waiting_on_other_thread=false;
     thread_to_wake_up->wait_for_thread->signal();

    }

    if (userthreads_.at(i)->getTID() == tid)
    {

      index = i;
    }
  }

  if (index >= 0)
  {
    userthreads_.erase(userthreads_.begin() + index);
  }
}

size_t UserProcess::getRetValFromMap(size_t tid)
{

  size_t ret_val = thread_retvals[tid];
  thread_retvals.erase(tid);

  return ret_val;
}

bool UserProcess::checkForDeadlockJoin()
{

  auto userthread= (UserThread*) currentThread;
  for(auto& thread : userthreads_)
  {
    assert(thread != NULL);
    if(currentThread->getTID()== thread->waiting_thread_id && thread->waiting_on_other_thread==true && thread->getTID()== userthread->waiting_thread_id)
    {
    
      thread->waiting_on_other_thread=false;
    
      assert(thread->wait_for_thread !=NULL);
      thread->wait_for_thread->signal(); 
      

      return true;
    }
  }
  return false;
}

size_t UserProcess::mapGlobalFdNum(size_t global_fd_num)
{
  ScopeLock l(local_fd_mapping_lock_);

  if (fd_mapping_.find(global_fd_num) != fd_mapping_.end())
  {
    return -1;
  }

  size_t local_fd_num = global_fd_num * 100;
  fd_mapping_.insert({global_fd_num, local_fd_num});
  return local_fd_num;
}

size_t UserProcess::getGlobalFdNum(size_t local_fd_num)
{
  if (local_fd_num == 0)
    return 0;

  if (local_fd_num == 1)
    return 1;

  ScopeLock l(local_fd_mapping_lock_);
  for (const auto &pair : fd_mapping_)
  {
    if (pair.second == local_fd_num)
    {
      return pair.first;
    }
  }

  return -1;
}

size_t UserProcess::eraseFdMapping(size_t global_fd_num)
{
  ScopeLock l(local_fd_mapping_lock_);

  if (fd_mapping_.find(global_fd_num) != fd_mapping_.end())
  {
    fd_mapping_.erase(global_fd_num);
    return 0;
  }

  return -1;
}

void UserProcess::reserveSpaceForMmapCall(size_t len)
{
  for(size_t size = 0 ; size < len ; size+= PAGE_SIZE)
  {
    
    shared_memory_break += PAGE_SIZE;

  }

}

MemoryMappedRegion* UserProcess::getMemoryMappedRegion(size_t adress)
{
  for(auto&memory_mapped_region : memory_mapped_regions)
  {
    if(adress >= memory_mapped_region->start_adress && adress < memory_mapped_region->end_adress)
    {
      return memory_mapped_region;
    }
  }
  return NULL;

}

void UserProcess::setPermissionbitsMmap(PageTableEntry* pt_entry, int prot, size_t shared)
{
   
   debug(USERPROCESS,"%d",shared);


    switch (prot)
        {
        case PROT_READ:
           pt_entry->writeable=0;
           pt_entry->shm=shared;         
          break;
        case PROT_READ|PROT_WRITE:
           pt_entry->writeable=1;
            pt_entry->shm=shared;
          break;
        case PROT_READ|PROT_EXEC:
           pt_entry->writeable=0;
           pt_entry->execution_disabled=0;
            pt_entry->shm=shared;
          break;
        case PROT_EXEC:
           pt_entry->execution_disabled=0;
           pt_entry->shm=shared;  
           break;  
        case PROT_READ|PROT_WRITE|PROT_EXEC:
           pt_entry->writeable=1;
           pt_entry->execution_disabled=0;
            pt_entry->shm=shared;  
           break;
        case PROT_NONE:
          pt_entry->user_access=0;
          pt_entry->shm=shared; 
           break;     
        default:
          break;
        }
}

void UserProcess::removeMemoryMappedRegion(size_t adress)
{
  for (auto it = memory_mapped_regions.begin(); it != memory_mapped_regions.end(); ++it)
  {
        auto memory_mapped_region = *it;
    if(adress >= memory_mapped_region->start_adress && adress < memory_mapped_region->end_adress)
    {
     if(adress/PAGE_SIZE == memory_mapped_region->start_adress/PAGE_SIZE)
     {
      //debug(LOADER, " removing first page of mapping %p\n",adress);
       memory_mapped_region->start_adress +=4096;
       //debug(LOADER, "  %p  %p\n",memory_mapped_region->end_adress - 4096,memory_mapped_region->start_adress );
       if(adress/PAGE_SIZE == (memory_mapped_region->end_adress - 4096)/PAGE_SIZE && (memory_mapped_region->end_adress )/PAGE_SIZE == memory_mapped_region->start_adress/PAGE_SIZE )
       {
        //debug(LOADER, "no page left remove mapping %p\n",adress);
         memory_mapped_regions.erase(it);
         break;
       }
       break;
     }
     if(adress/PAGE_SIZE == (memory_mapped_region->end_adress - 4096)/PAGE_SIZE && (memory_mapped_region->end_adress - 4096)/PAGE_SIZE != memory_mapped_region->start_adress/PAGE_SIZE )
     {
      //debug(LOADER, " removing last page of mapping %p \n",adress);
       memory_mapped_region->end_adress -= 4096;
       break;
     }

    //debug(LOADER, " removing page somwehere in the middles\n");

    auto split_mapping_block= new MemoryMappedRegion{adress+4096,memory_mapped_region->end_adress,memory_mapped_region->permission_bits,0,memory_mapped_region->offset_};
    //debug(LOADER, " new block starting at : %p and endingt at not inclusive: %p\n", split_mapping_block->start_adress, split_mapping_block->end_adress);
    memory_mapped_region->end_adress = adress;
     //debug(LOADER, " old now ending  not inclusive: %p\n", memory_mapped_region->end_adress);
    memory_mapped_regions.insert(it + 1, split_mapping_block);
     break;
    
    }
  }
    

}

bool UserProcess::checkPermissionbitsMmap(int prot)
{
    switch (prot)
        {
        case PROT_READ:
          return true;      
          break;
        case PROT_READ|PROT_WRITE:
          return true;
          break;
        case PROT_READ|PROT_EXEC:
           return true;
          break;
        case PROT_READ|PROT_WRITE|PROT_EXEC:
           return true;
           break;
        case PROT_EXEC:
          return true;
          break;   
        case PROT_NONE:
         return true;
           break;     
        default:
          break;
        }
        return false;
}



