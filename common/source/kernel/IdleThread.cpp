#include "IdleThread.h"
#include "Scheduler.h"
#include "ArchCommon.h"

IdleThread::IdleThread() : Thread(0, "IdleThread", Thread::KERNEL_THREAD)
{
}

void IdleThread::Run()
{
  uint32 last_ticks = 0;
  uint32 new_ticks = 0;
  while (1)
  {
    new_ticks = Scheduler::instance()->getTicks();
    if (new_ticks == last_ticks)
    {
      last_ticks = new_ticks + 1;
      ArchCommon::idle();
    }
    else
    {
      last_ticks = new_ticks;
      Scheduler::instance()->yield();
    }
  }
}
