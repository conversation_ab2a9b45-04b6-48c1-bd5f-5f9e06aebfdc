# include "Pipe.h"
#include "PipeFdManager.h"


Pipe::Pipe(UserProcess* process):pipe_buffer_(256)
{

fds_[0]= PipeFdManager::instance()->generateFd();
fds_[1]= PipeFdManager::instance()->generateFd();


ustl::pair<bool,bool> open_closed_bits;
open_closed_bits.first=true;
open_closed_bits.second=true;


references.insert({process,open_closed_bits});

}

Pipe::~Pipe(){};


void Pipe::write(char* buffer, size_t size)
{
      size_t i = 0;

      while(i < size)
      {
        pipe_buffer_.put( buffer[i]);
        i++;
      }
}

void Pipe::read(char* buffer, size_t count)
{ 
     size_t i =0;
      while(i < count)
      {
        char c;
        pipe_buffer_.get(c);
        buffer[i]= c;
        i++;
      }
      
}

bool Pipe::is_reading_fd(size_t fd)
{

 return (fd == fds_[0]);

}

bool Pipe::is_writing_fd(size_t fd)
{

 return (fd == fds_[1]);

}

void Pipe::reset_open_closed_bits(size_t fd, UserProcess* currentProcess)
{

     if(fds_[0] == fd)
     {
       references[currentProcess].first=0;
       
     }
     if(fds_[1] == fd)
     {
       references[currentProcess].second=0;
     }

      if(references[currentProcess].first ==0 && references[currentProcess].second ==0)
     {
      
      references.erase(currentProcess);
      
     }
     

}

bool Pipe::has_reference(size_t  pid)
{
   
  for(auto pair : references)
  {
    if(pair.first->pid_ == pid)
    {
      return true;
    }
  }

return false;
 
}

void Pipe::insert_reference_entry(UserProcess* process)
{
   ustl::pair<bool,bool> open_closed_bits;
    open_closed_bits.first=true;
    open_closed_bits.second=true;
    references.insert({process,open_closed_bits});
}

void Pipe::remove_refernce_entry(UserProcess* process)
{
  
  references.erase(process);

}

