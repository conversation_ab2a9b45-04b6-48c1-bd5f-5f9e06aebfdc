#include "PipeFdManager.h"

PipeFdManager *PipeFdManager::instance_ = nullptr;

PipeFdManager *PipeFdManager::instance()
{
    if(unlikely(!instance_))
        instance_ = new PipeFdManager();
    return instance_;
}

PipeFdManager::PipeFdManager() : lock_("PipeFdManager::lock")
{
    
}

size_t PipeFdManager::generateFd()
{
    lock_.acquire();
    
    size_t fd = last_fd_++;

    if(fd % 100 == 0)
    {
      fd = last_fd_++;
    }
    
    lock_.release();
    return fd;
}