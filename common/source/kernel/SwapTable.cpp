#include "SwapTable.h"

SwapTable::SwapTable()
{
}

SwapEntry* SwapTable::getEntry(size_t spn)
{
    for(auto it = Table.begin(); it != Table.end(); it++)
    {
        if(it->spn_ == spn)
        {
            return it;
        }
    }
    return 0;
}

void SwapTable::removeReference(size_t spn, size_t pid)
{
     if(getEntry(spn) != NULL)
     {
    auto& tuple_vec = getEntry(spn)->IPT_Entry_.tuples;

   
        
    for (auto tuple = tuple_vec.begin(); tuple != tuple_vec.end();)
    {
     if(tuple->process->pid_ == pid)
     {
        
        debug(USERPROCESS, " erased tuple with process %d", pid);
        tuple = tuple_vec.erase(tuple);
     }
     else
     {
        ++tuple;
     }

   }
     }
    

    
}