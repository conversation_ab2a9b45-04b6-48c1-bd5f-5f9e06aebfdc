#include "ShmObject.h"
#include "ShmObjectFdManager.h"
#include "VfsSyscall.h"
#include "Loader.h"

ShmObject::ShmObject(char* name,mode_t mode):name_(name),mode_(mode){

        


};
ShmObject::ShmObject(type type):type_(type){};

ShmObject::ShmObject(Inode* inode):backing_inode(inode){};

ShmObject::~ShmObject()
{
  

  //delete backing_inode;

}

MemoryMappedRegion* ShmObject::getMemoryMapping(UserProcess* process, size_t adress)
{
     assert(process != NULL);
    if(memory_mappings.find(process) != memory_mappings.end() )
    {
        for(auto& memory_mapped_region : memory_mappings[process])
        {
            if(adress >= memory_mapped_region->start_adress && adress < memory_mapped_region->end_adress)
            {
                return memory_mapped_region;
            }
        }
    }
  
  return NULL;

}

bool ShmObject::hasReference(UserProcess* process)
{
   
    assert(process != NULL);
    //assert(memory_mappings.size() > 0);
    if(memory_mappings.find(process) != memory_mappings.end() )
    {
        return true;
    }
   
   return false;
}

void ShmObject::addMemoryMapping(UserProcess* process,MemoryMappedRegion* memory_mapped_region)
{
    assert(process != NULL);
    assert(memory_mapped_region != NULL);
    memory_mappings[process].push_back(memory_mapped_region);
}

void ShmObject::removeMemoryMapping(UserProcess* process,size_t adress)
{
  assert(process != NULL);

  if(hasReference(process))
  {

   auto memory_mapped_regions= memory_mappings[process];

    for (auto it = memory_mapped_regions.begin(); it != memory_mapped_regions.end(); ++it)
  {
        auto memory_mapped_region = *it;
    if(adress >= memory_mapped_region->start_adress && adress < memory_mapped_region->end_adress)
    {
     if(adress/PAGE_SIZE == memory_mapped_region->start_adress/PAGE_SIZE)
     {
      //debug(LOADER, " removing first page of mapping %p\n",adress);
       memory_mapped_region->start_adress +=4096;
       //debug(LOADER, "  %p  %p\n",memory_mapped_region->end_adress - 4096,memory_mapped_region->start_adress );
       if(adress/PAGE_SIZE == (memory_mapped_region->end_adress - 4096)/PAGE_SIZE && (memory_mapped_region->end_adress )/PAGE_SIZE == memory_mapped_region->start_adress/PAGE_SIZE )
       {
        //debug(LOADER, "no page left remove mapping %p\n",adress);
         memory_mapped_regions.erase(it);
         break;
       }
       break;
     }
     if(adress/PAGE_SIZE == (memory_mapped_region->end_adress - 4096)/PAGE_SIZE && (memory_mapped_region->end_adress - 4096)/PAGE_SIZE != memory_mapped_region->start_adress/PAGE_SIZE )
     {
      //debug(LOADER, " removing last page of mapping %p \n",adress);
       memory_mapped_region->end_adress -= 4096;
       break;
     }

    //debug(LOADER, " removing page somwehere in the middles\n");

    auto split_mapping_block= new MemoryMappedRegion{adress+4096,memory_mapped_region->end_adress,memory_mapped_region->permission_bits,0,memory_mapped_region->offset_};
    //debug(LOADER, " new block starting at : %p and endingt at not inclusive: %p\n", split_mapping_block->start_adress, split_mapping_block->end_adress);
    memory_mapped_region->end_adress = adress;
    //debug(LOADER, " old now ending  not inclusive: %p\n", memory_mapped_region->end_adress);
    memory_mapped_regions.insert(it + 1, split_mapping_block);
     break;
    
    }
  }  
  }
   
}

void ShmObject::removeReference(UserProcess* process)
{
  if(hasReference(process))
  {
    memory_mappings[process].deallocate();
    memory_mappings.erase(process);
  }

}

void ShmObject::addReference(UserProcess* process)
{
 
  if(memory_mappings.find(process) != memory_mappings.end() )
    {
        ustl::vector<MemoryMappedRegion*> memory_mapped_regions;
        memory_mappings.insert({process, memory_mapped_regions });
    }

}

int ShmObject::generateFd()
{
       if(backing_inode == NULL)
       {
        auto fs_type=vfs.getFsType("ramfs");
        auto superblock=fs_type->createSuper(1);
        backing_inode = superblock->createInode(I_FILE);
        
       }
       int fd =ShmObjectFdManager::instance()->generateFd(backing_inode,((UserThread*) currentThread)->process_);
       debug(PAGEFAULT, "%d",fd);
       fds_.push_back(fd);  
       return fd;

}

void ShmObject::performWriteback(UserProcess* process, size_t adress)
{
   assert(process != NULL);
   MemoryMappedRegion* memory_region =  getMemoryMapping(process,adress);
   size_t index = (adress -memory_region->start_adress)/PAGE_SIZE;
   off_t offset = memory_region->offset_;
   assert(memory_region != NULL);
    
    
    

    if(mapping_index_vpn_ppn.find(index) != mapping_index_vpn_ppn.end())
    {
      size_t fd = process->getGlobalFdNum(memory_region->fd_);
      auto ppn = mapping_index_vpn_ppn[index];
      if((int)fd != -1) 
      {  
        VfsSyscall::lseek(fd,index*PAGE_SIZE+offset,SEEK_SET);
        VfsSyscall::write(fd,(char*)process->loader_->arch_memory_.getIdentAddressOfPPN(ppn),4096);
      }
      
    }


}

 