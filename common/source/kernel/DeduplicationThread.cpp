#include "DeduplicationThread.h"
#include "Scheduler.h"
#include "Loader.h"
#include "ProcessRegistry.h"
#include "PageManager.h"

DeduplicationThread::DeduplicationThread() : Thread(0, "Deduplication Thread", Thread::KERNEL_THREAD)
{
}

DeduplicationThread::~DeduplicationThread()
{
  assert(false && "Deduplication Thread destruction means that you probably have accessed an invalid pointer somewhere.");
}

void DeduplicationThread::kill()
{
  assert(false && "Deduplication Thread destruction means that you probably have accessed an invalid pointer somewhere.");
}

void DeduplicationThread::Run()
{

while(1)
{
   ProcessRegistry::instance()->dedup_lock_.acquire();
   while (ProcessRegistry::instance()->dedup == false)
   {
    ProcessRegistry::instance()->dedup_wait.wait();
     debug(LOADER, "Deduplication Thread woke up\n");
   }
   ProcessRegistry::instance()->dedup = false;
   ProcessRegistry::instance()->dedup_lock_.release();
   


ProcessRegistry::instance()->ipt_lock_.acquire();
  deduplicate();
  hash_to_ppn.deallocate();

//debug(LOADER, "Ipt after deduplicating\n");
//ProcessRegistry::instance()->printIPT();

ProcessRegistry::instance()->ipt_lock_.release();  






 
}



}

void DeduplicationThread::deduplicate()
{
   for(size_t i =0; i< 2016; i++)
   {
     auto& ipt_entry = ProcessRegistry::instance()->inverted_page_table[i];
     

     if(ipt_entry.tuples.size()>0 )
     {
       if(ipt_entry.tuples.at(0).type != PAGE)
       {
        continue;
       }
       auto& process = ipt_entry.tuples.at(0).process;
       acquireLockifNeeded(process);
       char* page  = (char*)  process->loader_->arch_memory_.getIdentAddressOfPPN(i);
       size_t hash = hashFunction(page);
       if(hash_to_ppn.find(hash) != hash_to_ppn.end())
       {
           bool found_duplicate = false;
          for(auto& ppn : hash_to_ppn[hash])
          {
          
          auto& ipt_entry_duplicate_page = ProcessRegistry::instance()->inverted_page_table[ppn];
          assert(ipt_entry_duplicate_page.tuples.size() >0);
          auto& process2 = ipt_entry_duplicate_page.tuples.at(0).process;
          acquireLockifNeeded(process2);
          char* page_to_compare  = (char*) process2->loader_->arch_memory_.getIdentAddressOfPPN(ppn);
          

         if(memcmp(page,page_to_compare,PAGE_SIZE) == 0 )
         {
            found_duplicate = true;
            debug(LOADER, "found duplicate\n");

             for(auto& tuple :ipt_entry_duplicate_page.tuples)
          {
            acquireLockifNeeded(tuple.process);
            auto& mapping = tuple.process->loader_->arch_memory_.resolveMapping(tuple.vpn);

             auto page_table_entry = &mapping.pt[mapping.pti];
             page_table_entry->writeable=0;
             page_table_entry->cow = 1;
             //tuple.process->loader_->arch_memory_.archmemory_lock_.release();

          } 

          for(auto& tuple :ipt_entry.tuples)
          {
             acquireLockifNeeded(tuple.process);
             auto& mapping = tuple.process->loader_->arch_memory_.resolveMapping(tuple.vpn);
             auto page_table_entry = &mapping.pt[mapping.pti];
             page_table_entry->writeable=0;
             page_table_entry->cow = 1;
             page_table_entry->page_ppn = ppn;
              debug(LOADER, " deduped from page %d to page %d \n",i,ppn);
             ProcessRegistry::instance()->addToIPT(ppn, tuple.vpn ,tuple.process,PAGE);
             //tuple.process->loader_->arch_memory_.archmemory_lock_.release();
          }
          PageManager::instance()->freePPN(i);
         ipt_entry.tuples.deallocate();
         
         assert(ipt_entry.tuples.size() ==0);
         assert(ProcessRegistry::instance()->inverted_page_table[i].tuples.size() ==0);
          
        

            break;
         }
         
        

       }
       if(found_duplicate == false)
       {
         hash_to_ppn[hash].push_back(i);
       }
       }
       else
       {
         ustl::vector <size_t> ppns;
         ppns.push_back(i);
         hash_to_ppn.insert({hash,ppns});
       }
       ReleaseAllArchmemLocks();  
     }

   }     
 

}

size_t DeduplicationThread::hashFunction(char* page)
{
    size_t hash = 0;
    for(size_t i = 0; i < 4096 ; i++)
    {
        hash = hash +31 * page[i];
    }

 return hash;

}

void DeduplicationThread::acquireLockifNeeded(UserProcess* process)
{
   
  if(!findProcessinVector(process))
  {
    acquired_locks.push_back(process);
    process->loader_->arch_memory_.archmemory_lock_.acquire();
  }
       

}


bool DeduplicationThread::findProcessinVector(UserProcess* process)
{
   for(auto& process_to_compare : acquired_locks)
    {
       if(process == process_to_compare)
       {
          return true;
       }
    }
    return false;  

}

void DeduplicationThread::ReleaseAllArchmemLocks()
{
  for(int i = acquired_locks.size()-1 ; i >= 0; i--)
  {
    acquired_locks.at(i)->loader_->arch_memory_.archmemory_lock_.release();
  }
acquired_locks.deallocate();

}


