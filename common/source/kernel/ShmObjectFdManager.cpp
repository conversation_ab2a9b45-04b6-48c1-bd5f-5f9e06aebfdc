#include "ShmObjectFdManager.h"

ShmObjectFdManager *ShmObjectFdManager::instance_ = nullptr;

ShmObjectFdManager *ShmObjectFdManager::instance()
{
    if(unlikely(!instance_))
        instance_ = new ShmObjectFdManager();
    return instance_;
}

ShmObjectFdManager::ShmObjectFdManager() : lock_("ShmObjectFdManager::lock")
{
    
}

size_t ShmObjectFdManager::generateFd(Inode* inode, UserProcess* process)
{
    lock_.acquire();
   

    Dentry* file_dentry = new Dentry(inode);
    RamFSFile* ramfs_file = new RamFSFile(inode, file_dentry, O_RDWR | O_CREAT);
    auto filedescriptor=ramfs_file->openFd();
    size_t fd=filedescriptor->getFd();
    assert(!global_fd_list.add(filedescriptor));
    fd=process->mapGlobalFdNum(fd);

  
    lock_.release();
    return fd;
}