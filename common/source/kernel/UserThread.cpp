#include "UserThread.h"
#include "offsets.h"
#include "debug.h"
#include "ArchThreads.h"
#include "PageManager.h"
#include "Thread.h"
#include "Loader.h"
#include "Console.h"
#include "Mutex.h"
#include "Condition.h"
#include "ProcessRegistry.h"

UserThread::UserThread(UserProcess *process, ustl::string filename, size_t startroutine, size_t arg, uint32_t terminal_number, size_t(wrapper))
    : Thread(process->fs_info_, filename, Thread::USER_THREAD),
      process_(process),
      terminal_number_(terminal_number),
      arg_(arg)
{
  process_->process_lock_.acquire();
  loader_ = process_->loader_;
  setTID(++process_->latest_tid_);

  canceled = 0;
  detached = 0;
  setCancelState(PTHREAD_CANCEL_ENABLE);
  setCancelType(PTHREAD_CANCEL_DEFERRED);

  process_->process_lock_.release();


  stack_end_ = (USER_BREAK - sizeof(pointer) - getTID()*16 * PAGE_SIZE);
  stack_start_ = (stack_end_ + PAGE_SIZE * 15); //die letzte page für guard auslassen


  if (wrapper != (size_t)nullptr)
  {
    ArchThreads::createUserRegisters(user_registers_, (void *)wrapper, (void *)stack_start_, getKernelStackStartPointer());
    user_registers_->rip = wrapper;
    user_registers_->rdi = startroutine;
    user_registers_->rsi = arg;
  }

  else
  {
    ArchThreads::createUserRegisters(user_registers_, (void *)startroutine, (void *)stack_start_, getKernelStackStartPointer());
  }

  loader_->arch_memory_.archmemory_lock_.acquire();
  ArchThreads::setAddressSpace(this, loader_->arch_memory_);
  loader_->arch_memory_.archmemory_lock_.release();

  debug(USERPROCESS, "ctor: Done loading %s\n", filename.c_str());

  if (main_console->getTerminal(terminal_number))
    setTerminal(main_console->getTerminal(terminal_number));

  process_->threadcount_++;
  wait_for_thread = new Condition(&process->thread_vector_lock_, "waiting for thread");
  wait_for_process = new Condition(&ProcessRegistry::instance()->process_vector_lock_, "waiting for process");
  switch_to_userspace_ = 1;
}

UserThread::UserThread(UserThread const &other, UserProcess *new_process) : Thread(other),
                                                                            process_(new_process),
                                                                            terminal_number_(other.terminal_number_),
                                                                            arg_(other.arg_)
{
  process_->process_lock_.acquire();
  setTID(++process_->latest_tid_);
  loader_ = process_->loader_;

  canceled = 0;
  detached = 0;
  setCancelState(PTHREAD_CANCEL_ENABLE);
  setCancelType(PTHREAD_CANCEL_DEFERRED);

  process_->process_lock_.release();
  wait_for_thread = new Condition(&process_->thread_vector_lock_, "waiting for thread");
  wait_for_process = new Condition(&ProcessRegistry::instance()->process_vector_lock_, "waiting for process");

  // size_t page_for_stack = PageManager::instance()->allocPPN();
  // loader_->arch_memory_.archmemory_lock_.acquire();
  // bool vpn_mapped = loader_->arch_memory_.mapPage(USER_BREAK / PAGE_SIZE - 1 - getTID(), page_for_stack, 1);
  // assert(vpn_mapped && "Virtual page for stack was already mapped - this should never happen");
  // loader_->arch_memory_.archmemory_lock_.release();
  stack_end_ = other.stack_end_;
  stack_start_ = other.stack_start_;

  type_ = TYPE::USER_THREAD;

  loader_->arch_memory_.archmemory_lock_.acquire();
  ArchThreads::setAddressSpace(this, loader_->arch_memory_);
  loader_->arch_memory_.archmemory_lock_.release();
  process_->threadcount_++; // atomic
  debug(FORK, "Copy constructor: Done copieing userthread\n");
}

UserThread::~UserThread()
{

  if (process_->exec == 1 && delete_old_loader)
  {
    process_->exec = 0;
    delete loader_;
    loader_ = 0;
  }

  if (this->process_->threadcount_ == 1)
  {
    delete process_;
  }
  else
  {
    this->process_->threadcount_--;
  }
}

void UserThread::Run()
{
  assert(false);
}

bool UserThread::checkExecparameters(size_t path, char **argv, size_t &argc)
{
  if (path >= USER_BREAK || path == NULL)
  {
    return false;
  }

  if ((size_t)argv >= USER_BREAK)
  {
    return false;
  }

  size_t index = 0;
  size_t size = 0;

  if (argv != NULL)
  {
    while (argv[index] != NULL)
    {
      index++;
    }

    argc = index;
    for (size_t i = 0; i < argc; i++)
    {
      if ((size_t)argv[i] >= USER_BREAK)
      {
        return false;
      }
      size += strlen(argv[i]);
    }
  }

  size += argc * sizeof(char *) + sizeof(char *);
  if (size > PAGE_SIZE)
  {
    return false;
  }

  return true;
}

char **UserThread::copyStringsOnPage(size_t argc, char **argv)
{

 

  size_t ppn = PageManager::instance()->allocPPN();
  char *user_stack = (char *)(stack_start_ - PAGE_SIZE);
  user_stack = user_stack - PAGE_SIZE * 2 + sizeof(pointer);
  int counter = -1;
  ustl::queue<size_t> ppns;
  PageManager::instance()->allocatePagesMapPage(counter,ppns,(stack_start_ / PAGE_SIZE) - 2, loader_->arch_memory_);
  bool vpn_mapped = loader_->arch_memory_.mapPage((stack_start_ / PAGE_SIZE) - 2,ppns,ppn, 1);
  
  
  assert(vpn_mapped && "Virtual page for stack was already mapped - this should never happen");
   char *page = (char *)loader_->arch_memory_.getIdentAddressOfPPN(ppn);

  size_t offset = 0;
  ustl::vector<char *> adresses_of_strings;
  for (size_t i = 0; i < argc; i++)
  {
    memcpy((char *)(page + offset), argv[i], strlen(argv[i]) + 1);
    char *adress = user_stack + offset;
    adresses_of_strings.push_back(adress);
    offset += strlen(argv[i]) + 1;
  }
  char **virtual_adress = (char **)(page + offset);

  for (size_t i = 0; i < argc; i++)
  {
    *(virtual_adress + i) = adresses_of_strings.at(i);
  }

  char **array = (char **)(user_stack + offset);
  loader_->arch_memory_.archmemory_lock_.release();
  ProcessRegistry::instance()->ipt_lock_.release();

  return array;
}

int UserThread::join_thread(size_t thread_id, size_t &ret_val)
{
  if (getTID() == thread_id || thread_id == 0)
  {
    return -1;
  }

  assert(process_ != NULL);
  if (process_->thread_retvals.find(thread_id) != process_->thread_retvals.end())
  {
    ret_val = process_->getRetValFromMap(thread_id);
    return 0;
  }

  if (process_->getThreadfromVector(thread_id) != NULL && !process_->getThreadfromVector(thread_id)->detached)
  {

    auto userthread = (UserThread *)currentThread;
    assert(userthread != NULL);
    userthread->waiting_on_other_thread = true;
    userthread->waiting_thread_id = thread_id;

    if (userthread->process_->checkForDeadlockJoin())
    {
      return -1;
    }

    while (userthread->waiting_on_other_thread)
    {
      assert(userthread->wait_for_thread != NULL);
      userthread->wait_for_thread->wait();
    }

    ret_val = process_->getRetValFromMap(thread_id);

    return 0;
  }

  else
  {
    return -1;
  }
}

void UserThread::pollPageTablesUpdateNFU()
{
 
   PageMapLevel4Entry *pml4 = (PageMapLevel4Entry *)loader_->arch_memory_.getIdentAddressOfPPN(loader_->arch_memory_.page_map_level_4_);
  for (uint64 pml4i = 0; pml4i < PAGE_MAP_LEVEL_4_ENTRIES / 2; pml4i++) // free only lower half
  {
    if (pml4[pml4i].present)
    {
      PageDirPointerTableEntry *pdpt = (PageDirPointerTableEntry *)loader_->arch_memory_.getIdentAddressOfPPN(pml4[pml4i].page_ppn);
      for (uint64 pdpti = 0; pdpti < PAGE_DIR_POINTER_TABLE_ENTRIES; pdpti++)
      {
        if (pdpt[pdpti].pd.present)
        {
          assert(pdpt[pdpti].pd.size == 0);
          PageDirEntry *pd = (PageDirEntry *)loader_->arch_memory_.getIdentAddressOfPPN(pdpt[pdpti].pd.page_ppn);
          for (uint64 pdi = 0; pdi < PAGE_DIR_ENTRIES; pdi++)
          {
            if (pd[pdi].pt.present)
            {
              assert(pd[pdi].pt.size == 0);
              PageTableEntry *pt = (PageTableEntry *)loader_->arch_memory_.getIdentAddressOfPPN(pd[pdi].pt.page_ppn);
              for (uint64 pti = 0; pti < PAGE_TABLE_ENTRIES; pti++)
              {
                if (pt[pti].present && pt[pti].accessed)
                {
                  pt[pti].accessed = 0;
                  ProcessRegistry::instance()->inverted_page_table[pt[pti].page_ppn].refcount_nfu++;
                  
                }

              }
             
            }
          }
         
        }
      }
    
    }
  }
  



}
