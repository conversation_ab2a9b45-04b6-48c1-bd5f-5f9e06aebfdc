#include "Scheduler.h"
#include "Thread.h"
#include "panic.h"
#include "ArchThreads.h"
#include "ArchCommon.h"
#include "kprintf.h"
#include "ArchInterrupts.h"
#include "KernelMemoryManager.h"
#include <ulist.h>
#include "backtrace.h"
#include "ArchThreads.h"
#include "Mutex.h"
#include "umap.h"
#include "ustring.h"
#include "Lock.h"

#include "Syscall.h"
#include "UserThread.h"
#include "ArchMemory.h"
#include "Loader.h"

ArchThreadRegisters *currentThreadRegisters;
Thread *currentThread;

Scheduler *Scheduler::instance_ = nullptr;

Scheduler *Scheduler::instance()
{
  if (unlikely(!instance_))
    instance_ = new Scheduler();
  return instance_;
}

Scheduler::Scheduler()
{
  block_scheduling_ = 0;
  ticks_ = 0;
  addNewThread(&cleanup_thread_);
  addNewThread(&idle_thread_);
  addNewThread(&swap_thread_);
}

void Scheduler::schedule()
{
  assert(!ArchInterrupts::testIFSet() && "Tried to schedule with Interrupts enabled");
  if (block_scheduling_)
  {
    debug(SCHEDULER, "schedule: currently blocked\n");
    return;
  }
  if (currentThread != 0x0)
  { 
     endClock();
     
     if(currentThread->type_ == Thread::USER_THREAD)
     {
      auto userthread = ((UserThread*)currentThread);
      userthread->pollPageTablesUpdateNFU();   
     }

  }



  size_t already_one_possiblitiy_found = 0;
  auto it = threads_.begin();
  auto current_it=it;

  for(; it != threads_.end(); ++it)
  {
    if((*it)->asleep)
    {
      if(getTicks()>=(*it)->wake_time)
      {
        (*it)->asleep=false;
        if((*it)->schedulable())
        {
          currentThread = *it;
          current_it=it;
          break;
        }

      }

    }
    if((*it)->schedulable() && already_one_possiblitiy_found == 0)
      {
          if((*it)->type_ == Thread::USER_THREAD)
          {
              size_t tmp;
              asm volatile("movq %%cr3, %0" : "=r"(tmp) :: "memory");
              asm volatile("movq %[new_cr3], %%cr3" :: [new_cr3]"r"((*it)->loader_->arch_memory_.page_map_level_4_ * PAGE_SIZE));
              size_t* pointer =(size_t*) ((UserThread*)(*it))->stack_start_ -1;
              if((*it)->loader_->arch_memory_.checkAddressValid((size_t)pointer))
              {
                  size_t sleep = *pointer;
                  asm volatile("movq %0, %%cr3" :: "r"(tmp) : "memory");
                  if(sleep == 9)
                  {
                      continue;
                  }
              }
              asm volatile("movq %0, %%cr3" :: "r"(tmp) : "memory");
          }
        currentThread = *it;
        current_it=it;
        already_one_possiblitiy_found = 1;
      }


  }

  assert(current_it != threads_.end() && "No schedulable thread found");
  ustl::rotate(threads_.begin(), current_it + 1, threads_.end()); // no new/delete here - important because interrupts are disabled
  //debug(SCHEDULER, "Scheduler::schedule: new currentThread is %p %s, switch_to_userspace: %d\n", currentThread, currentThread->getName(), currentThread->switch_to_userspace_);
  //start processor time count

  auto newProcess = ((UserThread *)currentThread)->process_;
  //new idea save every thread runtime in the thread and if clock called go through the processes threads and add the times together
  startClock();

  if(currentThread->switch_to_userspace_ == 1)
{
  if(currentThread->canceled==1 || newProcess->exit_called)
  {
    if((currentThread->getCancelState()==PTHREAD_CANCEL_ENABLE && currentThread->getCancelType()==PTHREAD_CANCEL_ASYNCHRONOUS)|| ((UserThread*) currentThread)->process_->exit_called)
    {

      currentThreadRegisters = currentThread->kernel_registers_;

      debug(SCHEDULER, "SCHEDULER::schedule: Async canceling\n");
      currentThread->switch_to_userspace_ = 0;
      currentThreadRegisters->rip = reinterpret_cast<size_t>(Syscall::exitthread);
      currentThreadRegisters->rdi = PTHREAD_CANCELED;
      return;
    }
  }
  currentThreadRegisters = currentThread->user_registers_;
  return;
}
currentThreadRegisters = currentThread->kernel_registers_;
}

void Scheduler::addNewThread(Thread *thread)
{
  assert(thread);
  debug(SCHEDULER, "addNewThread: %p  %zd:%s\n", thread, thread->getTID(), thread->getName());
  if (currentThread)
    ArchThreads::debugCheckNewThread(thread);
  KernelMemoryManager::instance()->getKMMLock().acquire();
  lockScheduling();
  KernelMemoryManager::instance()->getKMMLock().release();
  threads_.push_back(thread);
  unlockScheduling();
}

void Scheduler::sleep()
{
  currentThread->setState(Sleeping);
  assert(block_scheduling_ == 0);
  yield();
}

void Scheduler::wake(Thread* thread_to_wake)
{
  // wait until the thread is sleeping
  while(thread_to_wake->getState() != Sleeping)
    yield();
  thread_to_wake->setState(Running);
}

void Scheduler::yield()
{
  assert(this);
  if (!ArchInterrupts::testIFSet())
  {
    assert(currentThread);
    kprintfd("Scheduler::yield: WARNING Interrupts disabled, do you really want to yield ? (currentThread %p %s)\n",
             currentThread, currentThread->name_.c_str());
    currentThread->printBacktrace();
  }
  ArchThreads::yield();
}

void Scheduler::cleanupDeadThreads()
{
  /* Before adding new functionality to this function, consider if that
     functionality could be implemented more cleanly in another place.
     (e.g. Thread/Process destructor) */

  assert(currentThread == &cleanup_thread_);

  lockScheduling();
  uint32 thread_count_max = sizeof(cleanup_thread_.kernel_stack_) / (2 * sizeof(Thread*));
  thread_count_max = ustl::min(thread_count_max, threads_.size());
  Thread* destroy_list[thread_count_max];
  uint32 thread_count = 0;
  for (uint32 i = 0; i < threads_.size() && thread_count < thread_count_max; ++i)
  {
    Thread* tmp = threads_[i];
    if (tmp->getState() == ToBeDestroyed)
    {
      destroy_list[thread_count++] = tmp;
      threads_.erase(threads_.begin() + i); // Note: erase will not realloc!
      --i;
    }
  }
  unlockScheduling();
  if (thread_count > 0)
  {
    for (uint32 i = 0; i < thread_count; ++i)
    {
      delete destroy_list[i];
    }
    debug(SCHEDULER, "cleanupDeadThreads: done\n");
  }
}

void Scheduler::printThreadList()
{
  lockScheduling();
  debug(SCHEDULER, "Scheduler::printThreadList: %zd Threads in List\n", threads_.size());
  for (size_t c = 0; c < threads_.size(); ++c)
    debug(SCHEDULER, "Scheduler::printThreadList: threads_[%zd]: %p  %zd:%25s     [%s]\n", c, threads_[c],
          threads_[c]->getTID(), threads_[c]->getName(), Thread::threadStatePrintable[threads_[c]->state_]);
  unlockScheduling();
}

void Scheduler::lockScheduling() //not as severe as stopping Interrupts
{
  if (unlikely(ArchThreads::testSetLock(block_scheduling_, 1)))
    kpanict("FATAL ERROR: Scheduler::*: block_scheduling_ was set !! How the Hell did the program flow get here then ?\n");
}

void Scheduler::unlockScheduling()
{
  block_scheduling_ = 0;
}

bool Scheduler::isSchedulingEnabled()
{
  return this && block_scheduling_ == 0;
}

bool Scheduler::isCurrentlyCleaningUp()
{
  return currentThread == &cleanup_thread_;
}

size_t Scheduler::getTicks()
{
  return ticks_;
}

void Scheduler::incTicks()
{
  ++ticks_;
}

void Scheduler::printStackTraces()
{
  lockScheduling();
  debug(BACKTRACE, "printing the backtraces of <%zd> threads:\n", threads_.size());

  for (const auto& thread : threads_)
  {
    thread->printBacktrace();
    debug(BACKTRACE, "\n");
    debug(BACKTRACE, "\n");
  }

  unlockScheduling();
}

void Scheduler::printLockingInformation()
{
  lockScheduling();
  kprintfd("\n");
  debug(LOCK, "Scheduler::printLockingInformation:\n");

  for(Thread* t : threads_)
  {
    if(t->holding_lock_list_)
      Lock::printHoldingList(t);
  }
  for(Thread* t : threads_)
  {
    if(t->lock_waiting_on_)
      debug(LOCK, "Thread %s (%p) is waiting on lock: %s (%p).\n",
            t->getName(), t, t->lock_waiting_on_ ->getName(), t->lock_waiting_on_ );
  }
  debug(LOCK, "Scheduler::printLockingInformation finished\n");
  unlockScheduling();
}

size_t Scheduler::getRdtsc()
{
  uint32 hi, lo;
  asm volatile("rdtsc" : "=a"(lo), "=d"(hi));
  size_t currentTime = (size_t)hi<<32|lo;

  return currentTime;
}

void Scheduler::endClock()
{

  currentThread->current_process_time = currentThread->current_process_time+(getRdtsc()-currentThread->start_process_time);

}

void Scheduler::startClock()
{

  currentThread->start_process_time = getRdtsc();

} 

