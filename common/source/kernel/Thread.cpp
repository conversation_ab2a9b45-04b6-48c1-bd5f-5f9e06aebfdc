#include "Thread.h"

#include "kprintf.h"
#include "ArchThreads.h"
#include "ArchInterrupts.h"
#include "Scheduler.h"
#include "Loader.h"
#include "Console.h"
#include "Terminal.h"
#include "backtrace.h"
#include "KernelMemoryManager.h"
#include "Stabs2DebugInfo.h"


#define BACKTRACE_MAX_FRAMES 20



const char* Thread::threadStatePrintable[3] =
{
"Running", "Sleeping", "ToBeDestroyed"
};

extern "C" void threadStartHack()
{
  currentThread->setTerminal(main_console->getActiveTerminal());
  currentThread->Run();
  currentThread->kill();
  debug(THREAD, "ThreadStartHack: Panic, thread couldn't be killed\n");
  while(1);
}

Thread::Thread(FileSystemInfo *working_dir, ustl::string name, Thread::TYPE type) :
    kernel_registers_(0), user_registers_(0), switch_to_userspace_(type == Thread::USER_THREAD ? 1 : 0), loader_(0),
    next_thread_in_lock_waiters_list_(0), lock_waiting_on_(0), holding_lock_list_(0), state_(Running), tid_(0),
    my_terminal_(0), working_dir_(working_dir), name_(ustl::move(name))
{
  type_ = type;
  debug(THREAD, "Thread ctor, this is %p, stack is %p, fs_info ptr: %p\n", this, kernel_stack_, working_dir_);
  ArchThreads::createKernelRegisters(kernel_registers_, (void*) (type == Thread::USER_THREAD ? 0 : threadStartHack), getKernelStackStartPointer());
  kernel_stack_[2047] = STACK_CANARY;
  kernel_stack_[0] = STACK_CANARY;

  canceled = 0;
  detached = 0;
  asleep = false;
}

Thread::Thread(Thread const &other) :
  canceled(other.canceled),
  kernel_registers_(new ArchThreadRegisters(*other.kernel_registers_)),
  user_registers_(new ArchThreadRegisters(*other.user_registers_)),
  switch_to_userspace_(1),
  next_thread_in_lock_waiters_list_(nullptr),
  lock_waiting_on_(nullptr),
  holding_lock_list_(nullptr),
  state_(other.state_),
  cancelstate_(other.cancelstate_),
  canceltype_(other.canceltype_),
  tid_(other.tid_),
  my_terminal_(other.my_terminal_),
  working_dir_(new FileSystemInfo(*other.working_dir_)),
  name_(other.name_)
{
  debug(FORK, "Thread ctor, this is %p, stack is %p, fs_info ptr: %p\n", this, kernel_stack_, working_dir_);
  memcpy(kernel_stack_, other.kernel_stack_, sizeof(kernel_stack_));
  kernel_stack_[2047] = STACK_CANARY;
  uint64 kernel_stack_top = (uint64)&kernel_stack_[2047];
  kernel_stack_[0] = STACK_CANARY;

  kernel_registers_->rsp = (uint64) kernel_stack_top;
  kernel_registers_->rbp = kernel_registers_->rsp;
  kernel_registers_->rsp0 = 0;
  kernel_registers_->rip = 0;
  user_registers_->rsp0 = kernel_registers_->rsp;
  user_registers_->rax = 0;//child return value should be 0;
  debug(FORK, "Copy constructor: Done copieing thread\n");
}

Thread::~Thread()
{
  debug(THREAD, "~Thread: freeing ThreadInfos\n");
  delete user_registers_;
  user_registers_ = 0;
  delete kernel_registers_;
  kernel_registers_ = 0;
  if(unlikely(holding_lock_list_ != 0))
  {
    debug(THREAD, "~Thread: ERROR: Thread <%s (%p)> is going to be destroyed, but still holds some locks!\n",
          getName(), this);
    Lock::printHoldingList(this);
    assert(false);
  }
  debug(THREAD, "~Thread: done (%s)\n", name_.c_str());
}

// DO NOT use new / delete in this Method, as it is sometimes called from an Interrupt Handler with Interrupts disabled
void Thread::kill()
{
  debug(THREAD, "kill: Called by <%s (%p)>. Preparing Thread <%s (%p)> for destruction\n", currentThread->getName(),
        currentThread, getName(), this);

  setState(ToBeDestroyed); // vvv Code below this line may not be executed vvv

  if (currentThread == this)
  {
    ArchInterrupts::enableInterrupts();
    Scheduler::instance()->yield();
    assert(false && "This should never happen, how are we still alive?");
  }
}

void* Thread::getKernelStackStartPointer()
{
  pointer stack = (pointer) kernel_stack_;
  stack += sizeof(kernel_stack_) - sizeof(uint32);
  return (void*)stack;
}

bool Thread::currentThreadIsStackCanaryOK()
{
  return !currentThread || currentThread->isStackCanaryOK();
}
bool Thread::isStackCanaryOK()
{
  return kernel_stack_[0] == STACK_CANARY && kernel_stack_[2047] == STACK_CANARY;
}

Terminal *Thread::getTerminal()
{
  return my_terminal_ ? my_terminal_ : main_console->getActiveTerminal();
}

void Thread::setTerminal(Terminal *my_term)
{
  my_terminal_ = my_term;
}

void Thread::printBacktrace()
{
  printBacktrace(currentThread != this);
}

FileSystemInfo* Thread::getWorkingDirInfo()
{
  return working_dir_;
}

FileSystemInfo* getcwd()
{
  if (FileSystemInfo* info = currentThread->getWorkingDirInfo())
    return info;
  return default_working_dir;
}

void Thread::setWorkingDirInfo(FileSystemInfo* working_dir)
{
  working_dir_ = working_dir;
}

extern Stabs2DebugInfo const *kernel_debug_info;

void Thread::printBacktrace(bool use_stored_registers)
{
  if(!kernel_debug_info)
  {
    debug(BACKTRACE, "Kernel debug info not set up, backtrace won't look nice!\n");
  }

  static_assert(BACKTRACE_MAX_FRAMES < 128);
  pointer call_stack[BACKTRACE_MAX_FRAMES];
  size_t count = 0;

  if(kernel_debug_info)
  {
    count = backtrace(call_stack, BACKTRACE_MAX_FRAMES, this, use_stored_registers);

    debug(BACKTRACE, "=== Begin of backtrace for %sthread <%s> ===\n", user_registers_ ? "user" : "kernel", getName());
    for(size_t i = 0; i < count; ++i)
    {
        debug(BACKTRACE, " ");
        kernel_debug_info->printCallInformation(call_stack[i]);
    }
  }
  if(user_registers_)
  {
    Stabs2DebugInfo const *deb = loader_->getDebugInfos();
    count = backtrace_user(call_stack, BACKTRACE_MAX_FRAMES, this, 0);
    debug(BACKTRACE, " ----- Userspace --------------------\n");
    if(!deb)
      debug(BACKTRACE, "Userspace debug info not set up, backtrace won't look nice!\n");
    else
    {
      for(size_t i = 0; i < count; ++i)
      {
        debug(BACKTRACE, " ");
        deb->printCallInformation(call_stack[i]);
      }
    }
  }
  debug(BACKTRACE, "=== End of backtrace for %sthread <%s> ===\n", user_registers_ ? "user" : "kernel", getName());
}

bool Thread::schedulable()
{
  return (getState() == Running && asleep == false);
}

bool Thread::is_sleeping()
{
  return (getState() == Sleeping);
}

const char *Thread::getName()
{
  return name_.c_str();
}

size_t Thread::getTID()
{
  return tid_;
}

void Thread::setTID(size_t id)
{
  tid_ = id;
}

ThreadState Thread::getState() const
{
  return state_;
}

void Thread::setState(ThreadState new_state)
{
  assert(!((state_ == ToBeDestroyed) && (new_state != ToBeDestroyed)) && "Tried to change thread state when thread was already set to be destroyed");
  assert(!((new_state == Sleeping) && (currentThread != this)) && "Setting other threads to sleep is not thread-safe");

  state_ = new_state;
}

CancelState Thread::getCancelState()
{
  return cancelstate_;
}

void Thread::setCancelState(CancelState new_state)
{
  assert(!((new_state != PTHREAD_CANCEL_ENABLE) && (new_state != PTHREAD_CANCEL_DISABLE)) && "Tried to change CancelState to undefined Type");
  debug(THREAD, " thread: %ld Set cancel_state %d\n",getTID(),new_state);
  cancelstate_ = new_state;
}

CancelType Thread::getCancelType()
{
  return canceltype_;
}

void Thread::setCancelType(CancelType new_type)
{ 
  assert(!((new_type != PTHREAD_CANCEL_ASYNCHRONOUS) && (new_type != PTHREAD_CANCEL_DEFERRED)) && "Tried to change CancelType to undefined Type");
  debug(THREAD, " thread: %ld Set cancel_type %d\n",getTID(),new_type);
  canceltype_ = new_type;
}
