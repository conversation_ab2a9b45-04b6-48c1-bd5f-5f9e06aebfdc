#include "Condition.h"
#include "Thread.h"
#include "Mutex.h"
#include "Scheduler.h"
#include "assert.h"
#include "kprintf.h"
#include "debug.h"
#include "Stabs2DebugInfo.h"
#include "backtrace.h"

Condition::Condition(Mutex* mutex, const char* name) :
  Lock(name), mutex_(mutex)
{
}

Condition::~Condition()
{
  mutex_ = nullptr; // Explicitly set to zero to catch some incorrect wait() usages
}

void Condition::waitAndRelease(pointer called_by)
{
  if(!called_by)
    called_by = getCalledBefore(1);
  wait(false, called_by);
}

void Condition::wait(bool re_acquire_mutex, pointer called_by)
{
  if(unlikely(system_state != RUNNING))
    return;
  if(!called_by)
    called_by = getCalledBefore(1);
//  debug(LOCK, "Condition::wait: Thread %s (%p) is waiting on condition %s (%p).\n",
//        currentThread->getName(), currentThread, getName(), this);
//  if(kernel_debug_info)
//  {
//    debug(LOCK, "The wait is called by: ");
//    kernel_debug_info->printCallInformation(called_by);
//  }

  assert(mutex_->isHeldBy(currentThread));
  // check if the interrupts are enabled and the thread is not waiting for some other locks
  checkInterrupts("Condition::wait");
  checkCurrentThreadStillWaitingOnAnotherLock();

  assert(currentThread->holding_lock_list_);
  if(currentThread->holding_lock_list_->hasNextOnHoldingList())
  {
    debug(LOCK, "Condition::wait: Warning: The thread %s (%p) is holding some locks when going to sleep on condition %s (%p).\n"
          "This may lower the performance of the system, and cause undetectable deadlocks!\n",
          currentThread->getName(), currentThread, getName(), this);
    printHoldingList(currentThread);


    // Holding locks that are not released by the CV when waiting on it is strongly discouraged.
    // It might not be a problem, but it is slow and WILL lead to hard-to-find deadlocks in most cases.
    // If you want to remove this assert, talk to your tutor FIRST!
    assert(!currentThread->holding_lock_list_->hasNextOnHoldingList() && "thread is holding unrelated locks while waiting on a condition variable");
  }
  lockWaitersList();
  last_accessed_at_ = called_by;
  // The mutex can be released here, because for waking up another thread, the list lock is needed, which is still held by the thread.
  mutex_->release(called_by);
  sleepAndRelease();
  // Thread has been woken up again
  currentThread->lock_waiting_on_ = 0;

  if(re_acquire_mutex)
  {
    assert(mutex_ && "Mutex pointer is null, maybe lock went out-of-scope or was deleted");
    mutex_->acquire(called_by);
  }
}

void Condition::signal(pointer called_by, bool broadcast)
{
  if(unlikely(system_state != RUNNING))
  {
    return;
  }
  if(!called_by)
  {
    called_by = getCalledBefore(1);
  }
//  debug(LOCK, "Condition::signal: Thread %s (%p) is signaling condition %s (%p).\n",
//        currentThread->getName(), currentThread, getName(), this);
//  debug(LOCK, "The signal is called by: ");
//  kernel_debug_info->printCallInformation(called_by);

  assert(mutex_->isHeldBy(currentThread));
  checkInterrupts("Condition::signal");
  do
  {
    lockWaitersList();
    last_accessed_at_ = called_by;
    Thread* thread_to_be_woken_up = popBackThreadFromWaitersList();
    unlockWaitersList();

    if(thread_to_be_woken_up)
    {
      //debug(LOCK, "Condition: Thread %s (%p) being signaled for condition %s (%p).\n",
      //      thread_to_be_woken_up->getName(), thread_to_be_woken_up, getName(), this);

      Scheduler::instance()->wake(thread_to_be_woken_up);
    }
    else
    {
      break;
    }
  } while (broadcast);
}

void Condition::broadcast(pointer called_by)
{
  signal(called_by, true);
}
