#include "SpinLock.h"
#include "kprintf.h"
#include "ArchThreads.h"
#include "ArchInterrupts.h"
#include "Scheduler.h"
#include "assert.h"
#include "Stabs2DebugInfo.h"
#include "backtrace.h"
extern Stabs2DebugInfo const *kernel_debug_info;

SpinLock::SpinLock(const char* name) :
  Lock::Lock(name), lock_(0)
{
}

bool SpinLock::acquireNonBlocking(pointer called_by)
{
  if(unlikely(system_state != RUNNING))
    return true;
  if(!called_by)
    called_by = getCalledBefore(1);
//  debug(LOCK, "Spinlock::acquireNonBlocking: Acquire spinlock %s (%p) with thread %s (%p)\n",
//        getName(), this, currentThread->getName(), currentThread);
//  if(kernel_debug_info)
//  {
//    debug(LOCK, "The acquire is called by: ");
//    kernel_debug_info->printCallInformation(called_by);
//  }

  // There may be some cases where the pre-checks may not be wished here.
  // But these cases are usually dirty implemented, and it would not be necessary to call this method there.
  // So in case you see this comment, re-think your implementation and don't just comment out this line!
  doChecksBeforeWaiting();

  if(ArchThreads::testSetLock(lock_, 1))
  {
    // The spinlock is held by another thread at the moment
    return false;
  }
  // The spinlock is now held by the current thread.
  assert(held_by_ == 0);
  last_accessed_at_ = called_by;
  held_by_ = currentThread;
  pushFrontToCurrentThreadHoldingList();
  return true;
}

void SpinLock::acquire(pointer called_by)
{
  if(unlikely(system_state != RUNNING))
    return;
  if(!called_by)
    called_by = getCalledBefore(1);
//  debug(LOCK, "Spinlock::acquire: Acquire spinlock %s (%p) with thread %s (%p)\n",
//        getName(), this, currentThread->getName(), currentThread);
//  if(kernel_debug_info)
//  {
//    debug(LOCK, "The acquire is called by: ");
//    kernel_debug_info->printCallInformation(called_by);
//  }
  if(ArchThreads::testSetLock(lock_, 1))
  {
    // We did not directly managed to acquire the spinlock, need to check for deadlocks and
    // to push the current thread to the waiters list.
    doChecksBeforeWaiting();

    currentThread->lock_waiting_on_ = this;
    lockWaitersList();
    pushFrontCurrentThreadToWaitersList();
    unlockWaitersList();

    // here comes the basic spinlock
    while(ArchThreads::testSetLock(lock_, 1))
    {
      //SpinLock: Simplest of Locks, do the next best thing to busy waiting
      Scheduler::instance()->yield();
    }
    // Now we managed to acquire the spinlock. Remove the current thread from the waiters list.
    lockWaitersList();
    removeCurrentThreadFromWaitersList();
    unlockWaitersList();
    currentThread->lock_waiting_on_ = 0;
  }
  // The current thread is now holding the spinlock
  last_accessed_at_ = called_by;
  held_by_ = currentThread;
  pushFrontToCurrentThreadHoldingList();
}

bool SpinLock::isFree()
{
  if(unlikely(ArchInterrupts::testIFSet() && Scheduler::instance()->isSchedulingEnabled()))
  {
    debug(LOCK, "SpinLock::isFree: ERROR: Should not be used with IF=1 AND enabled Scheduler, use acquire instead\n");
    assert(false);
  }
  return (lock_ == 0);
}

void SpinLock::release(pointer called_by)
{
  if(unlikely(system_state != RUNNING))
    return;
  if(!called_by)
    called_by = getCalledBefore(1);
//  debug(LOCK, "Spinlock::release: Release spinlock %s (%p) with thread %s (%p)\n",
//        getName(), this, currentThread->getName(), currentThread);
//  if(kernel_debug_info)
//  {
//    debug(LOCK, "The release is called by: ");
//    kernel_debug_info->printCallInformation(called_by);
//  }
  checkInvalidRelease("SpinLock::release");
  removeFromCurrentThreadHoldingList();
  last_accessed_at_ = called_by;
  held_by_ = 0;
  lock_ = 0;
}

