#include "FrameBufferConsole.h"
#include "ArchCommon.h"
#include "Terminal.h"
#include "KeyboardManager.h"
#include "kprintf.h"
#include "Scheduler.h"
#include "kstring.h"

FrameBufferConsole::FrameBufferConsole(uint32 num_terminals) :
    Console(num_terminals, "VESAConsoleThread")
{
  x_res_ = ArchCommon::getVESAConsoleWidth();
  y_res_ = ArchCommon::getVESAConsoleHeight();
  bits_per_pixel_ = ArchCommon::getVESAConsoleBitsPerPixel();
  bytes_per_pixel_ = bits_per_pixel_ / 8;

  uint32 i, j = 10, log = 1, k = 0, l = 0;

  while (num_terminals / j)
  {
    j *= 10;
    log++;
  }

  char term_name[log + 5];
  term_name[0] = 't';
  term_name[1] = 'e';
  term_name[2] = 'r';
  term_name[3] = 'm';
  term_name[log + 4] = 0;

  for (i = 0; i < num_terminals; ++i)
  {
    uint32 cterm = i;
    for (l = 4, k = j / 10; k > 0; k /= 10, l++)
    {
      term_name[l] = cterm / k + '0';
      cterm -= ((cterm / k) * k);
    }

    Terminal *term = new Terminal(term_name, this, consoleGetNumColumns(), consoleGetNumRows());
    terminals_.push_back(term);
  }

  active_terminal_ = 0;
  consoleClearScreen();
}

void FrameBufferConsole::consoleClearScreen()
{
  memset((void*) ArchCommon::getVESAConsoleLFBPtr(), 0, x_res_ * y_res_ * bytes_per_pixel_);
}

uint32 FrameBufferConsole::consoleGetNumRows() const
{
  return y_res_ / 16;
}

uint32 FrameBufferConsole::consoleGetNumColumns() const
{
  return x_res_ / 8;
}

extern uint8 fontdata_sun8x16[];

void FrameBufferConsole::setPixel(uint32 x, uint32 y, uint8 r, uint8 g, uint8 b)
{
  uint16 *lfb = (uint16*) ArchCommon::getVESAConsoleLFBPtr();
  uint32 offset = (x + y * x_res_);
  uint16 color = (b >> 3);
  color |= (g >> 2) << 5;
  color |= (r >> 3) << 11;

  lfb[offset] = color;
}

uint32 FrameBufferConsole::consoleSetCharacter(uint32 const &row, uint32 const&column, uint8 const &character,
                                               uint8 const &state)
{
  CONSOLECOLOR fg, bg;
  colorsFromState(state, fg, bg);
  uint16 bg_color = convertConsoleColor(bg);
  uint16 fg_color = convertConsoleColor(fg);
  uint32 i, k;
  uint32 character_index = character * 16;

  uint16 *lfb = (uint16*) ArchCommon::getVESAConsoleLFBPtr();

  uint32 top_left_pixel = column * 8 + row * 16 * x_res_;

  for (i = 0; i < 16; ++i)
  {

    for (k = 0; k < 8; ++k)
    {
      // find out the bit we want to draw
      uint8 temp = fontdata_sun8x16[character_index + i];
      temp &= 1 << (7 - k);
      if (temp)
      {
        lfb[top_left_pixel + k + i * x_res_] = fg_color;
      }
      else
      {
        lfb[top_left_pixel + k + i * x_res_] = bg_color;
      }
    }

  }

  return 0;
}

void FrameBufferConsole::consoleScrollUp(uint8 const &state)
{
  CONSOLECOLOR fg, bg;
  colorsFromState(state, fg, bg);
  uint16 bg_color = convertConsoleColor(bg);
  pointer fb = ArchCommon::getVESAConsoleLFBPtr();
  uint16* fb16 = (uint16*)fb;
  memmove((void*) fb, (void*) (fb + (consoleGetNumColumns() * bytes_per_pixel_ * 8 * 16)),
          (consoleGetNumRows() - 1) * consoleGetNumColumns() * bytes_per_pixel_ * 8 * 16);

  for(uint32 index = ((consoleGetNumRows() - 1) * consoleGetNumColumns() * bytes_per_pixel_ * 8 * 16) / 2;
      index < x_res_ * y_res_; index++)
  {
    fb16[index] = bg_color;
  }
}

uint16 FrameBufferConsole::convertConsoleColor(CONSOLECOLOR color)
{
  uint8 r = 0, g = 0, b = 0;
  switch(color)
  {
  case BLACK:
    r = 0; g = 0; b = 0;
    break;
  case BLUE:
    r = 0; g = 0; b = 255;
    break;
  case GREEN:
    r = 0; g = 200; b = 0;
    break;
  case CYAN:
    r = 0; g = 255; b = 255;
    break;
  case RED:
    r = 255; g = 0; b = 0;
    break;
  case MAGENTA:
    r = 255; g = 0; b = 255;
    break;
  case BROWN:
    r = 165; g = 42; b = 42;
    break;
  case WHITE:
    r = 245; g = 245; b = 245;
    break;
  case DARK_GREY:
    r = 169; g = 169; b = 169;
    break;
  case BRIGHT_BLUE:
    r = 144, g = 144; b = 238;
    break;
  case BRIGHT_GREEN:
    r = 144; g = 238; b = 144;
    break;
  case BRIGHT_CYAN:
    r = 224; g = 255; b = 255;
    break;
  case PINK:
    r = 255; g = 192; b = 203;
    break;
  case BRIGHT_MAGENTA:
    r = 255; g = 100; b = 255;
    break;
  case YELLOW:
    r = 255; g = 255; b = 0;
    break;
  case BRIGHT_WHITE:
    r = 255; g = 255; b = 255;
    break;
  }
  uint16 scaled_r = ((uint16)(r * 31)) / 255;
  uint16 scaled_g = ((uint16)(g * 63)) / 255;
  uint16 scaled_b = ((uint16)(b * 31)) / 255;
  return scaled_b | (scaled_g << 5) | (scaled_r << 11);
}

void FrameBufferConsole::colorsFromState(uint8 const &state, CONSOLECOLOR &fg, CONSOLECOLOR &bg)
{
  fg = (CONSOLECOLOR) (state & 15);
  bg = (CONSOLECOLOR) ((state & 240) >> 4);
}
