#include "TextConsole.h"
#include "Terminal.h"
#include "ArchCommon.h"
#include "panic.h"

#include "Scheduler.h"

#include "KeyboardManager.h"
#include "kprintf.h"
#include "kstring.h"

TextConsole::TextConsole(uint32 num_terminals) :
    Console(num_terminals, "TxTConsoleThread")
{
  uint32 i, j = 10, log = 1, k = 0, l = 0;

  while (num_terminals / j)
  {
    j *= 10;
    log++;
  }

  char term_name[log + 5];
  term_name[0] = 't';
  term_name[1] = 'e';
  term_name[2] = 'r';
  term_name[3] = 'm';
  term_name[log + 4] = 0;

  for (i = 0; i < num_terminals; ++i)
  {
    uint32 cterm = i;
    for (l = 4, k = j / 10; k > 0; k /= 10, l++)
    {
      term_name[l] = cterm / k + '0';
      cterm -= ((cterm / k) * k);
    }

    Terminal *term = new Terminal(term_name, this, consoleGetNumColumns(), consoleGetNumRows());
    terminals_.push_back(term);
  }

  active_terminal_ = 0;
}

uint32 TextConsole::consoleGetNumRows() const
{
  return 25;
}

uint32 TextConsole::consoleGetNumColumns() const
{
  return 80;
}

void TextConsole::consoleClearScreen()
{
  char *fb = (char*) ArchCommon::getFBPtr();
  uint32 i;
  for (i = 0; i < consoleGetNumRows() * consoleGetNumColumns() * 2; ++i)
  {
    fb[i] = 0;
  }
}

uint32 TextConsole::consoleSetCharacter(uint32 const &row, uint32 const&column, uint8 const &character,
                                        uint8 const &state)
{
  char *fb = (char*) ArchCommon::getFBPtr();
  fb[(column + row * consoleGetNumColumns()) * 2] = character;
  fb[(column + row * consoleGetNumColumns()) * 2 + 1] = state;

  return 0;
}


#define STAT_ROWS (1)

void TextConsole::consoleScrollUp(uint8 const &state)
{
  char* fb = (char*) ArchCommon::getFBPtr();
  memmove((void*) (fb + (consoleGetNumColumns() * 2 * STAT_ROWS)),
          (void*) (fb + (consoleGetNumColumns() * 2 * (STAT_ROWS + 1))),
          (consoleGetNumRows() - 1 + STAT_ROWS) * consoleGetNumColumns() * 2);
  for(size_t i = 0; i < consoleGetNumColumns(); i++)
  {
    fb[(i + (consoleGetNumRows() - 1) * consoleGetNumColumns()) * 2] = ' ';
    fb[(i + (consoleGetNumRows() - 1) * consoleGetNumColumns()) * 2 + 1] = state;
  }
}
