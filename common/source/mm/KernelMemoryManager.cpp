#include "KernelMemoryManager.h"
#include "ArchCommon.h"
#include "assert.h"
#include "debug_bochs.h"
#include "kprintf.h"
#include "debug.h"
#include "Scheduler.h"
#include "ArchInterrupts.h"
#include "ArchMemory.h"
#include "PageManager.h"
#include "kstring.h"
#include "Stabs2DebugInfo.h"
#include "backtrace.h"
#include "Thread.h"

extern Stabs2DebugInfo const* kernel_debug_info;

KernelMemoryManager kmm;

KernelMemoryManager * KernelMemoryManager::instance_;
size_t KernelMemoryManager::pm_ready_;

KernelMemoryManager* KernelMemoryManager::instance()
{
  if (unlikely(!instance_))
  {
    assert(false && "you can not use KernelMemoryManager::instance before the PageManager is ready!");
  }
  return instance_;
}

KernelMemoryManager::KernelMemoryManager(size_t min_heap_pages, size_t max_heap_pages) :
        tracing_(false), lock_("KMM::lock_"), segments_used_(0), segments_free_(0), approx_memory_free_(0)
{
  assert(instance_ == 0);
  instance_ = this;
  pointer start_address = ArchCommon::getFreeKernelMemoryStart();
  assert(((start_address) % PAGE_SIZE) == 0);
  base_break_ = start_address;
  kernel_break_ = start_address + min_heap_pages * PAGE_SIZE;
  reserved_min_ = min_heap_pages * PAGE_SIZE;
  reserved_max_ = max_heap_pages * PAGE_SIZE;
  debug(KMM, "Clearing initial heap pages\n");
  memset((void*)start_address, 0, min_heap_pages * PAGE_SIZE);
  first_ = (MallocSegment*)start_address;
  new ((void*)start_address) MallocSegment(0, 0, min_heap_pages * PAGE_SIZE - sizeof(MallocSegment), false);
  last_ = first_;
  debug(KMM, "KernelMemoryManager::ctor, Heap starts at %zx and initially ends at %zx\n", start_address, start_address + min_heap_pages * PAGE_SIZE);
}

pointer KernelMemoryManager::allocateMemory(size_t requested_size, pointer called_by)
{
  assert((requested_size & 0x80000000) == 0 && "requested too much memory");

  // 16 byte alignment
  requested_size = (requested_size + 0xF) & ~0xF;

  lockKMM();
  pointer ptr = private_AllocateMemory(requested_size, called_by);
  if (ptr)
    unlockKMM();

  debug(KMM, "allocateMemory returns address: %zx \n", ptr);
  return ptr;
}
pointer KernelMemoryManager::private_AllocateMemory(size_t requested_size, pointer called_by)
{
  assert(Thread::currentThreadIsStackCanaryOK() && "Kernel stack corruption detected.");

  assert((requested_size & 0xF) == 0 && "Attempt to allocate block with unaligned size");

  // find next free pointer of neccessary size + sizeof(MallocSegment);
  MallocSegment *new_pointer = findFreeSegment(requested_size);

  if (new_pointer == 0)
  {
    unlockKMM();
    kprintfd("KernelMemoryManager::allocateMemory: Not enough Memory left\n");
    kprintfd("Are we having a memory leak in the kernel??\n");
    kprintfd(
        "This might as well be caused by running too many threads/processes, which partially reside in the kernel.\n");
    assert(false && "Kernel Heap is out of memory");
    return 0;
  }

  fillSegment(new_pointer, requested_size);
  new_pointer->freed_at_ = 0;
  new_pointer->alloc_at_ = tracing_ ? called_by : 0;
  new_pointer->alloc_by_ = (pointer)currentThread;

  return ((pointer) new_pointer) + sizeof(MallocSegment);
}

bool KernelMemoryManager::freeMemory(pointer virtual_address, pointer called_by)
{
  assert(Thread::currentThreadIsStackCanaryOK() && "Kernel stack corruption detected.");

  if (virtual_address == 0 || virtual_address < ((pointer) first_) || virtual_address >= kernel_break_)
    return false;

  lockKMM();

  MallocSegment *m_segment = getSegmentFromAddress(virtual_address);
  if (not m_segment->markerOk())
  {
    unlockKMM();
    return false;
  }
  freeSegment(m_segment);
  if ((pointer)m_segment < kernel_break_ && m_segment->markerOk())
    m_segment->freed_at_ = called_by;

  unlockKMM();
  return true;
}

pointer KernelMemoryManager::reallocateMemory(pointer virtual_address, size_t new_size, pointer called_by)
{
  assert(Thread::currentThreadIsStackCanaryOK() && "Kernel stack corruption detected.");

  assert((new_size & 0x80000000) == 0 && "requested too much memory");
  if (new_size == 0)
  {
    //in case you're wondering: we really don't want to lock here yet :) guess why
    freeMemory(virtual_address, called_by);
    return 0;
  }
  //iff the old segment is no segment ;) -> we create a new one
  if (virtual_address == 0)
    return allocateMemory(new_size, called_by);

  // 16 byte alignment
  new_size = (new_size + 0xF) & ~0xF;

  lockKMM();

  MallocSegment *m_segment = getSegmentFromAddress(virtual_address);

  if (new_size == m_segment->getSize())
  {
    unlockKMM();
    return virtual_address;
  }

  if (new_size < m_segment->getSize())
  {
    fillSegment(m_segment, new_size, 0);
    unlockKMM();
    return virtual_address;
  }
  else
  {
    //maybe we can solve this the easy way...
    if (m_segment->next_ != 0)
      if (m_segment->next_->getUsed() == false && m_segment->next_->getSize() + m_segment->getSize() >= new_size)
      {
        mergeWithFollowingFreeSegment(m_segment);
        fillSegment(m_segment, new_size, 0);
        unlockKMM();
        return virtual_address;
      }

    //or not.. lets search for larger space

    //thx to Philipp Toeglhofer we are not going to deadlock here anymore ;)
    pointer new_address = private_AllocateMemory(new_size, called_by);
    if (new_address == 0)
    {
      //we are not freeing the old semgent in here, so that the data is not
      //getting lost, although we could not allocate more memory

      //just if you wonder: the KMM is already unlocked
      kprintfd("KernelMemoryManager::reallocateMemory: Not enough Memory left\n");
      kprintfd("Are we having a memory leak in the kernel??\n");
      kprintfd(
          "This might as well be caused by running too many threads/processes, which partially reside in the kernel.\n");
      assert(false && "Kernel Heap is out of memory");
      return 0;
    }
    memcpy((void*) new_address, (void*) virtual_address, m_segment->getSize());
    freeSegment(m_segment);
    if ((pointer)m_segment < kernel_break_ && m_segment->markerOk())
      m_segment->freed_at_ = called_by;
    unlockKMM();
    return new_address;
  }
}

MallocSegment *KernelMemoryManager::getSegmentFromAddress(pointer virtual_address)
{
  MallocSegment *m_segment;
  m_segment = (MallocSegment*) (virtual_address - sizeof(MallocSegment));
  assert(virtual_address != 0 && m_segment != 0 && "trying to access a nullpointer");
  assert(m_segment->markerOk() && "memory corruption - probably 'write after delete'");
  return m_segment;
}

MallocSegment *KernelMemoryManager::findFreeSegment(size_t requested_size)
{
  debug(KMM, "findFreeSegment: seeking memory block of bytes: %zd \n", requested_size + sizeof(MallocSegment));

  MallocSegment *current = first_;
  while (current != 0)
  {
    debug(KMM, "findFreeSegment: current: %p size: %zd used: %d \n", current, current->getSize() + sizeof(MallocSegment),
          current->getUsed());
    assert(current->markerOk() && "memory corruption - probably 'write after delete'");
    if ((current->getSize() >= requested_size) && (current->getUsed() == false))
      return current;

    current = current->next_;
  }
  // No free segment found, could we allocate more memory?
  if(last_->getUsed())
  {
    // In this case we have to create a new segment...
    MallocSegment* new_segment = new ((void*)ksbrk(sizeof(MallocSegment) + requested_size)) MallocSegment(last_, 0, requested_size, 0);
    last_->next_ = new_segment;
    last_ = new_segment;
  }
  else
  {
    // else we just increase the size of the last segment
    size_t needed_size = requested_size - last_->getSize();
    ksbrk(needed_size);
    last_->setSize(requested_size);
  }

  return last_;
}

void KernelMemoryManager::fillSegment(MallocSegment *this_one, size_t requested_size, uint32 zero_check)
{
  assert(this_one != 0 && "trying to access a nullpointer");
  assert(this_one->markerOk() && "memory corruption - probably 'write after delete'");
  assert(this_one->getSize() >= requested_size && "segment is too small for requested size");
  assert((requested_size & 0xF) == 0 && "Attempt to fill segment with unaligned size");

  uint32* mem = (uint32*) (this_one + 1);
  if (zero_check)
  {
    for (uint32 i = 0; i < requested_size / 4; ++i)
    {
      if(unlikely(mem[i] != 0))
      {

        kprintfd("KernelMemoryManager::fillSegment: WARNING: Memory not zero at %p (value=%x)\n", mem + i, mem[i]);
        if(this_one->freed_at_)
        {
          if(kernel_debug_info)
          {
            kprintfd("KernelMemoryManager::freeSegment: The chunk may previously be freed at: ");
            kernel_debug_info->printCallInformation(this_one->freed_at_);
          }
          assert(false);
        }
        mem[i] = 0;
      }
    }
  }

  size_t space_left = this_one->getSize() - requested_size;

  //size stays as it is, if there would be no more space to add a new segment
  this_one->setUsed(true);
  assert(this_one->getUsed() == true && "trying to fill an unused segment");

  //add a free segment after this one, if there's enough space
  if (space_left > sizeof(MallocSegment))
  {
    this_one->setSize(requested_size);
    assert(this_one->getSize() == requested_size && "size error");
    assert(this_one->getUsed() == true && "trying to fill an unused segment");

    MallocSegment *new_segment =
        new ((void*) (((pointer) this_one) + sizeof(MallocSegment) + requested_size)) MallocSegment(
            this_one, this_one->next_, space_left - sizeof(MallocSegment), false);
    if (this_one->next_ != 0)
    {
      assert(this_one->next_->markerOk() && "memory corruption - probably 'write after delete'");
      this_one->next_->prev_ = new_segment;
    }
    this_one->next_ = new_segment;

    if (new_segment->next_ == 0)
      last_ = new_segment;
  }
  debug(KMM, "fillSegment: filled memory block of bytes: %zd \n", this_one->getSize() + sizeof(MallocSegment));
}

void KernelMemoryManager::freeSegment(MallocSegment *this_one)
{
  debug(KMM, "KernelMemoryManager::freeSegment(%p)\n", this_one);
  assert(this_one != 0 && "trying to access a nullpointer");
  assert(this_one->markerOk() && "memory corruption - probably 'write after delete'");

  if (this_one->getUsed() == false)
  {
    kprintfd("KernelMemoryManager::freeSegment: FATAL ERROR\n");
    kprintfd("KernelMemoryManager::freeSegment: tried freeing not used memory block\n");
    if(this_one->freed_at_)
    {
      if(kernel_debug_info)
      {
        kprintfd("KernelMemoryManager::freeSegment: The chunk may previously be freed at: ");
        kernel_debug_info->printCallInformation(this_one->freed_at_);
      }
    }
    assert(false);
  }

  debug(KMM, "fillSegment: freeing block: %p of bytes: %zd \n", this_one, this_one->getSize() + sizeof(MallocSegment));

  this_one->setUsed(false);

  if (this_one->prev_ != 0)
  {
    assert(this_one->prev_->markerOk() && "memory corruption - probably 'write after delete'");
    if (this_one->prev_->getUsed() == false)
    {
      size_t my_true_size = (
          (this_one->next_ == 0) ? kernel_break_ - ((pointer) this_one) :
                                   ((pointer) this_one->next_) - ((pointer) this_one));

      MallocSegment *previous_one = this_one->prev_;

      previous_one->setSize(my_true_size + previous_one->getSize());
      previous_one->next_ = this_one->next_;
      if (this_one->next_ != 0)
      {
        assert(this_one->next_->markerOk() && "memory corruption - probably 'write after delete'");
        this_one->next_->prev_ = previous_one;
      }
      else
      {
    	  assert(this_one == last_ && "this should never happen, there must be a bug in KMM");
    	  last_ = previous_one;
      }

      debug(KMM, "freeSegment: post premerge, pre postmerge\n");
      debug(KMM, "freeSegment: previous_one: %p size: %zd used: %d\n", previous_one,
            previous_one->getSize() + sizeof(MallocSegment), previous_one->getUsed());
      debug(KMM, "freeSegment: this_one: %p size: %zd used: %d\n", this_one, this_one->getSize() + sizeof(MallocSegment),
            this_one->getUsed());

      this_one = previous_one;
    }
  }

  mergeWithFollowingFreeSegment(this_one);

  memset((void*) ((size_t) this_one + sizeof(MallocSegment)), 0, this_one->getSize()); // ease debugging

  // Change break if this is the last segment
  if(this_one == last_)
  {
    if(this_one != first_)
    {
      // Default case, there are three sub cases
      // 1. we can free the whole segment because it is above the reserved minimum
      // 2. we can not touch the segment because it is below the reserved minimum
      // 3. we can shrink the size of the segment because a part of it is above the reserved minimum
      if((size_t)this_one > base_break_ + reserved_min_)
      {
        // Case 1
        assert(this_one && this_one->prev_ && this_one->prev_->markerOk() && "memory corruption - probably 'write after delete'");
        this_one->prev_->next_ = 0;
        last_ = this_one->prev_;
        ksbrk(-(this_one->getSize() + sizeof(MallocSegment)));
      }
      else if((size_t)this_one + sizeof(MallocSegment) + this_one->getSize() <= base_break_ + reserved_min_)
      {
        // Case 2
        // This is easy, just relax and do nothing
      }
      else
      {
        // Case 3
        // First calculate the new size of the segment
        size_t segment_size = (base_break_ + reserved_min_) - ((size_t)this_one + sizeof(MallocSegment));
        // Calculate how much we have to sbrk
        ssize_t sub = segment_size - this_one->getSize();
        ksbrk(sub);
        this_one->setSize(segment_size);
      }
    }
    else
    {
      if((this_one->getSize() - reserved_min_))
      {
        ksbrk(-(this_one->getSize() - reserved_min_));
        this_one->setSize(reserved_min_);
      }
    }
  }

  {
    MallocSegment *current = first_;
    while (current != 0)
    {
      debug(KMM, "freeSegment: current: %p prev: %p next: %p size: %zd used: %d\n", current, current->prev_,
            current->next_, current->getSize() + sizeof(MallocSegment), current->getUsed());
      assert(current->markerOk() && "memory corruption - probably 'write after delete'");
      current = current->next_;
    }

  }
}

bool KernelMemoryManager::mergeWithFollowingFreeSegment(MallocSegment *this_one)
{
  assert(this_one != 0 && "trying to access a nullpointer");
  assert(this_one->markerOk() && "memory corruption - probably 'write after delete'");

  if (this_one->next_ != 0)
  {
    assert(this_one->next_->markerOk() && "memory corruption - probably 'write after delete'");
    if (this_one->next_->getUsed() == false)
    {
      MallocSegment *next_one = this_one->next_;
      size_t true_next_size = (
          (next_one->next_ == 0) ? kernel_break_ - ((pointer) next_one) :
                                   ((pointer) next_one->next_) - ((pointer) next_one));

      this_one->setSize(this_one->getSize() + true_next_size);
      this_one->next_ = next_one->next_;
      if (next_one->next_ != 0)
      {
        assert(next_one->next_->markerOk() && "memory corruption - probably 'write after delete'");
        next_one->next_->prev_ = this_one;
      }

      memset((void*) next_one, 0, sizeof(MallocSegment));

      //have to check again, could have changed...
      if (this_one->next_ == 0)
        last_ = this_one;

      return true;
    }
  }
  return false;
}

pointer KernelMemoryManager::ksbrk(ssize_t size)
{
  assert(base_break_ <= (size_t)kernel_break_ + size && "kernel heap break value corrupted");
  assert((reserved_max_ == 0 || ((kernel_break_ - base_break_) + size) <= reserved_max_) && "maximum kernel heap size reached");
  assert(DYNAMIC_KMM && "ksbrk should only be called if DYNAMIC_KMM is 1 - not in baseline SWEB");
  if(size != 0)
  {
    size_t old_brk = kernel_break_;
    size_t cur_top_vpn = kernel_break_ / PAGE_SIZE;
    if ((kernel_break_ % PAGE_SIZE) == 0)
      cur_top_vpn--;
    kernel_break_ = ((size_t)kernel_break_) + size;
    size_t new_top_vpn = (kernel_break_ )  / PAGE_SIZE;
    if ((kernel_break_ % PAGE_SIZE) == 0)
      new_top_vpn--;
    if(size > 0)
    {
      debug(KMM, "%zx != %zx\n", cur_top_vpn, new_top_vpn);
      while(cur_top_vpn != new_top_vpn)
      {
        debug(KMM, "%zx != %zx\n", cur_top_vpn, new_top_vpn);
        cur_top_vpn++;
        assert(pm_ready_ && "Kernel Heap should not be used before PageManager is ready");
        size_t new_page = PageManager::instance()->allocPPN();
        if(unlikely(new_page == 0))
        {
          kprintfd("KernelMemoryManager::freeSegment: FATAL ERROR\n");
          kprintfd("KernelMemoryManager::freeSegment: no more physical memory\n");
          assert(new_page != 0 && "Kernel Heap is out of memory");
        }
        debug(KMM, "kbsrk: map %zx -> %zx\n", cur_top_vpn, new_page);
        memset((void*)ArchMemory::getIdentAddressOfPPN(new_page), 0 , PAGE_SIZE);
        ArchMemory::mapKernelPage(cur_top_vpn, new_page);
      }

    }
    else
    {
      while(cur_top_vpn != new_top_vpn)
      {
        assert(pm_ready_ && "Kernel Heap should not be used before PageManager is ready");
        ArchMemory::unmapKernelPage(cur_top_vpn);
        cur_top_vpn--;
      }
    }
    return old_brk;
  }
  else
  {
    return kernel_break_;
  }
}

Thread* KernelMemoryManager::KMMLockHeldBy()
{
  return lock_.heldBy();
}

void KernelMemoryManager::lockKMM()
{
  assert((!(system_state == RUNNING) || PageManager::instance()->heldBy() != currentThread) && "You're abusing the PageManager lock");
  lock_.acquire(getCalledBefore(1));
}

void KernelMemoryManager::unlockKMM()
{
  assert((!(system_state == RUNNING) || PageManager::instance()->heldBy() != currentThread) && "You're abusing the PageManager lock");
  lock_.release(getCalledBefore(1));
}

SpinLock& KernelMemoryManager::getKMMLock()
{
  return lock_;
}

size_t KernelMemoryManager::getUsedKernelMemory(bool show_allocs = false) {
    MallocSegment *current = first_;
    size_t size = 0, blocks = 0, unused = 0;
    if(show_allocs) kprintfd("Kernel Memory Usage\n\n");
    while (current != 0)
    {
      if (current->getUsed()) {
        size += current->getSize();
        blocks++;
        if(current->alloc_at_ && show_allocs)
        {
            if(kernel_debug_info)
            {
                kprintfd("%8zu bytes (by %p) at: ", current->getSize(), (void*)current->alloc_by_);
                kernel_debug_info->printCallInformation(current->alloc_at_);
            }
        }
      } else {
          unused += current->getSize();
      }

      current = current->next_;
    }
    if(show_allocs) kprintfd("\n%zu bytes in %zu blocks are in use (%zu%%)\n", size, blocks, 100 * size / (size + unused));
    return size;
}

void KernelMemoryManager::startTracing() {
    tracing_ = true;
}

void KernelMemoryManager::stopTracing() {
    tracing_ = false;
}

pointer KernelMemoryManager::getKernelBreak() const {
  return kernel_break_;
}
