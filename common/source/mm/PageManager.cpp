#include "PageManager.h"
#include "new.h"
#include "offsets.h"
#include "paging-definitions.h"
#include "ArchCommon.h"
#include "ArchMemory.h"
#include "kprintf.h"
#include "Scheduler.h"
#include "KernelMemoryManager.h"
#include "assert.h"
#include "Bitmap.h"
#include "SwapThread.h"
#include "UserThread.h"

PageManager pm;

PageManager *PageManager::instance_ = nullptr;

PageManager *PageManager::instance()
{
  if (unlikely(!instance_))
    new (&pm) PageManager();
  return instance_;
}

PageManager::PageManager() : lock_("PageManager::lock_")
{
  assert(!instance_);
  instance_ = this;
  assert(!KernelMemoryManager::instance_);
  number_of_pages_ = 0;
  lowest_unreserved_page_ = 0;

  size_t num_mmaps = ArchCommon::getNumUseableMemoryRegions();

  size_t highest_address = 0, used_pages = 0;

  // Determine Amount of RAM
  for (size_t i = 0; i < num_mmaps; ++i)
  {
    pointer start_address = 0, end_address = 0, type = 0;
    ArchCommon::getUsableMemoryRegion(i, start_address, end_address, type);
    debug(PM, "Ctor: memory region from physical %zx to %zx (%zu bytes) of type %zd\n",
          start_address, end_address, end_address - start_address, type);

    if (type == 1)
      highest_address = Max(highest_address, end_address & 0x7FFFFFFF);
  }

  number_of_pages_ = highest_address / PAGE_SIZE;

  size_t boot_bitmap_size = Min(4096 * 8 * 2, number_of_pages_);
  uint8 page_usage_table[BITMAP_BYTE_COUNT(boot_bitmap_size)];
  used_pages = boot_bitmap_size;
  memset(page_usage_table, 0xFF, BITMAP_BYTE_COUNT(boot_bitmap_size));

  // mark as free, everything that might be useable
  for (size_t i = 0; i < num_mmaps; ++i)
  {
    pointer start_address = 0, end_address = 0, type = 0;
    ArchCommon::getUsableMemoryRegion(i, start_address, end_address, type);
    if (type != 1)
      continue;
    size_t start_page = start_address / PAGE_SIZE;
    size_t end_page = end_address / PAGE_SIZE;
    debug(PM, "Ctor: usable memory region: start_page: %zx, end_page: %zx, type: %zd\n", start_page, end_page, type);

    for (size_t k = Max(start_page, lowest_unreserved_page_); k < Min(end_page, number_of_pages_); ++k)
    {
      Bitmap::unsetBit(page_usage_table, used_pages, k);
    }
  }

  debug(PM, "Ctor: Marking pages used by the kernel as reserved\n");
  for (size_t i = ArchMemory::RESERVED_START; i < ArchMemory::RESERVED_END; ++i)
  {
    size_t physical_page = 0;
    size_t pte_page = 0;
    size_t this_page_size = ArchMemory::get_PPN_Of_VPN_In_KernelMapping(i, &physical_page, &pte_page);
    assert(this_page_size == 0 || this_page_size == PAGE_SIZE || this_page_size == PAGE_SIZE * PAGE_TABLE_ENTRIES);
    if (this_page_size > 0)
    {
      // our bitmap only knows 4k pages for now
      uint64 num_4kpages = this_page_size / PAGE_SIZE; // should be 1 on 4k pages and 1024 on 4m pages
      for (uint64 p = 0; p < num_4kpages; ++p)
      {
        if (physical_page * num_4kpages + p < number_of_pages_)
          Bitmap::setBit(page_usage_table, used_pages, physical_page * num_4kpages + p);
      }
      i += (num_4kpages - 1); //+0 in most cases

      if (num_4kpages == 1 && i % 1024 == 0 && pte_page < number_of_pages_)
        Bitmap::setBit(page_usage_table, used_pages, pte_page);
    }
  }

  debug(PM, "Ctor: Marking GRUB loaded modules as reserved\n");
  // LastbutNotLeast: Mark Modules loaded by GRUB as reserved (i.e. pseudofs, etc)
  for (size_t i = 0; i < ArchCommon::getNumModules(); ++i)
  {
    size_t start_page = (ArchCommon::getModuleStartAddress(i) & 0x7FFFFFFF) / PAGE_SIZE;
    size_t end_page = (ArchCommon::getModuleEndAddress(i) & 0x7FFFFFFF) / PAGE_SIZE;
    debug(PM, "Ctor: module: start_page: %zx, end_page: %zx\n", start_page, end_page);
    for (size_t k = Min(start_page, number_of_pages_); k <= Min(end_page, number_of_pages_ - 1); ++k)
    {
      Bitmap::setBit(page_usage_table, used_pages, k);
      if (ArchMemory::get_PPN_Of_VPN_In_KernelMapping(PHYSICAL_TO_VIRTUAL_OFFSET / PAGE_SIZE + k, 0, 0) == 0)
        ArchMemory::mapKernelPage(PHYSICAL_TO_VIRTUAL_OFFSET / PAGE_SIZE + k, k);
    }
  }

  size_t num_pages_for_bitmap = (number_of_pages_ / 8) / PAGE_SIZE + 1;
  assert(used_pages < number_of_pages_ / 2 && "No space for kernel heap!");

  HEAP_PAGES = number_of_pages_ / 2 - used_pages;
  if (HEAP_PAGES > 1024)
    HEAP_PAGES = 1024 + (HEAP_PAGES - Min(HEAP_PAGES, 1024)) / 8;

  size_t start_vpn = ArchCommon::getFreeKernelMemoryStart() / PAGE_SIZE;
  size_t free_page = 0;
  size_t temp_page_size = 0;
  size_t num_reserved_heap_pages = 0;
  for (num_reserved_heap_pages = 0; num_reserved_heap_pages < num_pages_for_bitmap || temp_page_size != 0 ||
                                    num_reserved_heap_pages < ((DYNAMIC_KMM || (number_of_pages_ < 512)) ? 0 : HEAP_PAGES);
       ++num_reserved_heap_pages)
  {
    while (!Bitmap::setBit(page_usage_table, used_pages, free_page))
      free_page++;
    if ((temp_page_size = ArchMemory::get_PPN_Of_VPN_In_KernelMapping(start_vpn, 0, 0)) == 0)
      ArchMemory::mapKernelPage(start_vpn, free_page++);
    start_vpn++;
  }

  extern KernelMemoryManager kmm;
  new (&kmm) KernelMemoryManager(num_reserved_heap_pages, HEAP_PAGES);
  page_usage_table_ = new Bitmap(number_of_pages_);

  for (size_t i = 0; i < boot_bitmap_size; ++i)
  {
    if (Bitmap::getBit(page_usage_table, i))
      page_usage_table_->setBit(i);
  }

  debug(PM, "Ctor: find lowest unreserved page\n");
  for (size_t p = 0; p < number_of_pages_; ++p)
  {
    if (!page_usage_table_->getBit(p))
    {
      lowest_unreserved_page_ = p;
      break;
    }
  }
  debug(PM, "Ctor: Physical pages - free: %zu used: %zu total: %u\n", page_usage_table_->getNumFreeBits(),
        page_usage_table_->getNumBitsSet(), number_of_pages_);
  assert(lowest_unreserved_page_ < number_of_pages_);

  debug(PM, "Clearing free pages\n");
  for (size_t p = lowest_unreserved_page_; p < number_of_pages_; ++p)
  {
    if (!page_usage_table_->getBit(p))
    {
      memset((void *)ArchMemory::getIdentAddressOfPPN(p), 0xFF, PAGE_SIZE);
    }
  }

  num_pages_for_user_ = DYNAMIC_KMM ? -1 : getNumFreePages();
  KernelMemoryManager::pm_ready_ = 1;
}

uint32 PageManager::getTotalNumPages() const
{
  return number_of_pages_;
}

uint32 PageManager::getNumFreePages() const
{
  return page_usage_table_->getNumFreeBits();
}

bool PageManager::reservePages(uint32 ppn, uint32 num)
{
  assert(lock_.heldBy() == currentThread);
  if (ppn < number_of_pages_ && !page_usage_table_->getBit(ppn))
  {
    if (num == 1 || reservePages(ppn + 1, num - 1))
    {
      page_usage_table_->setBit(ppn);
      return true;
    }
  }
  return false;
}

uint32 PageManager::allocPPN(uint32 page_size)
{
  uint32 p;
  uint32 found = 0;

  assert((page_size % PAGE_SIZE) == 0);

  lock_.acquire();

  for (p = lowest_unreserved_page_; !found && (p < number_of_pages_); ++p)
  {
    if ((p % (page_size / PAGE_SIZE)) != 0)
      continue;
    if (reservePages(p, page_size / PAGE_SIZE))
      found = p;
  }
  while ((lowest_unreserved_page_ < number_of_pages_) && page_usage_table_->getBit(lowest_unreserved_page_))
    ++lowest_unreserved_page_;

  lock_.release();

  assert(currentThread->holding_lock_list_ == nullptr && "Thread is holding locks while allocating memory pages!");

  ProcessRegistry::instance()->dedup_lock_.acquire();

  if (getNumFreePages() > 100 && getNumFreePages() < 500)
  {
    ProcessRegistry::instance()->dedup = true;
    ProcessRegistry::instance()->dedup_wait.signal();
  }
  ProcessRegistry::instance()->dedup_lock_.release();

  if (found == 0)
  {
    // assert(false && "PageManager::allocPPN: Out of memory / No more free physical pages");
    // swapping logic
    Request *new_Request = new Request;

    Condition *Swap_Request_Condvariable = new Condition(&Scheduler::instance()->swap_thread_.swap_request_mutex, "Swap_Request_Mutex");
    // Swap_Request_Condvariable_Lock->acquire();
    Scheduler::instance()->swap_thread_.swap_request_mutex.acquire();
    new_Request->type_ = SWAPOUT;
    new_Request->Swap_Request_Condvariable_ = Swap_Request_Condvariable;
    // new_Request->Swap_Request_Condvariable_Lock_ = Swap_Request_Condvariable_Lock;
    new_Request->process = ((UserThread *)currentThread)->process_;
    new_Request->vpn_ = 0;
    new_Request->now_free_ppn_ = 0;
    new_Request->finished = false;

    Scheduler::instance()->swap_thread_.Swap_Request_List.push_back(new_Request);
    Scheduler::instance()->swap_thread_.cond_swap_thread.signal();

    while (!new_Request->finished)
    {
      Swap_Request_Condvariable->wait();
    }

    Scheduler::instance()->swap_thread_.swap_request_mutex.release();

    delete Swap_Request_Condvariable;

    // Swap_Request_Condvariable_Lock->release();
    assert(new_Request->now_free_ppn_ != 0);
    if (new_Request->action == UNMAP_FREE)
    {
      ProcessRegistry::instance()->ipt_lock_.acquire();
      auto entry = ProcessRegistry::instance()->inverted_page_table[new_Request->now_free_ppn_];
      for (auto tuble : entry.tuples)
      {
        auto process = tuble.process;
        process->loader_->arch_memory_.archmemory_lock_.acquire();
        process->loader_->arch_memory_.unmapPage(tuble.vpn);
        process->loader_->arch_memory_.archmemory_lock_.release();
      }
      lock_.acquire();
      assert(page_usage_table_->getBit(new_Request->now_free_ppn_) == 0);
      lock_.release();
      ProcessRegistry::instance()->ipt_lock_.release();
      debug(PM, "unmapped page %d because it can be loaded from demand loader!\n", new_Request->now_free_ppn_);
    }

    if (new_Request->now_free_ppn_ != 0)
    {
      lock_.acquire();
      page_usage_table_->setBit(new_Request->now_free_ppn_);

      found = new_Request->now_free_ppn_;

      memset((void *)ArchMemory::getIdentAddressOfPPN(found), 0xFF, PAGE_SIZE);
      lock_.release();

      const char *page_ident_addr = (const char *)ArchMemory::getIdentAddressOfPPN(found);
      const char *page_modified = (const char *)memnotchr(page_ident_addr, 0xFF, page_size);
      if (page_modified)
      {
        debug(PM, "Detected use-after-free for PPN %x at offset %zx\n", found, page_modified - page_ident_addr);
        assert(!page_modified && "Page modified after free");
      }

      memset((void *)ArchMemory::getIdentAddressOfPPN(found), 0, page_size);
      delete new_Request;
      return found;
    }
    assert(false && "should never happen");
    return 0;
  }
  // [Memory check code follows]
  const char *page_ident_addr = (const char *)ArchMemory::getIdentAddressOfPPN(found);
  const char *page_modified = (const char *)memnotchr(page_ident_addr, 0xFF, page_size);
  if (page_modified)
  {
    debug(PM, "Detected use-after-free for PPN %x at offset %zx\n", found, page_modified - page_ident_addr);
    assert(!page_modified && "Page modified after free");
  }

  memset((void *)ArchMemory::getIdentAddressOfPPN(found), 0, page_size);
  return found;
}

void PageManager::freePPN(uint32 page_number, uint32 page_size)
{
  assert((page_size % PAGE_SIZE) == 0);
  if (page_number > getTotalNumPages())
  {
    debug(PM, "Tried to free PPN %u (=%x)\n", page_number, page_number);
    assert(false && "PPN to be freed is out of range");
  }
  memset((void *)ArchMemory::getIdentAddressOfPPN(page_number), 0xFF, page_size);

  lock_.acquire();
  if (page_number < lowest_unreserved_page_)
    lowest_unreserved_page_ = page_number;
  for (uint32 p = page_number; p < (page_number + page_size / PAGE_SIZE); ++p)
  {
    // for debug
    if (page_usage_table_->getBit(p) == false)
    {
      debug(PM, "error");
    }
    if (!page_usage_table_->getBit(p))
    {
      debug(LOADER, "double free on ppn :%d", page_number);
    }
    assert(page_usage_table_->getBit(p) && "Double free PPN");
    page_usage_table_->unsetBit(p);
  }
  lock_.release();
}

void PageManager::printBitmap()
{
  page_usage_table_->bmprint();
}

uint32 PageManager::getNumPagesForUser() const
{
  return num_pages_for_user_;
}

void PageManager::allocatePagesMapPage(int &counter, ustl::queue<size_t> &ppns, size_t vpn, ArchMemory &arch_memory_)
{
  while (true)
  {
    ProcessRegistry::instance()->ipt_lock_.acquire();
    arch_memory_.archmemory_lock_.acquire();

    counter = arch_memory_.determineNeededPagesAllocPpn(vpn);

    if ((unsigned int)counter == ppns.size())
    {
      debug(PAGEFAULT, "counted to same number\n");
      break;
    }

    arch_memory_.archmemory_lock_.release();
    ProcessRegistry::instance()->ipt_lock_.release();

    while (ppns.size() > 0)
    {
      size_t ppn = ppns.front();
      PageManager::instance()->freePPN(ppn);
      ppns.pop();
    }

    for (int i = 0; i < counter; i++)
    {
      ppns.push(PageManager::instance()->allocPPN());
    }
  }
}