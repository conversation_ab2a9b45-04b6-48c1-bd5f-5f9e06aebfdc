#include "PageFaultHandler.h"
#include "kprintf.h"
#include "Thread.h"
#include "ArchInterrupts.h"
#include "offsets.h"
#include "Scheduler.h"
#include "Loader.h"
#include "Syscall.h"
#include "ArchThreads.h"
#include "UserThread.h"
#include "PageManager.h"
#include "ProcessRegistry.h"

#include "UserThread.h"
#include "PageManager.h"
#include "VfsSyscall.h"
#include "File.h"
extern "C" void arch_contextSwitch();

const size_t PageFaultHandler::null_reference_check_border_ = PAGE_SIZE;

inline bool PageFaultHandler::checkStackValidity(size_t address)
{
  auto userthread = (UserThread *)currentThread;
  if (address <= userthread->stack_start_ && address >= userthread->stack_end_)
  {
    return true;
  }
  debug(PAGEFAULT, "Could not find stack for adress %p\n", address);
  return false;
}

inline void PageFaultHandler::allocStackPage(size_t address)
{
  auto userthread = (UserThread *)currentThread;
  // for (auto thread : userthread->process_->userthreads_)
  //{
  if (address <= userthread->stack_start_ && address >= userthread->stack_end_)
  {
    size_t page_num = (userthread->stack_start_ - address) / PAGE_SIZE;

    

    debug(PAGEFAULT, "mapping to %p\n", (void *)(userthread->stack_start_ - page_num * PAGE_SIZE));
    debug(PAGEFAULT, "page: %ld should be:%ld\n", userthread->stack_start_ / PAGE_SIZE - page_num, address / PAGE_SIZE);
    size_t ppn = PageManager::instance()->allocPPN();
  int counter = -1;
  ustl::queue<size_t> ppns;
  PageManager::instance()->allocatePagesMapPage(counter,ppns,userthread->stack_start_ / PAGE_SIZE - page_num,currentThread->loader_->arch_memory_);

    bool vpn_mapped = currentThread->loader_->arch_memory_.mapPage(userthread->stack_start_ / PAGE_SIZE - page_num,ppns,ppn,  1);
    if (vpn_mapped == false)
    {
      debug(PAGEFAULT, "Could not map %p - is now beeing freed", ppn);
     
      PageManager::instance()->freePPN(ppn);
    }
    assert(ppns.size() ==0);
    // PoC: this assert may trigger by accident and assert the kernel => just remove and free the page if we couldn't map it
    // assert(vpn_mapped && "Virtual page for stack was already mapped - this should never happen");

    userthread->stack_bits[page_num] = true;
    currentThread->loader_->arch_memory_.archmemory_lock_.release();
    ProcessRegistry::instance()->ipt_lock_.release();
    return;
  }
  //}
}

inline bool PageFaultHandler::checkPageFaultIsValid(size_t address, bool user,
                                                    bool present, bool switch_to_us)
{
  assert((user == switch_to_us) && "Thread is in user mode even though is should not be.");
  assert(!(address < USER_BREAK && currentThread->loader_ == 0) && "Thread accesses the user space, but has no loader.");
  assert(!(user && currentThread->user_registers_ == 0) && "Thread is in user mode, but has no valid registers.");

  if (address < null_reference_check_border_)
  {
    debug(PAGEFAULT, "Maybe you are dereferencing a null-pointer. Adress \n");
  }
  else if (!user && address >= USER_BREAK)
  {
    debug(PAGEFAULT, "You are accessing an invalid kernel address.\n");
  }
  else if (user && address >= USER_BREAK)
  {
    debug(PAGEFAULT, "You are accessing a kernel address in user-mode.\n");
  }
  else if (present)
  {
    debug(PAGEFAULT, "You got a pagefault even though the address is mapped.\n");

    UserThread *userthread = (UserThread *)currentThread;
    auto mapping = userthread->loader_->arch_memory_.resolveMapping(address / PAGE_SIZE);
    debug(PAGEFAULT, "writeable bit: %d cow bit: %d \n", mapping.pt[mapping.pti].writeable, mapping.pt[mapping.pti].cow);
    if (mapping.pt[mapping.pti].cow && mapping.pt[mapping.pti].writeable == 0)
    {
      return true;
    }
  }
  else
  {
    // everything seems to be okay
    return true;
  }
  return false;
}

inline void PageFaultHandler::handlePageFault(size_t address, bool user,
                                              bool present, bool writing,
                                              bool fetch, bool switch_to_us)
{
  if (PAGEFAULT & OUTPUT_ENABLED)
    kprintfd("\n");
  debug(PAGEFAULT, "Address: %18zx - Thread %zu: %s (%p)\n",
        address, currentThread->getTID(), currentThread->getName(), currentThread);
  debug(PAGEFAULT, "Flags: %spresent, %s-mode, %s, %s-fetch, switch to userspace: %1d\n",
        present ? "    " : "not ",
        user ? "  user" : "kernel",
        writing ? "writing" : "reading",
        fetch ? "instruction" : "    operand",
        switch_to_us);

  
  ArchThreads::printThreadRegisters(currentThread, false);
  UserThread *userthread = (UserThread *)currentThread;

  
  if (checkPageFaultIsValid(address, user, present, switch_to_us))
  {
    
    size_t swap_in_ppn = PageManager::instance()->allocPPN();
    Scheduler::instance()->swap_thread_.swap_request_mutex.acquire();
    userthread->loader_->arch_memory_.archmemory_lock_.acquire();
    auto mapping = currentThread->loader_->arch_memory_.resolveMapping(address / PAGE_SIZE);
    if (mapping.pt)
    {
      debug(PAGEFAULT, "swapped %d, present %d, cow %d, dirty %d, stored %d\n",  mapping.pt[mapping.pti].swapped,  mapping.pt[mapping.pti].present,  mapping.pt[mapping.pti].cow,  mapping.pt[mapping.pti].dirty,  mapping.pt[mapping.pti].stored);
      debug(PAGEFAULT, "Process %d , Thread %d\n",((UserThread*)currentThread)->process_->pid_, currentThread->getTID());
    }

    // swap in
    if (mapping.pt && mapping.pt[mapping.pti].swapped == 1 && mapping.pt[mapping.pti].present == 0)
    {
      userthread->loader_->arch_memory_.archmemory_lock_.release();
      swapInPage(address,swap_in_ppn);
      ProcessRegistry::instance()->ppns_second_chance.push_back(swap_in_ppn);
      Scheduler::instance()->swap_thread_.swap_request_mutex.release();
      return;
    }
    PageManager::instance()->freePPN(swap_in_ppn);
     userthread->loader_->arch_memory_.archmemory_lock_.release();
      Scheduler::instance()->swap_thread_.swap_request_mutex.release();
    // cow
    uint64 cow_page_ppn = PageManager::instance()->allocPPN(); 
    ProcessRegistry::instance()->ipt_lock_.acquire();
    userthread->loader_->arch_memory_.archmemory_lock_.acquire();
    mapping = currentThread->loader_->arch_memory_.resolveMapping(address / PAGE_SIZE);

    if (present && mapping.pt[mapping.pti].cow && mapping.pt[mapping.pti].writeable == 0)
    {
      debug(PAGEFAULT, "Cow Page\n");
      mapping = userthread->loader_->arch_memory_.resolveMapping(address / PAGE_SIZE);

      //ProcessRegistry::instance()->ipt_lock_.acquire();
      if (ProcessRegistry::instance()->inverted_page_table[mapping.page_ppn].tuples.size() > 1)
      {
        // auto ppn = PageManager::instance()->allocPPN();
        assert(cow_page_ppn != NULL && "page cannot be null");
        debug(PAGEFAULT, "copy Page for cow\n");

        char *new_page = (char *)userthread->loader_->arch_memory_.getIdentAddressOfPPN(cow_page_ppn);
        char *shared_page = (char *)userthread->loader_->arch_memory_.getIdentAddressOfPPN(mapping.page_ppn);

        memcpy((void *)new_page, (void *)shared_page, PAGE_SIZE);
        auto page_table_entry = &mapping.pt[mapping.pti];
        debug(PAGEFAULT, "page ppn: %d cow:%d\n", page_table_entry->page_ppn, page_table_entry->cow);

        page_table_entry->page_ppn = cow_page_ppn;
        page_table_entry->writeable = 1;
        page_table_entry->cow = 0;
        page_table_entry->dirty = 1;
        page_table_entry->stored = 0;
        debug(PAGEFAULT, "new ppn: %d cow:%d\n", page_table_entry->page_ppn, page_table_entry->cow);

        ProcessRegistry::instance()->deleteFromIPT(mapping.page_ppn, address / PAGE_SIZE, userthread->process_->pid_);
        debug(IPT, "Ereased entry from IPT. Entry: processID: %d, PPN: %d \n", userthread->process_->pid_, mapping.page_ppn);
        size_t spn = ProcessRegistry::instance()->inverted_page_table[mapping.page_ppn].spn;
        Scheduler::instance()->swap_thread_.swap_Table_.removeReference(spn,userthread->process_->pid_);
        debug(USERPROCESS, "page_ ppn:%d  removed from swaptable by process %d!\n", mapping.page_ppn, userthread->process_->pid_);
        ProcessRegistry::instance()->addToIPT(cow_page_ppn, address / PAGE_SIZE, userthread->process_, PAGE);
        ProcessRegistry::instance()->ppns_second_chance.push_back(cow_page_ppn);

        debug(IPT, "Added entry to IPT. Entry: processID: %d, PPN: %d \n", userthread->process_->pid_, cow_page_ppn);
        // userthread->pysical_page = ppn;
      }
      else if (ProcessRegistry::instance()->inverted_page_table[mapping.page_ppn].tuples.size() == 1)
      {
        auto mapping = userthread->loader_->arch_memory_.resolveMapping(address / PAGE_SIZE);
        debug(PAGEFAULT, "reuse original page %d\n", mapping.page_ppn);
        auto page_table_entry = &mapping.pt[mapping.pti];
        page_table_entry->cow = 0;
        page_table_entry->writeable = 1;
        PageManager::instance()->freePPN(cow_page_ppn);
      }

      //ProcessRegistry::instance()->ipt_lock_.release();
      userthread->loader_->arch_memory_.archmemory_lock_.release();
      ProcessRegistry::instance()->ipt_lock_.release();
      return;
    }
    PageManager::instance()->freePPN(cow_page_ppn);
    userthread->loader_->arch_memory_.archmemory_lock_.release();
    ProcessRegistry::instance()->ipt_lock_.release();


    //heap
    ustl::queue<size_t> ppns;
      
    

    size_t ppn = PageManager::instance()->allocPPN();
    userthread->process_->program_break_lock.acquire();
    if(address >= userthread->process_->initial_program_break &&  address <= userthread->process_->current_program_break)
    {   
       userthread->process_->program_break_lock.release();

    int counter = -1;
    ustl::queue<size_t> ppns;
    PageManager::instance()->allocatePagesMapPage(counter,ppns, address/ PAGE_SIZE,userthread->process_->loader_->arch_memory_);
    
     
      bool vpn_mapped = currentThread->loader_->arch_memory_.mapPage(address / PAGE_SIZE, ppns, ppn, 1);
      if(!vpn_mapped)
      {
        PageManager::instance()->freePPN(ppn);
      }
      assert(  ppns.size() ==0);
      userthread->loader_->arch_memory_.archmemory_lock_.release();
      ProcessRegistry::instance()->ipt_lock_.release();
      //userthread->process_->program_break_lock.release();
      return;
    }
  
    userthread->process_->program_break_lock.release();
    
    
      for(size_t i =0; i <3 ; i++)
      {
        ppns.push(PageManager::instance()->allocPPN());
      }

    userthread->process_->shared_memory_break_lock.acquire();
    if (address >= userthread->process_->shared_memory_start && address < userthread->process_->shared_memory_break)
    {
      ProcessRegistry::instance()->shm_vector_lock.acquire();
      auto shm_obj = ProcessRegistry::instance()->getShmObjByAdress(userthread->process_, address);

      if (shm_obj != NULL)
      {
        debug(PAGEFAULT, "found shm object");
        MemoryMappedRegion *memory_region = shm_obj->getMemoryMapping(userthread->process_, address);
        assert(memory_region != NULL);

        size_t index = (address - memory_region->start_adress) / PAGE_SIZE;
        off_t offset = memory_region->offset_;

       if(shm_obj->mapping_index_vpn_ppn.find(index) == shm_obj->mapping_index_vpn_ppn.end())
       {
         debug(PAGEFAULT, "alloc new ppn");

        
        size_t fd = userthread->process_->getGlobalFdNum(memory_region->fd_);
       
         if((int)fd != -1) 
            {  
              VfsSyscall::lseek(fd,index*PAGE_SIZE+offset,SEEK_SET);
              VfsSyscall::read(fd,(char*)userthread->loader_->arch_memory_.getIdentAddressOfPPN(ppn),4096);
            }
        
        ProcessRegistry::instance()->ipt_lock_.acquire();
        userthread->loader_->arch_memory_.archmemory_lock_.acquire();
        bool vpn_mapped = currentThread->loader_->arch_memory_.mapPage(address/PAGE_SIZE,ppns,ppn, 1);
        if(!vpn_mapped)
        {
          PageManager::instance()->freePPN(ppn);
        }
      
        assert(vpn_mapped && "Virtual page for heap was already mapped - this should never happen");
        auto mapping = userthread->process_->loader_->arch_memory_.resolveMapping(address/ PAGE_SIZE);
        PageTableEntry *pt_entry = &mapping.pt[mapping.pti];
        userthread->process_->setPermissionbitsMmap(pt_entry,memory_region->permission_bits,1);
        shm_obj->mapping_index_vpn_ppn.insert({index,ppn});
        userthread->process_->loader_->arch_memory_.archmemory_lock_.release();
        ProcessRegistry::instance()->ipt_lock_.release();
        ProcessRegistry::instance()->shm_vector_lock.release();
        userthread->process_->shared_memory_break_lock.release();

      while (ppns.size() >0)
      {
        PageManager::instance()->freePPN(ppns.front());
        ppns.pop();
      }

        assert(ppns.size() ==0);

        return;
       }
       else
       {
        while (ppns.size() >0)
        {
          PageManager::instance()->freePPN(ppns.front());
          ppns.pop();
        }

        assert(ppns.size() ==0);

         PageManager::instance()->freePPN(ppn);
        debug(PAGEFAULT, "using index");
        auto ppn= shm_obj->mapping_index_vpn_ppn[index];
        
        ProcessRegistry::instance()->ipt_lock_.acquire();
        userthread->loader_->arch_memory_.archmemory_lock_.acquire();
        bool vpn_mapped = currentThread->loader_->arch_memory_.mapPage(address/PAGE_SIZE, ppns,ppn, 1);
        assert(vpn_mapped && "Virtual page for heap was already mapped - this should never happen");
        auto mapping = userthread->process_->loader_->arch_memory_.resolveMapping(address/ PAGE_SIZE);
        PageTableEntry *pt_entry = &mapping.pt[mapping.pti];
        userthread->process_->setPermissionbitsMmap(pt_entry,memory_region->permission_bits,1);
        userthread->process_->loader_->arch_memory_.archmemory_lock_.release();
         ProcessRegistry::instance()->ipt_lock_.release();
        ProcessRegistry::instance()->shm_vector_lock.release();
        userthread->process_->shared_memory_break_lock.release();
        
         return;

       }
       
 
       }
       
       
       MemoryMappedRegion* memory_region = userthread->process_->getMemoryMappedRegion(address);
       if(memory_region != NULL)
       {  
          ProcessRegistry::instance()->ipt_lock_.acquire();
          userthread->loader_->arch_memory_.archmemory_lock_.acquire();
          bool vpn_mapped = currentThread->loader_->arch_memory_.mapPage(address/PAGE_SIZE, ppns,ppn, 1);
          assert(vpn_mapped && "Virtual page for heap was already mapped - this should never happen");
          auto mapping = userthread->process_->loader_->arch_memory_.resolveMapping(address/ PAGE_SIZE);
          PageTableEntry *pt_entry = &mapping.pt[mapping.pti];
          userthread->process_->setPermissionbitsMmap(pt_entry,memory_region->permission_bits,0);
          if(memory_region->fd_ > 0)
          {
            

           size_t fd=userthread->process_->getGlobalFdNum(memory_region->fd_);
               
            if((int)fd != -1) 
            { 
             size_t index = (address -memory_region->start_adress)/PAGE_SIZE;
             off_t offset  = memory_region->offset_;
              debug(PAGEFAULT,"%d  %d\n",memory_region->start_adress/PAGE_SIZE, address/PAGE_SIZE );
              debug(PAGEFAULT,"%d\n", index);
              
              VfsSyscall::lseek(fd,index*PAGE_SIZE+offset,SEEK_SET);
              VfsSyscall::read(fd,(char*)userthread->loader_->arch_memory_.getIdentAddressOfPPN(ppn),4096);
            }

          }

          userthread->process_->loader_->arch_memory_.archmemory_lock_.release();
           ProcessRegistry::instance()->ipt_lock_.release();
          
          ProcessRegistry::instance()->shm_vector_lock.release();
          userthread->process_->shared_memory_break_lock.release();
          while (ppns.size() >0)
          {
            PageManager::instance()->freePPN(ppns.front());
            ppns.pop();
          }

        return;
      }

      ProcessRegistry::instance()->shm_vector_lock.release();
    }

    userthread->process_->shared_memory_break_lock.release();
    
     while (ppns.size() >0)
    {
      PageManager::instance()->freePPN(ppns.front());
       ppns.pop();
    }
    
  

    PageManager::instance()->freePPN(ppn);
   
    if (currentThread->type_ == Thread::USER_THREAD)
    {
      if (address >= USER_BREAK / 2)
      {
        debug(PAGEFAULT, "Trying to access address in stack-range\n");
        if (checkStackValidity(address))
        {
          allocStackPage(address);
          return;
        }
        else
        {
          // assert(false && "out of valid stack area ");
          Syscall::exit(9999);
        }
      }
    }
    currentThread->loader_->loadPage(address);
  }
  else
  {
    
    // the page-fault seems to be faulty, print out the thread stack traces
    ArchThreads::printThreadRegisters(currentThread, true);
    currentThread->printBacktrace(true);
    // debug(PAGEFAULT,"faulty adress %p\n",address);
    // assert(false && "faulty pagefault ");
    if (currentThread->loader_)
      Syscall::exit(9999);
    else
      currentThread->kill();
  }
  debug(PAGEFAULT, "Page fault handling finished for Address: %18zx.\n", address);
}

void PageFaultHandler::enterPageFault(size_t address, bool user,
                                      bool present, bool writing,
                                      bool fetch)
{
  assert(currentThread && "You have a pagefault, but no current thread");
  // save previous state on stack of currentThread
  uint32 saved_switch_to_userspace = currentThread->switch_to_userspace_;

  currentThread->switch_to_userspace_ = 0;
  currentThreadRegisters = currentThread->kernel_registers_;
  ArchInterrupts::enableInterrupts();

  handlePageFault(address, user, present, writing, fetch, saved_switch_to_userspace);

  ArchInterrupts::disableInterrupts();
  currentThread->switch_to_userspace_ = saved_switch_to_userspace;
  if (currentThread->switch_to_userspace_)
    currentThreadRegisters = currentThread->user_registers_;
}

void PageFaultHandler::swapInPage(size_t address, size_t ppn)
{
  auto userthread = (UserThread *)currentThread;
  auto process = userthread->process_;

  Request *new_request = new Request;

  //Mutex *Swap_Request_Condvariable_Lock = new Mutex("Swap_Request_Mutex");
  Condition *Swap_Request_Condvariable = new Condition(&Scheduler::instance()->swap_thread_.swap_request_mutex, "Swap_Request_Mutex");

  new_request->type_ = SWAPIN;
  new_request->vpn_ = address / PAGE_SIZE;
  
  
  new_request->Swap_Request_Condvariable_ = Swap_Request_Condvariable;
  //new_request->Swap_Request_Condvariable_Lock_ = Swap_Request_Condvariable_Lock;
  new_request->process = process;
  new_request->now_free_ppn_ = 0;
  new_request->ppn_for_swap_in = ppn;
  new_request->finished=false;

  Scheduler::instance()->swap_thread_.Swap_Request_List.push_back(new_request);
  Scheduler::instance()->swap_thread_.cond_swap_thread.signal();


    while (!new_request->finished)
    {
      Swap_Request_Condvariable->wait();
    }

    delete Swap_Request_Condvariable;
    delete new_request;
  //Swap_Request_Condvariable_Lock->release();
}
