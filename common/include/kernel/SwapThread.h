#pragma once
#include "types.h"
#include "Thread.h"
#include "uvector.h"
#include "Condition.h"
#include "paging-definitions.h"
#include "Bitmap.h"
#include "BDManager.h"
#include "BDVirtualDevice.h"
#include "SwapTable.h"
#include "UserProcess.h"
#include "Loader.h"
#include "Mutex.h"

enum RequestType{SWAPIN, SWAPOUT};
enum Action{NONE, UNMAP_FREE};

struct Request{
    size_t vpn_;
    RequestType type_;
    Condition* Swap_Request_Condvariable_ ;
    Mutex* Swap_Request_Condvariable_Lock_;
    UserProcess* process;
    size_t now_free_ppn_;
    size_t ppn_for_swap_in;
    bool  finished;
    Action action = NONE;
    };

class SwapThread : public Thread
{
  public:
    SwapThread();

    void swapOut(Request* request);
    void swapIn(Request* request);
    bool swappablePageCheck(uint64 page_index);
    size_t randomPRA();
    size_t NFU_PRA();
    size_t SecondChancePRA();

   //ustl::queue<Request*> Swap_Out_Request_List;
   //ustl::queue<Request*> Swap_In_Request_List;
    ustl::vector<Request*> Swap_Request_List;
    Mutex swap_request_mutex;
    Condition cond_swap_thread;
    Bitmap* Swap_Bitmap;
    size_t random_pra_seed;
    BDVirtualDevice* Device;
   

    virtual void Run();

    SwapTable swap_Table_;
    
};