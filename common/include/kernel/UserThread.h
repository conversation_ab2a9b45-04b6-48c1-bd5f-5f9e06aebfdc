#pragma once

#include "Thread.h"
#include "UserProcess.h"
#include "Condition.h"

class UserThread : public Thread
{
public:
    UserProcess* process_;
    uint32_t terminal_number_;
    size_t arg_;
    bool waiting_on_other_thread;
    size_t waiting_thread_id;
    Condition* wait_for_thread;
    bool delete_old_loader;
    bool waiting_on_process;
    size_t waiting_pid;
    Condition* wait_for_process;
    bool stack_bits[21];
    size_t stack_start_;
    size_t stack_end_;


    UserThread(UserProcess* process, ustl::string filename, size_t startroutine, size_t arg, uint32_t terminal_number, size_t wrapper);
    UserThread(UserThread const &other, UserProcess* new_process);
    virtual ~UserThread();
    virtual void Run(); //not used
    bool checkExecparameters(size_t path, char** argv,size_t& argc);
    void pollPageTablesUpdateNFU();
    char** copyStringsOnPage(size_t argc,char** argv);
    int  join_thread(size_t thread_id,size_t& ret_val);

};