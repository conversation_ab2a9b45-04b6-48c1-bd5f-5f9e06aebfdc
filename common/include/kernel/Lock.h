#pragma once

#include "types.h"

class Thread;

/**
 * This call represents the locks which are used to synchronize the kernel.
 */
class Lock
{
public:
  friend class Scheduler;

  Lock(const char* name);

  /**
   * Destructor of the lock.
   * Some checks are done within there, so it can be assured that no
   * threads are waiting/holding this lock when it is destroyed.
   */
  virtual ~Lock();

  /**
   * Print out the list of threads waiting on the current thread.
   * Be careful when printing out this, it may happen that some threads are
   * removed from the waiters list. In this case, the behaviour is undefined.
   * (in case the current thread is not holding/locking the lock).
   */
  void printWaitersList();

  /**
   * Print out the list of locks which are held by a thread (time desc order).
   * Be careful when printing out the list of another thread (not the current thread).
   * You have to ensure that the thread cannot continue meanwhile, else it may happen
   * that he is modifying the list... That may be bad (and probably even crush the system).
   * @param thread The thread which is holding the locks.
   */
  static void printHoldingList(Thread* thread);

  Thread* heldBy() const
  {
    return held_by_;
  }

  bool isHeldBy(Thread* thread) const
  {
    return (held_by_ == thread);
  }

  inline const char* getName() const
  {
    return name_;
  }

  inline bool hasNextOnHoldingList() const
  {
    return next_lock_on_holding_list_;
  }

protected:

  /**
   * The thread which is holding this lock at the moment.
   */
  Thread* held_by_;

  /**
   * The single chained holding list of the thread.
   * It references to the next lock which is held by the thread which is holding this lock.
   */
  Lock* next_lock_on_holding_list_;

  /**
   * The call point which used the specified lock the last time.
   */
  pointer last_accessed_at_;

  /**
   * Remove the current thread from the holding list.
   */
  void removeCurrentThreadFromWaitersList();

  inline bool threadsAreOnWaitersList() const
  {
    return waiters_list_;
  }

  /**
   * Push the lock onto the holding lst of the current thread.
   */
  void pushFrontToCurrentThreadHoldingList();

  /**
   * Remove the lock from the holding list of the current thread.
   */
  void removeFromCurrentThreadHoldingList();

  /**
   * Check if a deadlock would happen in case the current thread would wait for this lock.
   * The circular check is done within this lock.
   */
  void checkForDeadLock();

  /**
   * Check if the current thread wants to wait on a lock, even if he is still waiting for
   * another one. This is bad!
   */
  void checkCurrentThreadStillWaitingOnAnotherLock();

  /**
   * Do some checks before start to wait on another lock.
   * This is done to prevent the kernel from crushing, and is only
   * useful for the kernel programmer.
   */
  void doChecksBeforeWaiting();

  /**
   * Verifies that interrupts are enabled.
   */
  void checkInterrupts(const char* method);

  /**
   * Verifies that the lock is held by this thread.
   * @param method in which the check is done
   */
  void checkInvalidRelease(const char* method);

  /**
   * Lock the waiters list, so it may be modified.
   * The lock may not be held in case the list is read out in some special cases.
   */
  void lockWaitersList();

  /**
   * unlock the waiters list.
   */
  void unlockWaitersList();

  /**
   * Check if the waiters list is locked at the moment.
   * Only use this method for assertions, it is not ensured that the lock is held by the current thread.
   * @return true in case the lock is held by a thread at the moment
   * @return false in case the lock is not held by a thread at the moment
   */
  inline bool waitersListIsLocked() const
  {
    return waiters_list_lock_;
  }

  /**
   * Pop the longest waiting thread from the waiters list.
   * @return The thread in case one is waiting
   * @return 0 in case no thread is waiting for this lock.
   */
  Thread* popBackThreadFromWaitersList();

  /**
   * Add the current thread to the waiters list of this lock.
   */
  void pushFrontCurrentThreadToWaitersList();

  /**
   * Print the lock status.
   */
  void printStatus();

  /**
   * Release the lock, and set the thread to sleeping.
   */
  void sleepAndRelease();

private:

  /**
   * The name of the lock is a char pointer instead of a string,
   * because a string may need dynamic memory, and dynamic memory needs locks.
   */
  const char* name_;

  /**
   * The single chained list of threads waiting on this lock.
   * The list can be read out while the lock is not held (for checks and prints).
   * To be able to read out without locking, all modifying accesses have to be atomic!
   * If not, the list may become invalid while someone is reading out of it!
   */
  Thread* waiters_list_;

  /**
   * The lock for the waiters list. The list has to be locked for writing access,
   * but may be used for unlocked access in case no element is going to be removed meanwhile.
   */
  size_t waiters_list_lock_;

  /**
   * Check if a deadlock would happen in combination with other locks.
   * @param thread_waiting The thread which wants to wait on the lock
   * @param start The lock which the thread wants to wait on
   */
  void checkForCircularDeadLock(Thread* thread_waiting, Lock* start);

  /**
   * Print out a circular deadlock hierarchy.
   * @param starting The thread which shall be started at
   */
  void printOutCircularDeadLock(Thread* starting);

};

