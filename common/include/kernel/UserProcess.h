#pragma once

#include "Thread.h"
#include "offsets.h"
#include "FileSystemInfo.h"
#include "Mutex.h"
#include "umap.h"
#include "uvector.h"
#include "Condition.h"
#include "Mutex.h"
#include "FileDescriptor.h"
#include "MemoryMappedRegion.h"

class UserThread;

class UserProcess
{
  public:

    Loader* loader_;
    FileSystemInfo* fs_info_;
    size_t pid_;
    int32 fd_;
    ustl::string file_name_;

    size_t current_process_time = 0;
    size_t start_process_time = 0;

    ustl::vector<UserThread*> userthreads_;
    ustl::map<size_t,size_t> thread_retvals;
    Mutex thread_vector_lock_;
    Mutex process_lock_;
    ustl::atomic<size_t> latest_tid_ = 0;
    ustl::atomic<size_t> threadcount_ = 0;
    ustl::atomic<bool> exit_called;
    Condition only_one_thread_living;
    Mutex lock_for_exec_flag;
    ustl::atomic<size_t> exec;

    ustl::map<size_t, size_t> fd_mapping_;
    Mutex local_fd_mapping_lock_;
    size_t current_program_break;
    size_t initial_program_break;
    Mutex program_break_lock;
    Mutex shared_memory_break_lock;
    size_t shared_memory_start;
    size_t shared_memory_break;
    ustl::vector <MemoryMappedRegion*> memory_mapped_regions;    

    UserThread* getThreadfromVector(size_t id);

    /**
     * Constructor
     * @param minixfs_filename filename of the file in minixfs to execute
     * @param fs_info filesysteminfo-object to be used
     * @param terminal_number the terminal to run in (default 0)
     *
     */
    UserProcess(size_t pid ,ustl::string minixfs_filename, FileSystemInfo *fs_info);
    void removeThreadFromVector(size_t tid);
    size_t getRetValFromMap(size_t tid);
    bool checkForDeadlockJoin();
    size_t mapGlobalFdNum(size_t global_fd_num);
    size_t getGlobalFdNum(size_t local_fd_num);
    size_t eraseFdMapping(size_t global_fd_num);
    void reserveSpaceForMmapCall(size_t len);
    MemoryMappedRegion* getMemoryMappedRegion(size_t adress);
    bool checkPermissionbitsMmap(int prot);
    void setPermissionbitsMmap(PageTableEntry* pt_entry, int prot, size_t shared);
    void removeMemoryMappedRegion(size_t adress);


    virtual ~UserProcess();

    virtual void Run(); // not used

    UserProcess( UserProcess &other);
};


