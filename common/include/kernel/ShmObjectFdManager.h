#include "offsets.h"
#include "Mutex.h"
#include "VirtualFileSystem.h"
#include "FileSystemType.h"
#include "Inode.h"
#include "Dentry.h"
#include "RamFSFile.h"
#include "FileDescriptor.h"
#include "Superblock.h"
#include "UserProcess.h"


class ShmObjectFdManager{
private:
    static ShmObjectFdManager *instance_;
    Mutex lock_;
    
public:
    static ShmObjectFdManager*instance();
    ShmObjectFdManager();
    size_t generateFd(Inode* inode,UserProcess* process);
};