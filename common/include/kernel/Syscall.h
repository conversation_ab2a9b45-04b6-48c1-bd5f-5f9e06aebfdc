#pragma once

#include <types.h>
#include <Thread.h>

class Syscall
{
  public:
    static size_t syscallException(size_t syscall_number, size_t arg1, size_t arg2, size_t arg3, size_t arg4, size_t arg5);

    static void exit(size_t exit_code);
    static void outline(size_t port, pointer text);

    static size_t write(size_t fd, pointer buffer, size_t size);
    static size_t read(size_t fd, pointer buffer, size_t count);
    static size_t close(size_t fd);
    static size_t open(size_t path, size_t flags);
    static void pseudols(const char *pathname, char *buffer, size_t size);
    
    static size_t createthread(size_t thread, size_t attr, size_t startroutine, size_t arg, size_t wrapper);
    static size_t cancelthread(size_t thread);
    static size_t setcancelstate(size_t state,size_t oldstate);
    static size_t setcanceltype(size_t type,size_t oldtype);
    static size_t detach(size_t thread);
    static void exitthread(size_t retval);


    static size_t syscall_sleep(size_t tseconds);
    static size_t syscall_clock();

    static int jointhread(size_t thread_id,size_t ret_val);
    static size_t exec(size_t path, char** argv);
    static size_t createprocess(size_t path, size_t sleep);
    static size_t waitpid(size_t pid, size_t* status, size_t options); 
    static void trace();

    static size_t fork();
    static int  pipe(int* fildes);
    static void reserve_pages(size_t amount);
    static size_t brk(size_t adress);
    static size_t sbrk(int increment);
    static size_t shm_open( char *name, int oflag, mode_t mode);
    static size_t mmap(size_t addr, size_t len, int prot, unsigned int flags,
       int fildes, off_t off);
    static size_t munmap(size_t addr, size_t len); 
    static size_t shm_unlink(char* name); 
    static size_t lseek(int fildes, off_t offset, int whence);
    static size_t mprotect(size_t addr, size_t len, int prot);
    static size_t swap_page_out(size_t vpn);
    static size_t swap_page_in(size_t vpn);

};

