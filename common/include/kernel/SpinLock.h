#pragma once

#include "types.h"
#include "Lock.h"

class Thread;

class SpinLock: public Lock
{
public:

  SpinLock(const char* name);

  SpinLock(SpinLock const &) = delete;
  SpinLock &operator=(SpinLock const&) = delete;

  void acquire(pointer called_by = 0);

  /**
   * Try to acquire the spinlock. If the spinlock is held by another thread at the moment,
   * this method instantly returns (and (of course) the spinlock has not been acquired).
   * @param called_by A pointer to the call point of this function.
   *                  Can be set in case this method is called by a wrapper function.
   * @return true in case the spinlock has been acquired
   * @return false in case the spinlock was held by another thread
   */
  bool acquireNonBlocking(pointer called_by = 0);

  void release(pointer called_by = 0);

  /**
   * allows you to check if the SpinLock is set or not.
   * trust the return value only if the SpinLock can't be acquired or releases
   * when you're not locking. (= only use in atomic state)
   */
  bool isFree();

private:
  /**
   * The basic spinlock is just a variable which is
   */
  size_t lock_;
};

