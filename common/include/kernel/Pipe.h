#pragma once

#include "RingBuffer.h"
#include "UserProcess.h"
#include "uvector.h"
#include "umap.h"
#include "upair.h"
#include "debug.h"

class Ringbuffer;

class Pipe{


public:
size_t fds_[2];
RingBuffer<char> pipe_buffer_;

ustl::map <UserProcess*, ustl::pair<bool,bool>> references;
Pipe(UserProcess* process);
~Pipe();

bool is_reading_fd(size_t fd);
bool is_writing_fd(size_t fd);


void write(char* buffer, size_t size);
void read(char* buffer, size_t count);
void reset_open_closed_bits(size_t fd, UserProcess* process);
bool has_reference(size_t pid);
void insert_reference_entry(UserProcess * process);
void remove_refernce_entry(UserProcess* process);

};

