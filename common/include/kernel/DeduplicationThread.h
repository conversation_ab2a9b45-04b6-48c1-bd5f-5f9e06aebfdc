#pragma once

#include "Thread.h"
#include "umap.h"
#include "uvector.h"
#include "UserProcess.h"

class DeduplicationThread: public Thread
{
  public:
    DeduplicationThread();
    virtual ~DeduplicationThread();
    virtual void kill();
    virtual void Run();
    void deduplicate();
    size_t hashFunction( char* page);
    void acquireLockifNeeded(UserProcess* process);
    bool findProcessinVector(UserProcess* process);
    void ReleaseAllArchmemLocks();

    ustl::map <size_t,ustl::vector<size_t>> hash_to_ppn;
    ustl::vector<UserProcess*> acquired_locks;


};
