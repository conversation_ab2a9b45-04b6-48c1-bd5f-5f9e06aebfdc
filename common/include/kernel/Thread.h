#pragma once

#include "types.h"
#include "fs/FileSystemInfo.h"

#define STACK_CANARY ((uint32)0xDEADDEAD ^ (uint32)(size_t)this)
#define PTHREAD_CANCELED -1

enum ThreadState{Running, Sleeping, ToBeDestroyed};
enum SystemState {BOOTING, RUNNING, KPANIC};
enum CancelState{PTHREAD_CANCEL_ENABLE, PTHREAD_CANCEL_DISABLE};
enum CancelType{PTHREAD_CANCEL_DEFERRED, PTHREAD_CANCEL_ASYNCHRONOUS};
enum FunctionRetValues{NOERROR,EINVAL,ESRCH};
extern SystemState system_state;

class Thread;
class ArchThreadRegisters;
class Loader;
class Terminal;
class Mutex;
class Lock;

extern Thread* currentThread;

class Thread
{
    friend class Scheduler;
  public:

    static const char* threadStatePrintable[3];

    enum TYPE { KERNEL_THREAD, USER_THREAD };

    ustl::atomic<size_t> canceled;
    ustl::atomic<size_t> detached;
    ustl::atomic<size_t> wake_time;
    ustl::atomic<bool> asleep;
    TYPE type_;


    ustl::atomic<size_t> start_process_time;

    ustl::atomic<size_t> current_process_time=0;



    /**
     * Constructor for a new thread with a given working directory, name and type
     * @param working_dir working directory information for the new Thread
     * @param name The name of the thread
     * @param type The type of the thread (user or kernel thread)
     */
    Thread(FileSystemInfo* working_dir, ustl::string name, Thread::TYPE type);

    virtual ~Thread();

    /**
     * Marks the thread to be deleted by the scheduler.
     * DO Not use new / delete in this Method, as it sometimes called from an Interrupt Handler with Interrupts disabled
     */
    virtual void kill();

    /**
     * runs whatever the user wants it to run;
     */
    virtual void Run() = 0;

    void* getKernelStackStartPointer();

    bool isStackCanaryOK();

    static bool currentThreadIsStackCanaryOK();

    const char* getName();

    size_t getTID();

    void setTID(size_t id);

    Terminal* getTerminal();

    void setTerminal(Terminal *my_term);

    FileSystemInfo* getWorkingDirInfo();

    void setWorkingDirInfo(FileSystemInfo* working_dir);

    /**
     * Prints a backtrace (i.e. the call stack) to the debug output.
     * @param use_stored_thread_info determines whether to use the stored or the current thread registers
     */
    void printBacktrace();
    void printBacktrace(bool use_stored_registers);

    /**
     * Tells the scheduler if this thread is ready for scheduling
     * @return true if ready for scheduling
     */
    bool schedulable();
    bool is_sleeping();
  
  
    uint32 kernel_stack_[2048];
    ArchThreadRegisters* kernel_registers_;
    ArchThreadRegisters* user_registers_;

    uint32 switch_to_userspace_;

    Loader* loader_;


    void setState(ThreadState state);


    CancelState getCancelState();
    CancelType getCancelType(); 

    void setCancelState(CancelState state);
    void setCancelType(CancelType type);

    /**
     * A part of the single-chained waiters list for the locks.
     * It references to the next element of the list.
     * In case of a spinlock it is a busy-waiter, else usually it is a sleeper ^^.
     */
    Thread* next_thread_in_lock_waiters_list_;

    /**
     * The information which lock the thread is currently waiting on.
     */
    Lock* lock_waiting_on_;

    /**
     * A single chained list containing all the locks held by the thread at the moment.
     * This list is not locked. It may only be accessed by the thread himself,
     * or by other threads in case they can ENSURE that this thread is not able to run at this moment.
     * Changing the list has to be done atomic, else it cannot be ensured that the list is valid at any moment!
     */
    Lock* holding_lock_list_;
    Thread(Thread const &src);

  private:
   
    Thread &operator=(Thread const &src);

    volatile ThreadState state_;

    ustl::atomic<CancelState> cancelstate_;
    ustl::atomic<CancelType> canceltype_;

    size_t tid_;

    Terminal* my_terminal_;

  protected:
    ThreadState getState() const;

    FileSystemInfo* working_dir_;

    ustl::string name_;

};

