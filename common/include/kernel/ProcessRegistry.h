#pragma once

#include "Thread.h"
#include "Mutex.h"
#include "Condition.h"
#include "uvector.h"
#include "umap.h"
#include "UserProcess.h"
#include "UserProcess.h"
#include "Pipe.h"
#include "ShmObject.h"
#define DECONSTRUCT -1

struct vpn_pid_tuple
{
  size_t vpn;
  UserProcess* process;
  PageType type;
};


struct InvertedPageTableEntry
{
  ustl::vector<vpn_pid_tuple> tuples;
  size_t refcount_nfu;
  size_t spn;
};

class ProcessRegistry : public Thread
{
  public:
    /**
     * Constructor
     * @param root_fs_info the FileSystemInfo
     * @param progs a string-array of the userprograms which should be executed
     */
    ProcessRegistry ( FileSystemInfo *root_fs_info, char const *progs[] );
    ~ProcessRegistry();

    /**
     * Mounts the Minix-Partition with user-programs and creates processes
     */
    virtual void Run();

    /**
     * Tells us that a userprocess is being destroyed
     */
    void processExit();

    /**
     * Tells us that a userprocess is being created due to a fork or something similar
     */
    void processStart();

    /**
     * Tells us how many processes are running
     */
    size_t processCount();

    static ProcessRegistry* instance();
    void createProcess(const char* path);
    Mutex process_vector_lock_;
    Mutex ipt_lock_;
    Mutex global_fd_list_lock_;
    Mutex pipe_vector_lock_;
    Mutex shm_vector_lock;
    ustl::vector<UserProcess*> processes_vector;
    ustl::map<size_t,size_t> processes_retvals;
    ustl::vector < Pipe*> pipe_vector;
    ustl::vector <ShmObject*> shm_objects;
    Mutex dedup_lock_;
    Condition dedup_wait;
    bool dedup;
    bool deduped;
    ustl::list<size_t> ppns_second_chance;
   
    void addToIPT(uint64_t ppn, size_t vpn, UserProcess* process, PageType type);
    void addSPNToTuble(uint64_t ppn, size_t vpn, UserProcess* process, size_t spn);
    void deleteFromIPT(uint64_t ppn, size_t vpn, size_t pid);
    void deleteIPTEntry(uint64_t ppn);
    void printIPT();
    void removeProcessFromVector(size_t id);
    UserProcess* getProcessfromVector(size_t id);
    Pipe* getPipeFromVector(size_t fd); 
    void removePipesFromVectorandDeleteThem(UserProcess* process);
    ShmObject* getShmObjByName(char* name);
    ShmObject* getShmObjByFd(int fd);
    ShmObject* getShmObjByAdress(UserProcess* process,size_t adress);
     ShmObject* getShmObjByInode(Inode* inode);
    void copyMemoryMappings(UserProcess* process, UserProcess* child);
    void removeReferencesShmObj(UserProcess* process);
    void removeShmObj();
    size_t getsizeIpt();


   

  InvertedPageTableEntry inverted_page_table[2018];
  private:

    
    char const **progs_;
    uint32 progs_running_;
    Mutex counter_lock_;
    Condition all_processes_killed_;
    static ProcessRegistry* instance_;
};

