#pragma once
#include "types.h"
#include "uvector.h"
#include "UserProcess.h"
#include "umap.h"
#include "upair.h"
#include "Inode.h"
#include "ustring.h"
#include "MemoryMappedRegion.h"
#include "UserThread.h"

#define PROT_NONE     0x00000000  // 00..00
#define PROT_READ     0x00000001  // ..0001
#define PROT_WRITE    0x00000002  // ..0010
#define PROT_EXEC     0x00000004  // ..0100

#define MAP_PRIVATE   0x20000000  // 0010..
#define MAP_SHARED    0x40000000  // 0100..
#define MAP_ANONYMOUS  0x80000000  // 1000..

enum type{
    Anonymous=0,
    Normal=1
};



class ShmObject
{
public:
 char* name_;
 ustl::vector<int>fds_;
 mode_t mode_;
 size_t size;
 size_t offset;
 type type_;
 Inode* backing_inode;

 ustl::map<UserProcess*,ustl::vector<MemoryMappedRegion*>> memory_mappings;

 ustl::map <size_t,size_t> mapping_index_vpn_ppn;

 ustl::vector <UserProcess*> references;


 ShmObject(char* name,mode_t mode);
 ShmObject(type type);
 ShmObject(Inode* inode);
 MemoryMappedRegion* getMemoryMapping(UserProcess* process, size_t adress);
 bool hasReference(UserProcess* process);
 void addReference(UserProcess* process);
 void addMemoryMapping(UserProcess* process,MemoryMappedRegion* memory_mapping);
 void removeMemoryMapping(UserProcess* process,size_t adress);
 void removeReference(UserProcess* process);
 void performWriteback(UserProcess* process,size_t adress);
 int generateFd();




 ~ShmObject();

};



