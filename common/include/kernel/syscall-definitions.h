#pragma once

#define fd_stdin 0
#define fd_stdout 1
#define fd_stderr 2

#define sc_exit 1
#define sc_fork 2
#define sc_read 3
#define sc_write 4
#define sc_open 5
#define sc_close 6
#define sc_lseek 19
#define sc_pseudols 43
#define sc_outline 105
#define sc_sched_yield 158
#define sc_createprocess 191
#define sc_trace 252
#define sc_pthreadcreate 222
#define sc_pthreadexit 223
#define sc_pthreadcancel 224
#define sc_pthreadsetcancelstate 225
#define sc_pthreadsetcanceltype 226
#define sc_pthreaddetach 227
#define sc_sleep 300
#define sc_clock 301
#define sc_pthread_join 5000
#define sc_exec 5001
#define sc_waitpid 5002
#define sc_getPid 228
#define sc_pipe 5003
#define sc_tortillas_bootup 400
#define sc_tortillas_finished 401
#define sc_reserve_pages 3001
#define sc_brk 5004
#define sc_sbrk 5005
#define sc_mmap 5006
#define sc_munmap 5007
#define sc_shm_open 5008
#define sc_shm_unlink 5009
#define sc_mprotect 5010
#define sc_swap_page_out 5011
#define sc_swap_page_in 5012


