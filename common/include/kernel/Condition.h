#pragma once

#include "Lock.h"

class Thread;
class Mutex;

class Condition : public Lock
{
  public:
    /**
   * Constructor
   * A Condition needs a Mutex for creation. Only one thread can acquire this Mutex
   * and enter the critical section
   * @param mutex the Mutex
   * @param name the name of the condition
   */
  Condition ( Mutex *mutex, const char* name);
  ~Condition();

  Condition(Condition const &) = delete;
  Condition &operator=(Condition const &) = delete;

    /**
     * Only possible if the current Thread has acquired the Mutex.
     * The Thread is put on the list of sleepers, releases the Mutex and goes to sleep
     * Acquires the Mutex when waking up.
     * @param re_acquire_mutex Automatically re-acquire the mutex after the wake-up.
     *        In case the mutex (or even the condition) does not exists any longer after wake-up,
     *        this flag has to be set to false. Then the mutex is not accessed any longer after wake up.
     * @param called_by A pointer to the call point of this function.
     *                  Can be set in case this method is called by a wrapper function.
     *
     */
    void wait(bool re_acquire_mutex = true, pointer called_by = 0);
    void waitAndRelease(pointer called_by = 0);

    /**
     * Wakes up the first Thread on the sleepers list.
     * If the list is empty, signal is being lost.
     * @param called_by A pointer to the call point of this function.
     *                  Can be set in case this method is called by a wrapper function.
     */
    void signal(pointer called_by = 0, bool broadcast = false);

    /**
     * Wakes up all Threads on the sleepers list.
     * If the list is empty, signal is being lost.
     * @param called_by A pointer to the call point of this function.
     *                  Can be set in case this method is called by a wrapper function.
     */
    void broadcast(pointer called_by = 0);

  private:
    /**
     * The mutex which is bound to this condition.
     */
    Mutex *mutex_;

};

