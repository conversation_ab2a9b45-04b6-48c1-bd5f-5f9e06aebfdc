#pragma once

#include "Console.h"

class FrameBufferConsole : public Console
{
  public:
    FrameBufferConsole(uint32 num_terminals);

    virtual uint32 consoleSetCharacter(uint32 const &row, uint32 const&column, uint8 const &character,
                                       uint8 const &state);

  private:
    virtual void consoleClearScreen();
    virtual uint32 consoleGetNumRows() const;
    virtual uint32 consoleGetNumColumns() const;
    virtual void consoleScrollUp(uint8 const &state);

    void setPixel(uint32 x, uint32 y, uint8 r, uint8 g, uint8 b);

    uint16 convertConsoleColor(CONSOLECOLOR color);
    void colorsFromState(uint8 const &state, CONSOLECOLOR &fg, CONSOLECOLOR &bg);

    uint32 x_res_;
    uint32 y_res_;
    uint32 bits_per_pixel_;
    uint32 bytes_per_pixel_;
};

