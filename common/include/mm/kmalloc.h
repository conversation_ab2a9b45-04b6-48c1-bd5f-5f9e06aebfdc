#pragma once

#include "types.h"

#ifdef __cplusplus
extern "C"
{
#endif
  /**
   * allocates memory with given size
   * @param size the size
   */
  void *kmalloc(size_t size);
  /**
   * frees the memory allocated at the given address
   * @param address the address to free
   */
  void kfree(void * address);

  /**
   * reallocates the momory allocated at the given address to the given size
   * @param address the address to reallocate
   * @param size the size of the new memory
   */
  void *krealloc(void * address, size_t size);
#ifdef __cplusplus
}
#endif

