#pragma once

#include "offsets.h"
#include "types.h"
#include "paging-definitions.h"
#include "uvector.h"

class ArchMemoryMapping
{
  public:
    Level1Entry* level1_entry;
    Level2Entry* level2_entry;
    Level3Entry* level3_entry;


    pointer page; //address to the ident mapped ppn

    size_t level1_ppn;
    size_t level2_ppn;
    size_t level3_ppn;

    size_t page_ppn;  //the physical page number

    size_t page_size;

    size_t level1_index;
    size_t level2_index;
    size_t level3_index;
};

extern Level1Entry kernel_paging_level1[];
extern Level2Entry kernel_paging_level2[];
extern Level3Entry kernel_paging_level3[];


/**
 *
 * Collection of architecture dependant functions concerning Memory and Pages
 *
 */
class ArchMemory
{
public:

/** 
 * Constructor
 * creates a new Page-Directory for a UserProccess by copying the
 * Kernel-Page-Directory
 *
 */
  ArchMemory();

/** 
 *
 * maps a virtual page to a physical page (pde and pte need to be set up first)
 *
 * @param virtual_page 
 * @param physical_page
 * @param user_access PTE User/Supervisor Flag, governing the binary Paging
 * Privilege Mechanism
 */
  bool mapPage(size_t virtual_page, size_t physical_page, size_t user_access);

/**
 * removes the mapping to a virtual_page by marking its PTE Entry as non valid
 *
 * @param virtual_page which will be invalidated
 */
  bool unmapPage(size_t virtual_page);

/**
 * Destructor. Recursively deletes the page directory and all page tables
 *
 */
  ~ArchMemory();

/**
 * Takes a Physical Page Number in Real Memory and returns a virtual address than
 * can be used to access given page
 * @param ppn Physical Page Number
 * @param page_size Optional, defaults to 4k pages, but you ned to set it to
 * 1024*4096 if you have a 4m page number
 * @return Virtual Address above 3GB pointing to the start of a memory segment that
 * is mapped to the physical page given
 */
  static pointer getIdentAddressOfPPN(size_t ppn, size_t page_size = PAGE_SIZE)
  {
    return IDENT_MAPPING_START + (ppn * page_size);
  }

/**
 * Checks if a given Virtual Address is valid and is mapped to real memory
 * @param vaddress_to_check Virtual Address we want to check
 * @return true: if mapping exists\nfalse: if the given virtual address is unmapped
 * and accessing it would result in a pageFault
 */
  pointer checkAddressValid(size_t vaddress_to_check);

/**
 * Takes a virtual_page and search through the pageTable and pageDirectory for the
 * physical_page it refers to
 * to get a physical address (which you can only use by adding 3gb to it) multiply
 * the &physical_page with the return value
 * @param virtual_page virtual Page to look up
 * @param *physical_page Pointer to the result
 * @param *physical_pte_page optional: Pointer to physical page number of used PTE, or unchanged if 4MiB Page
 * @return 0: if the virtual page doesn't map to any physical page\notherwise
 * returns the page size in byte (4096 for 4KiB pages or 4096*1024 for 4MiB pages)
 */
  static size_t get_PPN_Of_VPN_In_KernelMapping(size_t virtual_page, size_t *physical_page, size_t *physical_pte_page=0);
  const ArchMemoryMapping resolveMapping(size_t vpage);
  static const ArchMemoryMapping resolveMapping(size_t level1_ppn, size_t vpage);

  /**
   *
   * maps a virtual page to a physical page in kernel mapping
   *
   * @param virtual_page
   * @param physical_page
   */
    static void mapKernelPage(size_t virtual_page, size_t physical_page);

  /**
   * removes the mapping to a virtual_page by marking its PTE Entry as non valid
   * in kernel mapping
   *
   * @param virtual_page which will be invalidated
   */
    static void unmapKernelPage(size_t virtual_page);

  /**
  * ppn root paging level
  */
  size_t paging_root_page_;

  /**
   * ASID of the Address sapce
   */
  uint16_t address_space_id = 0;

  size_t getRootOfPagingStructure();

  static const size_t RESERVED_START = 0xFFFFFFC000000ULL;
  static const size_t RESERVED_END =   0xFFFFFFC000400ULL;

private:

  /**
  * Removes a paging entry from a given page_table if it is present
  * in the first place. Futhermore, the target page table is assured to be
  * empty.
  *
  * @param table_ptr physical page containing the target paging_table.
  * @param index Index of the paging entry to be removed
  */
  template<typename T> static bool checkAndRemove(pointer table_ptr, size_t index);

  /**
  * Removes a page directory entry from a given page directory if it is present
  * in the first place. Futhermore, the target page table is assured to be
  * empty.
  *
  * @param pde_vpn Index of the PDE (i.e. the page table) in the PD.
  */
  void checkAndRemovePT(size_t pde_vpn);


  ArchMemory(ArchMemory const &src); // not yet implemented
  ArchMemory &operator=(ArchMemory const &src); // should never be implemented

};

