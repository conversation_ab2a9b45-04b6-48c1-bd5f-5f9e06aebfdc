#pragma once

#include "types.h"
#define PAGE_ENTRIES   512
#define LEVEL0_ENTRIES PAGE_ENTRIES
#define LEVEL1_ENTRIES PAGE_ENTRIES
#define LEVEL2_ENTRIES PAGE_ENTRIES
#define LEVEL3_ENTRIES PAGE_ENTRIES
#define PAGE_SIZE 4096

#define PAGE_TABLE_ENTRIES PAGE_ENTRIES

#define ENTRY_DESCRIPTOR_BLOCK 1
#define ENTRY_DESCRIPTOR_TABLE 3
#define ENTRY_DESCRIPTOR_PAGE 3

#define ACCESS_FLAG 1

#define MEMORY_ATTR_STD 0
#define MEMORY_ATTR_MMIO 1
#define MEMORY_ATTR_NON_CACHABLE 2

#define SHARE_OSH 2
#define SHARE_ISH 3

struct Level12TableEntry
{
    size_t entry_descriptor_type   :2;     //1:0 | 2
    size_t ignored_1               :10;
    size_t table_address           :36;
    size_t reserved                :4;
    size_t ignored_2               :7;
    size_t pxn_table               :1;
    size_t xn_table                :1;
    size_t ap_table                :2;
    size_t ns_table                :1;
}__attribute__((__packed__));

static_assert(sizeof(Level12TableEntry) == 8, "Level12TableEntry is not 64 bit");

struct Level1BlockEntry
{
    size_t entry_descriptor_type   :2;     //1:0 | 1
    size_t memory_attributes_index :3;     // this is used to tell the mmu if its ram or mmio...
    size_t non_secure              :1;
    size_t access_permissions      :2;
    size_t shareability_field      :2;
    size_t access_flag             :1;
    size_t not_global              :1;
    size_t reserved_1              :18;
    size_t block_address           :18;
    size_t reserved_2              :4;
    size_t contiguous              :1;
    size_t privileged_execute_never:1;
    size_t execute_never           :1;
    size_t ingored_1               :4;      //Reserved for software use
    size_t ignored_2               :5;
}__attribute__((__packed__));

static_assert(sizeof(Level1BlockEntry) == 8, "Level1BlockEntry is not 64 bit");

struct Level2BlockEntry
{
    size_t entry_descriptor_type   :2;     //1:0 | 1
    size_t memory_attributes_index :3;     // this is used to tell the mmu if its ram or mmio...
    size_t non_secure              :1;
    size_t access_permissions      :2;
    size_t shareability_field      :2;
    size_t access_flag             :1;
    size_t not_global              :1;
    size_t reserved_1              :9;
    size_t block_address           :27;
    size_t reserved_2              :4;
    size_t contiguous              :1;
    size_t privileged_execute_never:1;
    size_t execute_never           :1;
    size_t ingored_1               :4;      //Reserved for software use
    size_t ignored_2               :5;
}__attribute__((__packed__));

static_assert(sizeof(Level2BlockEntry) == 8, "Level2BlockEntry is not 64 bit");

typedef struct
{
    size_t entry_descriptor_type   :2;     //1:0 | 2
    size_t memory_attributes_index :3;     // this is used to tell the mmu if its ram or mmio...
    size_t non_secure              :1;
    size_t access_permissions      :2;
    size_t shareability_field      :2;
    size_t access_flag             :1;
    size_t not_global              :1;
    size_t page_address            :36;
    size_t reserved_1              :4;
    size_t contiguous              :1;
    size_t privileged_execute_never:1;
    size_t execute_never           :1;
    size_t ingored_1               :4;      //Reserved for software use
    size_t ignored_2               :5;
}__attribute__((__packed__)) Level3Entry;

static_assert(sizeof(Level3Entry) == 8, "Level3Entry is not 64 bit");

typedef struct Level12TableEntry Level1Entry;

typedef union
{
  struct Level12TableEntry table;
  struct Level2BlockEntry block;
} __attribute__((__packed__)) Level2Entry;
