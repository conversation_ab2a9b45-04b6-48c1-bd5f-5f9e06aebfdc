#pragma once

typedef signed char int8;
typedef unsigned char uint8;

typedef signed short int int16;
typedef unsigned short int uint16;

typedef signed int int32;
typedef unsigned int uint32;

typedef unsigned long int uint64;
typedef signed long int int64;


typedef uint32 l_off_t;

typedef uint32 mode_t;
typedef uint32 uid_t;
typedef uint32 gid_t;
typedef uint64 size_t;
typedef int64 ssize_t;

typedef size_t pointer;

#pragma GCC poison double float

#define Min(x,y) (((x)<(y))?(x):(y))
#define Max(x,y) (((x)>(y))?(x):(y))

#define likely(x)       __builtin_expect((x),1)
#define unlikely(x)     __builtin_expect((x),0)
#define unreachable()    __builtin_unreachable()

#define NO_OPTIMIZE __attribute__((optimize("O0")))
