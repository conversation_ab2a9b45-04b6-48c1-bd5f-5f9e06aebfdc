#pragma once

#define PIC_IRQ_ENABLESET   0x2
#define PIC_IRQ_STATUS   0x0

#define REG_CTRL    0x02
#define REG_INTCLR    0x03
#define REG_INTSTAT   0x04
#define REG_INTMASKED   0x05
#define REG_BGLOAD    0x06
#define REG_LOAD    0x07
#define REG_CURRENT    0x07

#define CTRL_ENABLE     0x80
#define CTRL_MODE_PERIODIC  0x40
#define CTRL_INT_ENABLE   (1<<5)
#define CTRL_DIV_NONE   0x00
#define CTRL_SIZE_32    0x02


#define MASK_CURRENT           0x00
#define MASK_LOWER             0x10

#define ARM_EXC_SYNC           0x01
#define ARM_EXC_IRQ            0x02
#define ARM_EXC_FIQ            0x03
#define ARM_EXC_ERROR          0x04

#define ARM_EXC_CURR_SYNC      (MASK_CURRENT | ARM_EXC_SYNC)
#define ARM_EXC_CURR_IRQ       (MASK_CURRENT | ARM_EXC_IRQ)
#define ARM_EXC_CURR_FIQ       (MASK_CURRENT | ARM_EXC_FIQ)
#define ARM_EXC_CURR_ERROR     (MASK_CURRENT | ARM_EXC_ERROR)

#define ARM_EXC_LOWER_SYNC     (MASK_LOWER | ARM_EXC_SYNC)
#define ARM_EXC_LOWER_IRQ      (MASK_LOWER | ARM_EXC_IRQ)
#define ARM_EXC_LOWER_FIQ      (MASK_LOWER | ARM_EXC_FIQ)
#define ARM_EXC_LOWER_ERROR    (MASK_LOWER | ARM_EXC_ERROR)

#define ARM_EXC_CLA_SYSCALL 0x15
#define ARM_EXC_INSTR_ABORT_LOWER_EL 0x20
#define ARM_EXC_INSTR_ABORT_CURR_EL 0x21
#define ARM_EXC_DATA_ABORT_LOWER_EL 0x24
#define ARM_EXC_DATA_ABORT_CURR_EL 0x25


#define NU __attribute__((unused))
