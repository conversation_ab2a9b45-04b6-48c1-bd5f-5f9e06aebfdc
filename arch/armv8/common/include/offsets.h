#pragma once

#include "board_constants.h"

/**
 * These are the basic offsets for our memory layout
 */

/**
 * The kernel image will start at the second half of the kernel space, the first is ident mapping
 */
#define LINK_BASE 0xFFFFFFC000000000ULL

/**
 * physical ram starts at 0x0
 */
#define LOAD_BASE BOARD_LOAD_BASE

/**
 * this is the difference between link and load base
 */
#define PHYSICAL_TO_VIRTUAL_OFFSET (LINK_BASE - LOAD_BASE)

/**
 * returns the virtual address of a physical address by using the offset
 */
#define PHYSICAL_TO_VIRTUAL_BOOT(x) ((x) + PHYSICAL_TO_VIRTUAL_OFFSET)

/**
 * returns the physical address of a virtual address by using the offset
 */
#define VIRTUAL_TO_PHYSICAL_BOOT(x) ((x) - PHYSICAL_TO_VIRTUAL_OFFSET)

/**
 * The start of the ident mapping
 */
#define IDENT_MAPPING_START 0xFFFFFF8000000000ULL

/**
 * The final frontier between user and kernel memory
 */
#define USER_BREAK 0x0000008000000000ULL

/**
 * The number of paging tables, this can be changed based on layout and kernel memory amount
 */
#define KERNEL_LEVEL1_TABLES 1
#define KERNEL_LEVEL2_TABLES 3
#define KERNEL_LEVEL3_TABLES 8

#define NUMBER_KERNEL_PAGEING_TABLES (KERNEL_LEVEL1_TABLES + KERNEL_LEVEL2_TABLES + KERNEL_LEVEL3_TABLES)
