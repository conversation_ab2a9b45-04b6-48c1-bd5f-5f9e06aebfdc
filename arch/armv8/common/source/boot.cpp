#include "types.h"
#include "board_constants.h"
#include "init_boottime_pagetables.h"
#include "assert.h"
#include "kstring.h"
#include "debug_bochs.h"
#include "offsets.h"

extern "C" void uartWritePostboot(const char *str);
extern "C" void uartWritePreboot(const char *str);
extern "C" void setupSerialPort();
extern "C" void startup();
extern uint8 bss_start_address;
extern uint8 bss_end_address;
extern uint8 boot_stack[];
extern "C" void blink();

extern "C" void  entry_()
{
	//set the entry here because the compiler ignores the Naked flag
    asm(".global cpp_entry \n cpp_entry:");

	//setup stack and frame ptr
	asm("mov sp, %[v] \n mov x29, sp" : : [v]"r"( (((size_t)boot_stack) & ~LINK_BASE) + 0x4000));

	//init the serial port for debugging
	setupSerialPort();

	uartWritePreboot("booting...\n");

	//clear bss
    size_t start = (size_t) &bss_start_address;
    size_t end = (size_t) &bss_end_address;

    start &= ~LINK_BASE;
    end &= ~LINK_BASE;

    while (start < end)
    {
        *((size_t*) start) = 0;
        start += sizeof(size_t);
    }

	uartWritePreboot("setup MMU...\n");

	//setup the mmu and map all relevant data
    void (*initialiseBootTimePagingPTR)() = (void(*)())(((size_t)initialiseBootTimePaging) & ~LINK_BASE);
    initialiseBootTimePagingPTR();

    //parts of the following code are from:
    //https://github.com/bztsrc/raspi3-tutorial/blob/master/10_virtualmemory/mmu.c

    size_t tmp_var = 0;

    //get bit modes supported by mmu
    asm volatile ("mrs %0, id_aa64mmfr0_el1" : "=r" (tmp_var));

    size_t ph_bit_width = tmp_var & 0xF;

    if (tmp_var & (0xF << 28)/*4k*/|| ph_bit_width < 1/*36 bits*/)
    {
        uartWritePreboot("ERROR: 4k granule or 36 bit address space not supported\n");
        while(1);
    }

    //use AttrIdx0 for normal RAM (IWBWA, OWBWA, NTR)
    //use AttrIdx1 for device memory e.g. SFRs (nGnRE, OSH)
    //use AttrIdx2 for non cacheable memory
    tmp_var = (0xFF << 0) | (0x04 << 8) | (0x44 << 16);

    //set attributes for the memory types
    asm volatile ("msr mair_el1, %0" : : "r" (tmp_var));

    size_t ttbr0_settings =
            (0b00LL << 14) | // TG0=4k
            (0b11LL << 12) | // SH0=3 inner
            (0b01LL << 10) | // ORGN0=1 write back
            (0b01LL << 8) |  // IRGN0=1 write back
            (0b0LL << 7) |  // EPD0 enable lower half
            (25LL << 0);   // T0SZ=25, 3 levels (512G)

    size_t ttbr1_settings =
            (0b1LL << 36) | // AS use 16 bit ASID
            (0b10LL << 30) | // TG1=4k
            (0b11LL << 28) | // SH1=3 inner
            (0b01LL << 26) | // ORGN1=1 write back
            (0b01LL << 24) | // IRGN1=1 write back
            (0b0LL << 23) | // EPD1 enable higher half
            (25LL << 16); // T1SZ=25, 3 levels (512G)

    size_t tcr_el1_settings = (ph_bit_width << 32) | ttbr1_settings
            | ttbr0_settings;

    asm volatile ("isb");
    asm volatile ("msr tcr_el1, %0" : : "r" (tcr_el1_settings));

    //ttbr0_el1 and ttbr1_el1 are set in init_boottime_pagetables.cpp

    //set mmu parameters and enable mmu
    asm volatile ("dsb ish");
    asm volatile ("isb");
    asm volatile ("mrs %0, sctlr_el1" : "=r" (tmp_var));

    tmp_var |= 0xC00800;     // set mandatory reserved bits
    tmp_var &= ~((1 << 25) | // clear EE, little endian translation tables
            (1 << 24) |      // clear E0E
            (1 << 19) |      // clear WXN
            (1 << 12) |      // clear I, no instruction cache
            (1 << 4) |       // clear SA0
            (1 << 3) |       // clear SA
            (1 << 2) |       // clear C, no cache at all
            (1 << 1));       // clear A, no aligment check
    tmp_var |= (1 << 0);     // set M, enable MMU

    uartWritePreboot("enable MMU now...\n");

    asm volatile ("msr sctlr_el1, %0;" : : "r" (tmp_var));

    asm volatile ("isb");

    uartWritePreboot("done mapping...\n");

    writeLine2Bochs("finished boot time mapping jumping to startup now\n"); //use mapped mmio now

	//setup mapped stack
    asm("ldr x0  , =boot_stack");
    asm("add x0  , x0, #0x4, lsl #12");
    asm("mov sp  , x0");
    asm("mov x29 , sp");

    //jump to start function
	asm("ldr x0 , =startup");
    asm("blr x0");

    assert(false && "it should be impossible to get to this point");
}

volatile GpioRegisters *gpio_boot = (GpioRegisters*)(GPIO_REGS_BASE);
volatile AuxRegisters *aux = (AuxRegisters*)(AUX_REGS_BASE);
volatile PL011UartRegisters *uart = (PL011UartRegisters*)(BCM2837_PL011_REGS_BASE);

#define UART0_DR_NO_MAPPING        ((volatile unsigned int*)(SERIAL_BASE))
#define UART0_FR_NO_MAPPING        ((volatile unsigned int*)(SERIAL_BASE | SERIAL_FLAG_REGISTER))


extern "C" void uartWritePreboot(const char *str)
{
    while (*str)
    {
        while (*UART0_FR_NO_MAPPING & SERIAL_BUFFER_FULL)
            asm volatile("nop");

        *UART0_DR_NO_MAPPING = *str++;
    }
}

extern "C" void setupSerialPort()
{
    uart->CR = 0;
    //the default clock seems to be 48Mhz

    gpio_boot->GPFSEL1 &= ~(0x111 << (4 * 3));
    // set GPIO usage to uart
    gpio_boot->GPFSEL1 |= (0x100 << (4 * 3));
    //do the same for pin 15
    gpio_boot->GPFSEL1 &= ~(0x111 << (5 * 3));
    // set GPIO usage to uart
    gpio_boot->GPFSEL1 |= (0x100 << (5 * 3));

    // disable the pullups
    gpio_boot->GPPUD = 0;

    //wait 1500
    for (int cnt = 0; cnt < 150; cnt++)
        asm("nop");

    //set pullup/down clock for pins 14 and 15
    gpio_boot->GPPUDCLK0 = (1 << 14) | (1 << 15);

    for (int cnt = 0; cnt < 150; cnt++)
        asm("nop");

    //x/(16*115200) = 26.11
    //reste clock
    gpio_boot->GPPUDCLK0 = 0;
    uart->IMSC = 1 << 4;
    uart->ICR = 0x7FF;
    uart->IBRD = 26;
    uart->FBRD = 0x0;
    uart->LCRH = 0b11 << 5;
    uart->CR = 0x301;
}
