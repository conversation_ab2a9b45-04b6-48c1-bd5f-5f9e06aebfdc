//parts of this code are from:
//http://infocenter.arm.com/help/topic/com.arm.doc.dai0527a/DAI0527A_baremetal_boot_code_for_ARMv8_A_processors.pdf

#include <InterruptUtils.h>

.extern kernel_sp_struct_offset
.extern currentThreadRegisters
.extern boot_stack

panic: b panic

handle_exception:
    STP Q0  , Q1   , [X2, #32 * 0]  //store neon registers
    STP Q2  , Q3   , [X2, #32 * 1]
    STP Q4  , Q5   , [X2, #32 * 2]
    STP Q6  , Q7   , [X2, #32 * 3]
    STP Q8  , Q9   , [X2, #32 * 4]
    STP Q10 , Q11  , [X2, #32 * 5]
    STP Q12 , Q13  , [X2, #32 * 6]
    STP Q14 , Q15  , [X2, #32 * 7]
    STP Q16 , Q17  , [X2, #32 * 8]
    STP Q18 , Q19  , [X2, #32 * 9]
    STP Q20 , Q21  , [X2, #32 * 10]
    STP Q22 , Q23  , [X2, #32 * 11]
    STP Q24 , Q25  , [X2, #32 * 12]
    STP Q26 , Q27  , [X2, #32 * 13]
    STP Q28 , Q29  , [X2, #32 * 14]
    STP Q30 , Q31  , [X2, #32 * 15]

    ADD X2  , X2   , #(32 * 16)

get_context_data:
    MRS    X3 , SPSR_EL1
    MRS    X4 , ELR_EL1
    MRS    X6 , TTBR0_EL1

    //get kernel stack ptr
    LDR    X8 , [X2 , #(16 * 2)]  //todo use other register here

check_um:                     //check if exc occured in UM, we need to change the SP then
    AND    X7 , X3 ,#0xf        //todo this needs to be x3
    CBZ    X7 , exc_from_um   //exc from SM
    b      exc_from_sm


exc_from_um://set kernel stack ptr and save user stack ptr
    MRS X5 , SP_EL0
    MOV SP , X8
    b save_context_remainder

exc_from_sm:
    MOV X5 , SP

save_context_remainder:
    STP X3 , X4 , [X2, #16 * 0]
    STP X5 , X6 , [X2, #16 * 1]

get_exc_parms:
    MRS X1 , CurrentEL     // current el register
    MRS X2 , ESR_EL1       // indicates what exception occured
    MRS X3 , FAR_EL1       // contains the fault address
    MRS X4 , ELR_EL1       // the address to jump back to

    bl interruptEntry      //call c interrupt dispatcher

restore_context:
    LDR  X0 , =currentThreadRegisters
    LDR  X0 , [X0]
    LDR  X1 , =kernel_sp_struct_offset
    LDR  X1 , [X1]
    ADD  X0 , X0 , X1

    LDP X1 , X2 , [X0, #-16 * 1]
    LDP X3 , X4 , [X0, #-16 * 2]

check_sp:
    AND X6 , X3 , #0xf
    CBNZ X6 , restore_sp_sm    //check if we need to go back to UM or SM
    B           restore_sp_um

restore_sp_sm:
    MOV   SP  , X1
    B        restore_rest

restore_sp_um:
    MSR SP_EL0    , X1
    LDR X1 , =boot_stack
    ADD X1 , X1 , #0x4000
    MOV SP , X1

restore_rest:
    MSR TTBR0_EL1 , X2
    MSR SPSR_EL1  , X3
    MSR ELR_EL1   , X4


    DSB ISHST
    //TLBI ALLE1
    DSB ISH
    ISB


    SUB X0 , X0 , #32

    LDP Q30, Q31 , [X0, #-32 * 1]
    LDP Q28, Q29 , [X0, #-32 * 2]
    LDP Q26, Q27 , [X0, #-32 * 3]
    LDP Q24, Q25 , [X0, #-32 * 4]
    LDP Q22, Q23 , [X0, #-32 * 5]
    LDP Q20, Q21 , [X0, #-32 * 6]
    LDP Q18, Q19 , [X0, #-32 * 7]
    LDP Q16, Q17 , [X0, #-32 * 8]
    LDP Q14, Q15 , [X0, #-32 * 9]
    LDP Q12, Q13 , [X0, #-32 * 10]
    LDP Q10, Q11 , [X0, #-32 * 11]
    LDP Q8 , Q9  , [X0, #-32 * 12]
    LDP Q6 , Q7  , [X0, #-32 * 13]
    LDP Q4 , Q5  , [X0, #-32 * 14]
    LDP Q2 , Q3  , [X0, #-32 * 15]
    LDP Q0 , Q1  , [X0, #-32 * 16]

    SUB X0 , X0 , #(32*16)

    LDP X29 , X30, [X0, #-16 * 1]
    LDP X27 , X28, [X0, #-16 * 2]
    LDP X25 , X26, [X0, #-16 * 3]
    LDP X23 , X24, [X0, #-16 * 4]
    LDP X21 , X22, [X0, #-16 * 5]
    LDP X19 , X20, [X0, #-16 * 6]
    LDP X17 , X18, [X0, #-16 * 7]
    LDP X15 , X16, [X0, #-16 * 8]
    LDP X13 , X14, [X0, #-16 * 9]
    LDP X11 , X12, [X0, #-16 * 10]
    LDP X9  , X10, [X0, #-16 * 11]
    LDP X7  , X8 , [X0, #-16 * 12]
    LDP X5  , X6 , [X0, #-16 * 13]
    LDP X3  , X4 , [X0, #-16 * 14]
    LDP X1  , X2 , [X0, #-16 * 15]
    LDR X0  , [X0, #(-16 * 15)-8]

    dmb sy
    dsb sy
    isb sy

    ERET

.macro INTERRUPT_ENTRY interrupt_id
    nop
    
    /* this is used for debugging purposes
    MOV X0, #\interrupt_id
    MRS X1 , CurrentEL     // current el register
    MRS X2 , ESR_EL1       // indicates what exception occured
    MRS X3 , FAR_EL1       // contains the fault address
    MRS X4 , ELR_EL1
    b interruptEntryDebug
    */
    STP X0 , X1 , [SP, #-16]!

    LDR X0, =currentThreadRegisters    //get registers of current registers
    LDR X0 , [X0]

    STP X2  , X3  , [X0, #16 * 1]
    STP X4  , X5  , [X0, #16 * 2]
    STP X6  , X7  , [X0, #16 * 3]
    STP X8  , X9  , [X0, #16 * 4]
    STP X10 , X11 , [X0, #16 * 5]
    STP X12 , X13 , [X0, #16 * 6]
    STP X14 , X15 , [X0, #16 * 7]
    STP X16 , X17 , [X0, #16 * 8]
    STP X18 , X19 , [X0, #16 * 9]
    STP X20 , X21 , [X0, #16 * 10]
    STP X22 , X23 , [X0, #16 * 11]
    STP X24 , X25 , [X0, #16 * 12]
    STP X26 , X27 , [X0, #16 * 13]
    STP X28 , X29 , [X0, #16 * 14]
    STR X30 ,       [X0, #16 * 15]

    MOV X2  , X0
    LDP X0  , X1  , [SP] , #16
    STP X0  , X1  , [X2, #16 * 0]
    ADD X2  , X2  , #(8 * 31)


    MOV X0, #\interrupt_id

    b handle_exception
    b panic
.endm

.macro INTERRUPT_ENTRY_INVALID

B panic

.endm

.balign 0x800
.global Vector_table_el1
Vector_table_el1:
curr_el_sp0_sync:
INTERRUPT_ENTRY_INVALID

.balign 0x80
curr_el_sp0_irq:
INTERRUPT_ENTRY_INVALID

.balign 0x80
curr_el_sp0_fiq:
INTERRUPT_ENTRY_INVALID

.balign 0x80
curr_el_sp0_serror:
INTERRUPT_ENTRY_INVALID

/*  *********************************************  */

.balign 0x80
curr_el_spx_sync:
INTERRUPT_ENTRY ARM_EXC_CURR_SYNC

.balign 0x80
curr_el_spx_irq:
INTERRUPT_ENTRY ARM_EXC_CURR_IRQ

.balign 0x80
curr_el_spx_fiq:
INTERRUPT_ENTRY ARM_EXC_CURR_FIQ

.balign 0x80
curr_el_spx_serror:
INTERRUPT_ENTRY ARM_EXC_CURR_ERROR

/*  *********************************************  */

.balign 0x80
lower_el_aarch64_sync:
INTERRUPT_ENTRY ARM_EXC_LOWER_SYNC

.balign 0x80
lower_el_aarch64_irq:
INTERRUPT_ENTRY ARM_EXC_LOWER_IRQ

.balign 0x80
lower_el_aarch64_fiq:
INTERRUPT_ENTRY ARM_EXC_LOWER_FIQ

.balign 0x80
lower_el_aarch64_serror:
INTERRUPT_ENTRY ARM_EXC_LOWER_ERROR

/*  *********************************************  */

.balign 0x80
lower_el_aarch32_sync:
INTERRUPT_ENTRY_INVALID

.balign 0x80
lower_el_aarch32_irq:
INTERRUPT_ENTRY_INVALID

.balign 0x80
lower_el_aarch32_fiq:
INTERRUPT_ENTRY_INVALID

.balign 0x80
lower_el_aarch32_serror:
INTERRUPT_ENTRY_INVALID

