#include "kprintf.h"
#include "Thread.h"
#include "arch_backtrace.h"
#include "InterruptUtils.h"
#include "ArchThreads.h"
#include "KernelMemoryManager.h" // for use of "kernel_end_address"
#include "umap.h"
#include "ArchCommon.h"
#include "offsets.h"
#include "Loader.h"

struct StackFrame
{
  StackFrame *prev_fp;
  pointer lr;
};

extern Thread* currentThread;

int backtrace(pointer *call_stack, int size, Thread *thread, bool use_stored_registers)
{
  if (!call_stack ||
      (use_stored_registers && !thread) ||
      (!use_stored_registers && thread != currentThread) ||
      size <= 1)
    return 0;

  StackFrame *fp = 0;

  if (!use_stored_registers)
  {
    asm("mov %[v], x29" : [v]"=r" (fp));
  }
  else
    fp = (StackFrame*)((size_t)thread->kernel_registers_->X[29]);

  int i = 0;

  void *StackStart = (void*)(((size_t)thread->kernel_stack_ + sizeof(thread->kernel_stack_)) - sizeof(uint32)); // the stack "starts" at the high addresses...
  void *StackEnd = (void*)((size_t)thread->kernel_stack_); // ... and "ends" at the lower ones.

  if (use_stored_registers)
    call_stack[i++] = thread->kernel_registers_->ELR;

  while (i < size && ADDRESS_BETWEEN(fp, StackEnd, StackStart))
  {
        if (fp->lr == 0 || fp->prev_fp == 0)
            return i;

        call_stack[i++] = fp->lr;
        fp = fp->prev_fp;
  }

  return i;
}

int backtrace_user(pointer *call_stack, int size, Thread *thread, bool /*use_stored_registers*/)
{
  if (!call_stack || !size || !thread->user_registers_ || !thread)
    return 0;

  void *ebp = (void*)(size_t)thread->user_registers_->X[29];
  StackFrame *CurrentFrameI = (StackFrame*)thread->loader_->arch_memory_.checkAddressValid((size_t)ebp);
  StackFrame *CurrentFrame = (StackFrame*)(ebp);

  // the userspace stack is allowed to be anywhere in userspace
  void *StackStart = (void*) (USER_BREAK - sizeof(pointer));// the stack "starts" at the high addresses...
  void *StackEnd = 0x0; // ... and "ends" at the lower ones.

  int i = 0;

  call_stack[i++] = thread->user_registers_->ELR;

  while (i < size &&
      ADDRESS_BETWEEN(CurrentFrame, StackEnd, StackStart) && CurrentFrameI)
  {
    if(CurrentFrameI->lr == 0 || CurrentFrameI->prev_fp == 0)
        return i;

    call_stack[i++] = (pointer)CurrentFrameI->lr;

    CurrentFrame = CurrentFrameI->prev_fp;
    CurrentFrameI = (StackFrame*)thread->loader_->arch_memory_.checkAddressValid((size_t)CurrentFrame);
  }

  return i;
}


