//parts of this code are from:
//http://infocenter.arm.com/help/topic/com.arm.doc.dai0527a/DAI0527A_baremetal_boot_code_for_ARMv8_A_processors.pdf

.extern Vector_table_el1

.section .text.boot
.global entry
entry:
    mrs x0,mpidr_el1
    and x0,x0,#0xFF
    cbz x0, main_core
halt_loop:
    wfi
    b halt_loop;

main_core:
    mrs     x0, CurrentEL
    and     x0, x0, #12
    cmp     x0, #12
    bne     EL2

EL3:
    MSR CPTR_EL3, XZR //enable neon(disable FP register trapping)
    mov     x2, #0x581
    msr     scr_el3, x2
    mov     x2, #0x3c9
    msr     spsr_el3, x2
    adr     x2, EL2
    msr     elr_el3, x2
    eret
EL2:
    cmp     x0, #4
    beq     EL1
    MSR CPTR_EL2, XZR //enable neon(disable FP register trapping)
enable_timer:
    mrs     x0, cnthctl_el2
    orr     x0, x0, #0b111
    msr     cnthctl_el2, x0
    msr     cntvoff_el2, xzr
    mov     x0, #(1 << 31)      // AArch64
    orr     x0, x0, #(1 << 1)   // SWIO hardwired on Pi3
    msr     hcr_el2, x0
    mrs     x0, hcr_el2

    mov     x2, #0x0800
    movk    x2, #0x30c0, lsl #16
    msr     sctlr_el1, x2
    // change execution level to EL1
    mov     x2, #0x3c4
    msr     spsr_el2, x2
    adr     x2, EL1
    msr     elr_el2, x2
    eret

EL1:
    MOV     X1, #(0x3 << 20)//enable neon(disable FP register trapping)
    MSR     CPACR_EL1, X1
    ISB

    LDR     X1, =Vector_table_el1 //setup vector table
    MSR     VBAR_EL1, X1
    MSR     SPSel, #1 //use sp_EL1
    b       cpp_entry
