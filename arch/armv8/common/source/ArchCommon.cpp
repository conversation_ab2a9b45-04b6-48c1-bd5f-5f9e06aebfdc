#include "ArchCommon.h"
#include "ArchBoardSpecific.h"
#include "offsets.h"
#include "kprintf.h"
#include "ArchMemory.h"
#include "TextConsole.h"
#include "FrameBufferConsole.h"
#include "backtrace.h"
#include "SWEBDebugInfo.h"

#define PHYSICAL_MEMORY_AVAILABLE 8*1024*1024

extern void* kernel_end_address;

pointer ArchCommon::getKernelEndAddress()
{
   return (pointer)&kernel_end_address;
}

pointer ArchCommon::getFreeKernelMemoryStart()
{
   return (pointer)&kernel_end_address;
}

pointer ArchCommon::getFreeKernelMemoryEnd()
{
   return (pointer)getModuleEndAddress(0);
}


size_t ArchCommon::haveVESAConsole(size_t is_paging_set_up __attribute__((unused)))
{
  return true;
}

size_t ArchCommon::getNumModules(size_t is_paging_set_up __attribute__((unused)))
{
  return 1;
}

size_t ArchCommon::getModuleStartAddress(size_t num __attribute__((unused)), size_t is_paging_set_up __attribute__((unused)))
{
  return LINK_BASE;
}

size_t ArchCommon::getModuleEndAddress(size_t num __attribute__((unused)), size_t is_paging_set_up __attribute__((unused)))
{
  return getKernelEndAddress();
}

size_t ArchCommon::getVESAConsoleHeight()
{
  return 480;
}

size_t ArchCommon::getVESAConsoleWidth()
{
  return 640;
}

pointer ArchCommon::getVESAConsoleLFBPtr(size_t is_paging_set_up __attribute__((unused)))
{
  return ArchBoardSpecific::getVESAConsoleLFBPtr();
}

pointer ArchCommon::getFBPtr(size_t is_paging_set_up __attribute__((unused)))
{
  return getVESAConsoleLFBPtr();
}

size_t ArchCommon::getVESAConsoleBitsPerPixel()
{
  return 16;
}

size_t ArchCommon::getNumUseableMemoryRegions()
{
  return 1;
}

size_t ArchCommon::getUsableMemoryRegion(size_t region, pointer &start_address, pointer &end_address, size_t &type)
{
  return ArchBoardSpecific::getUsableMemoryRegion(region, start_address, end_address, type);
}

Console* ArchCommon::createConsole(size_t count)
{
  ArchBoardSpecific::frameBufferInit();
  return new FrameBufferConsole(count);
}

Stabs2DebugInfo const *kernel_debug_info = 0;

void ArchCommon::initDebug()
{
  extern unsigned char swebdbg_start_address_nr;
  extern unsigned char swebdbg_end_address_nr;

  kernel_debug_info = new SWEBDebugInfo((const char *)&swebdbg_start_address_nr, (const char*)&swebdbg_end_address_nr);
}

//this is just for debugging
extern size_t bss_start_address;
extern size_t bss_end_address;
extern "C" void dumpBss()
{
    size_t start = (size_t) &bss_start_address;
    size_t end = (size_t) &bss_end_address;

    start &= ~LINK_BASE;
    end &= ~LINK_BASE;

    while (start < end + 0x1000)
    {
        kprintfd("%p  ", (void*) start);

        for (size_t index = 0; index < 16; index++)
        {
            kprintfd("%02x ", (uint8) (*((char*) (start++))));
        }

        kprintfd("\n");
    }
}

extern "C" void halt()
{
  asm volatile("wfi");
}


void ArchCommon::idle()
{
  ArchBoardSpecific::onIdle();
  halt();
}

