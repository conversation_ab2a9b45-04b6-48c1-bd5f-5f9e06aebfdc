#include "BDManager.h"
#include "BDVirtualDevice.h"
#include "IDEDriver.h"
#include "MMCDriver.h"
#include "kstring.h"
#include "ArchInterrupts.h"
#include "kprintf.h"

uint32 IDEDriver::doDeviceDetection()
{
  const char* name = "idea";
  MMCDriver* drv = new MMCDriver();
  BDVirtualDevice *bdv = new BDVirtualDevice(drv, 0, drv->getNumSectors(), drv->getSectorSize(), name, true);
  BDManager::getInstance()->addVirtualDevice(bdv);
  debug(IDE_DRIVER, "doDetection: initialized with MMCDriver!\n");
  processMBR(drv, 0, drv->SPT, name);
  return 1;
}

int32 IDEDriver::processMBR(BDDriver * drv, uint32 sector, uint32 SPT, const char *name)
{
  uint32 offset = 0, numsec = 0;
  uint16 buff[256]; // read buffer
  debug(IDE_DRIVER, "processMBR:reading MBR\n");

  static uint32 part_num = 0;
//   char part_num_str[2];
//   char part_name[10];

  uint32 read_res = drv->readSector(sector, 1, (void *) buff);

  if (read_res != 0)
  {
    debug(IDE_DRIVER, "processMBR: drv returned BD_ERROR\n");
    return -1;
  }

  MBR *mbr = (MBR *) buff;

  if (mbr->signature == 0xAA55)
  {
    debug(IDE_DRIVER, "processMBR: | Valid PC MBR | \n");
    FP * fp = (FP *) mbr->parts;
    uint32 i;
    for (i = 0; i < 4; i++, fp++)
    {
      switch (fp->systid)
      {
        case 0x00:
          // do nothing
          break;
        case 0x05: // DOS extended partition
        case 0x0F: // Windows extended partition
        case 0x85: // linux extended partition
          debug(IDE_DRIVER, "ext. part. at: %d \n", fp->relsect);
          if (processMBR(drv, sector + fp->relsect, SPT, name) == -1)
            processMBR(drv, sector + fp->relsect - SPT, SPT, name);
          break;
        default:
          // offset = fp->relsect - SPT;
          offset = fp->relsect;
          numsec = fp->numsect;

          char part_name[6];
          strncpy(part_name, name, 4);
          part_name[4] = part_num + '0';
          part_name[5] = 0;
          part_num++;
          BDVirtualDevice *bdv = new BDVirtualDevice(drv, offset, numsec, drv->getSectorSize(), part_name, true);

          // set Partition Type (FileSystem identifier)
          bdv->setPartitionType(fp->systid);

          BDManager::getInstance()->addVirtualDevice(bdv);
          break;
      }
    }
  }
  else
  {
    debug(IDE_DRIVER, "processMBR: | Invalid PC MBR %d | \n", mbr->signature);
    return -1;
  }

  debug(IDE_DRIVER, "processMBR:, done with partitions \n");
  return 0;
}
