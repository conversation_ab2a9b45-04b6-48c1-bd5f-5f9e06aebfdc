#include "InterruptUtils.h"

#include "ArchBoardSpecific.h"
#include "BDManager.h"
#include "KeyboardManager.h"
#include "new.h"
#include "ArchMemory.h"
#include "ArchThreads.h"
#include "ArchCommon.h"
#include "Console.h"
#include "FrameBufferConsole.h"
#include "Terminal.h"
#include "kprintf.h"
#include "Scheduler.h"
#include "debug_bochs.h"

#include "panic.h"

#include "Thread.h"
#include "ArchInterrupts.h"
#include "backtrace.h"

#include "Loader.h"
#include "Syscall.h"
#include "paging-definitions.h"
#include "PageFaultHandler.h"


extern Console* main_console;

extern ArchThreadRegisters *currentThreadRegisters;
extern Thread *currentThread;

void pageFaultHandler(size_t address, uint32 type NU, size_t exc_syndrome NU)
{
  bool writing = exc_syndrome & (1 << 6);
  bool fetch = type;
  bool present = (exc_syndrome & 0b111111) == 0b0011;  //this is a permission fault

  PageFaultHandler::enterPageFault(address, currentThread->switch_to_userspace_, present , writing, fetch);
}

void timer_irq_handler()
{
  static uint32 heart_beat_value = 0;
  const char* clock = "/-\\|";
  ((FrameBufferConsole*)main_console)->consoleSetCharacter(0,0,clock[heart_beat_value],Console::GREEN);
  heart_beat_value = (heart_beat_value + 1) % 4;

  Scheduler::instance()->incTicks();
  Scheduler::instance()->schedule();
}

void arch_uart1_irq_handler()
{
  kprintfd("arch_uart1_irq_handler\n");
  while(1);
}

void arch_mouse_irq_handler()
{
  kprintfd("arch_mouse_irq_handler\n");
  while(1);
}

void arch_swi_irq_handler(size_t swi)
{
  assert(!ArchInterrupts::testIFSet());

  if (swi == 0xffff) // yield
  {
    Scheduler::instance()->schedule();
  }
  else if (swi == 0x0) // syscall
  {
    currentThread->switch_to_userspace_ = 0;
    currentThreadRegisters = currentThread->kernel_registers_;
    ArchInterrupts::enableInterrupts();
    auto ret = Syscall::syscallException(currentThread->user_registers_->X[0],
                                         currentThread->user_registers_->X[1],
                                         currentThread->user_registers_->X[2],
                                         currentThread->user_registers_->X[3],
                                         currentThread->user_registers_->X[4],
                                         currentThread->user_registers_->X[5]);

    currentThread->user_registers_->X[0] = ret;

    ArchInterrupts::disableInterrupts();
    currentThread->switch_to_userspace_ = 1;
    currentThreadRegisters =  currentThread->user_registers_;
  }
  else
  {
    kprintfd("Invalid SWI: %zx\n",swi);
    assert(false);
  }
}

extern "C" void exceptionHandler(size_t int_id , size_t curr_el NU, size_t exc_syndrome  , size_t fault_address , size_t return_addr NU )
{
  size_t type = int_id & 0x0F;
  size_t instruction_specific_syndrome = exc_syndrome & 0x1FFFFFF;
  size_t exception_class = (exc_syndrome >> 26) & 0x3F;

  assert(!currentThread || currentThread->isStackCanaryOK());
  debug(A_INTERRUPTS, "InterruptUtils::exceptionHandler: type = %zx\n", type);
  assert((currentThreadRegisters->SPSR & (0xE0)) == 0); //check if interrupts disabled

  if (type == ARM_EXC_IRQ)
  {
    ArchBoardSpecific::irq_handler();
  }
  else if (type == ARM_EXC_SYNC)
  {
      if(exception_class == ARM_EXC_CLA_SYSCALL)
      {
          arch_swi_irq_handler(instruction_specific_syndrome);
      }
      else if(exception_class == ARM_EXC_DATA_ABORT_CURR_EL || exception_class == ARM_EXC_DATA_ABORT_LOWER_EL)
      {
    	  pageFaultHandler(fault_address , 0, exc_syndrome);
      }
      else if(exception_class == ARM_EXC_INSTR_ABORT_CURR_EL || exception_class == ARM_EXC_INSTR_ABORT_LOWER_EL)
      {
    	  pageFaultHandler(fault_address , 1, exc_syndrome);
      }
  }
  else
  {
    kprintfd("\nCPU Fault type = %zx\n",type);
    ArchThreads::printThreadRegisters(currentThread,false);
    currentThread->switch_to_userspace_ = 0;
    currentThreadRegisters = currentThread->kernel_registers_;
    ArchInterrupts::enableInterrupts();
    currentThread->kill();
    for(;;);
  }

  assert((currentThreadRegisters->SPSR & 0xE0) == 0);
  assert(currentThread->switch_to_userspace_ == 0 || (currentThreadRegisters->SPSR & 0xF) == 0);
  assert(!currentThread || currentThread->isStackCanaryOK());
}
