#include "ArchThreads.h"
#include "ArchInterrupts.h"
#include "ArchMemory.h"
#include "kprintf.h"
#include "paging-definitions.h"
#include "offsets.h"
#include "Thread.h"
#include "Scheduler.h"
#include "SpinLock.h"

SpinLock global_atomic_add_lock("");

void ArchThreads::initialise()
{
  new (&global_atomic_add_lock) SpinLock("global_atomic_add_lock");
  currentThreadRegisters = (ArchThreadRegisters*) new uint8[sizeof(ArchThreadRegisters)];
  pointer paging_root = VIRTUAL_TO_PHYSICAL_BOOT(((pointer)kernel_paging_level1));
  currentThreadRegisters->TTBR0 = paging_root;
}

void ArchThreads::setAddressSpace(Thread *thread, ArchMemory& arch_memory)
{
  assert(arch_memory.paging_root_page_ != 0);

  size_t ttbr0_value = (LOAD_BASE + arch_memory.paging_root_page_ * PAGE_SIZE) | (((size_t)arch_memory.address_space_id) << 48);

  thread->kernel_registers_->TTBR0 = ttbr0_value;

  if (thread->user_registers_)
    thread->user_registers_->TTBR0 = ttbr0_value;
}

void ArchThreads::createKernelRegisters(ArchThreadRegisters *&info, void* start_function, void* stack)
{
  info = (ArchThreadRegisters*)new uint8[sizeof(ArchThreadRegisters)];
  memset((void*)info, 0, sizeof(ArchThreadRegisters));
  assert(!((pointer)start_function & 0x3));
  info->ELR = (pointer)start_function;
  info->SPSR = 0x60000005;
  info->SP = (pointer)stack & ~0xF;
  info->SP_SM = 0;
  info->X[29] = (pointer)stack & ~0xF; // X29 is the fp
  info->TTBR0 = 0;
}

void ArchThreads::changeInstructionPointer(ArchThreadRegisters *info, void* function)
{
  info->ELR = (pointer)function;
}

void ArchThreads::createUserRegisters(ArchThreadRegisters *&info, void* start_function, void* user_stack, void* kernel_stack)
{
  info = (ArchThreadRegisters*)new uint8[sizeof(ArchThreadRegisters)];
  memset((void*)info, 0, sizeof(ArchThreadRegisters));
  assert(!((pointer)start_function & 0x3));

  info->ELR = (pointer) start_function;
  info->SPSR = 0x60000000;
  info->SP = (pointer) user_stack & ~0xF;
  info->SP_SM = (pointer) kernel_stack & ~0xF;
  info->X[29] = (pointer) user_stack & ~0xF; // X29 is the fp

  info->TTBR0 = 0;
}

void ArchThreads::yield()
{
  asm("SVC #0xffff");
}

size_t ArchThreads::testSetLock(size_t &lock, size_t new_value)
{

	#ifdef VIRTUALIZED_QEMU
    size_t ret = 0;
    //on qemu the exclusive instructions do work
    __atomic_exchange(&lock, &new_value, &ret, __ATOMIC_RELAXED);
	#else
    volatile size_t ret = 0;

    //    this is very terrible but in aarch64 there is no swp and the exclusive instructions
    //    do not work without cache on the cortex-a53 of the raspi3

    bool int_state = ArchInterrupts::disableInterrupts();

    ret = lock;
    lock = new_value;

    if(int_state)
        ArchInterrupts::enableInterrupts();

	#endif

    return ret;
}


uint32 ArchThreads::atomic_add(uint32 &value, int32 increment)
{
  global_atomic_add_lock.acquire();
  uint32 result = value;
  value += increment;
  global_atomic_add_lock.release();
  return result;
}

int32 ArchThreads::atomic_add(int32 &value, int32 increment)
{
  return (int32) ArchThreads::atomic_add((uint32 &) value, increment);
}

uint64 ArchThreads::atomic_add(uint64 &value, int64 increment)
{
  global_atomic_add_lock.acquire();
  uint64 result = value;
  value += increment;
  global_atomic_add_lock.release();
  return result;
}

int64 ArchThreads::atomic_add(int64 &value, int64 increment)
{
  return (int64) ArchThreads::atomic_add((uint64 &) value, increment);
}

void ArchThreads::printThreadRegisters(Thread *thread, bool verbose)
{
  printThreadRegisters(thread,0,verbose);
  printThreadRegisters(thread,1,verbose);
}

void ArchThreads::printThreadRegisters(Thread *thread, uint32 userspace_registers, bool verbose)
{
    ArchThreadRegisters *info = userspace_registers?thread->user_registers_:thread->kernel_registers_;
    if (!info)
    {
      kprintfd("%sThread: %18p, has no %s registers. %s\n",userspace_registers?"  User":"Kernel",thread,userspace_registers?"User":"Kernel",userspace_registers?"":"This should never(!) occur. How did you do that?");
    }
    else if (verbose)
    {
      kprintfd("\t\t%sThread: %10p, info: %10p\n"\
               "\t\t\tTTBR0: %10zx  ELR: %10zx  SP: %10zx  SPSR: %10zx\n"\
               "\t\t\tX0:%10zx X1:%10zx X2:%10zx X3:%10zx X4:%10zx X5:%10zx X6:%10zx X7:%10zx X8:%10zx X9:%10zx X10:%10zx X11:%10zx X12:%10zx\n",
               userspace_registers?"  User":"Kernel",thread,info,info->TTBR0,info->ELR,info->SP,info->SPSR,info->X[0],info->X[1],info->X[2],info->X[3],info->X[4],info->X[5],info->X[6],info->X[7],info->X[8],info->X[9],info->X[10],info->X[11],info->X[12]);

    }
    else
    {
      kprintfd("%sThread: %10p, info: %10p -- TTBR0: %10zx  ELR: %10zx  SP: %10zx  SPSR: %10zx -- X0:%10zx X1:%10zx X2:%10zx X3:%10zx X4:%10zx X5:%10zx X6:%10zx X7:%10zx X8:%10zx X9:%10zx X10:%10zx X11:%10zx X12:%10zx\n",
               userspace_registers?"  User":"Kernel",thread,info,info->TTBR0,info->ELR,info->SP,info->SPSR,info->X[0],info->X[1],info->X[2],info->X[3],info->X[4],info->X[5],info->X[6],info->X[7],info->X[8],info->X[9],info->X[10],info->X[11],info->X[12]);
    }

}



void ArchThreads::atomic_set(size_t& target, size_t value)
{
    testSetLock(target,value);
}

void ArchThreads::atomic_set(int32& target, int32 value)
{
  atomic_set((size_t&)target, (size_t)value);
}

extern "C" void threadStartHack();

void ArchThreads::debugCheckNewThread(Thread* thread)
{
  assert(currentThread);
  ArchThreads::printThreadRegisters(currentThread,false);
  ArchThreads::printThreadRegisters(thread,false);
  assert(thread->kernel_registers_ != 0 && thread->kernel_registers_ != currentThread->kernel_registers_ && "all threads need to have their own register sets");
  assert(thread->kernel_registers_->SP_SM == 0 && "kernel register set needs no backup of kernel SP_SM");
  assert(thread->kernel_registers_->SP == thread->kernel_registers_->X[29] && "new kernel stack must be empty");
  assert(thread->kernel_registers_->SP != currentThread->kernel_registers_->SP && thread->kernel_registers_->X[29] != currentThread->kernel_registers_->X[29] && "all threads need their own stack");
  if (thread->user_registers_ == 0)
    return;
  assert(thread->kernel_registers_->ELR == 0 && "user threads should not start execution in kernel mode");
  assert(thread->switch_to_userspace_ == 1 && "new user threads must start in userspace");
  assert(thread->kernel_registers_->SP == thread->user_registers_->SP_SM && "SP_SM should point to kernel stack");
  assert(thread->kernel_registers_->TTBR0 == thread->user_registers_->TTBR0 && "user and kernel part of a thread need to have the same pageing root");
  assert(thread->user_registers_->ELR != 0 && "user eip needs to be valid... execution will start there");
  if (currentThread->user_registers_ == 0)
    return;
  assert(currentThread->user_registers_->SP_SM != thread->user_registers_->SP_SM && "no 2 threads may have the same esp0 value");
}
