#include "types.h"
#include "ArchInterrupts.h"
#include "kprintf.h"
#include "kstring.h"
#include "InterruptUtils.h"
#include "ArchThreads.h"
#include "ArchBoardSpecific.h"
#include "Thread.h"

extern "C" void exceptionHandler(size_t int_id, size_t curr_el, size_t exc_syndrome, size_t fault_address, size_t return_addr);
extern "C" const size_t kernel_sp_struct_offset = (size_t)&((ArchThreadRegisters *)NULL)->SP_SM;
extern uint8 boot_stack[];

//in interrupt_entry.S is the code for the actual context switching
extern "C" size_t interruptEntry(size_t int_id, size_t curr_el, size_t exc_syndrome, size_t fault_address, size_t return_addr )
{
	exceptionHandler(int_id, curr_el, exc_syndrome, fault_address, return_addr);

	return currentThread->switch_to_userspace_;
}

//in interrupt_entry.S is the code for the actual context switching
extern "C" size_t interruptEntryDebug(size_t int_id, size_t curr_el, size_t exc_syndrome, size_t fault_address, size_t return_addr )
{
    kprintfd("interrupt %zd   %zd  %zX   %zX   %zX  \n",int_id,curr_el,exc_syndrome,fault_address,return_addr);

    while(1)asm("nop");

    return 0;
}

uint32 arm4_cpsrget()
{
  uint32 r =0;

  asm volatile ("MRS %[ps], DAIF" : [ps]"=r" (r));
  return r;
}

void arm4_cpsrset(uint32 r)
{
    asm volatile ("MSR DAIF, %[ps]" : : [ps]"r" (r));
}

void ArchInterrupts::initialise()
{

}

void ArchInterrupts::enableTimer()
{
  ArchBoardSpecific::enableTimer();
}

void ArchInterrupts::setTimerFrequency(uint32 freq)
{
  ArchBoardSpecific::setTimerFrequency(freq);
}

void ArchInterrupts::disableTimer()
{
  ArchBoardSpecific::disableTimer();
}

void ArchInterrupts::enableKBD()
{
  ArchBoardSpecific::enableKBD();
}

void ArchInterrupts::disableKBD()
{
  ArchBoardSpecific::disableKBD();
}

void ArchInterrupts::enableInterrupts()
{
  arm4_cpsrset(arm4_cpsrget() & ~((1 << 8) | (1 << 7) | (1 << 6)));
}

bool ArchInterrupts::disableInterrupts()
{
  uint32 r = arm4_cpsrget();
  arm4_cpsrset(r | ((1 << 8) | (1 << 7) | (1 << 6)));
  return !(r & ((1 << 8) | (1 << 7) | (1 << 6)));
}

bool ArchInterrupts::testIFSet()
{
  return !(arm4_cpsrget() & ((1 << 8) | (1 << 7) | (1 << 6)));
}

void ArchInterrupts::yieldIfIFSet()
{
  if (system_state == RUNNING && currentThread && testIFSet())
  {
    ArchThreads::yield();
  }
  else
  {
    __asm__ __volatile__("nop");
  }
}

