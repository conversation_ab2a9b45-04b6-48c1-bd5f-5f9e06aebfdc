1.)
    The SD card needs to have 2 fat32 Partitions with 50MBs each (the second will be overwritten with minix).
    Use Gparted to change the partition layout.
    use mkfs.minix <path to partition> to format the second partition to minix

2.)
    Mount both partitions.

3.) 
    Copy the content from the boot folder to the first partition

4.)
    Go into the build folder of sweb and execute "make image", this will create the kernel8.img
    Copy the kernel8.img also onto the first partition.

5.)
    Copy the userprogramms and all the data you want to access within sweb onto the second partition.


Finish.
  
