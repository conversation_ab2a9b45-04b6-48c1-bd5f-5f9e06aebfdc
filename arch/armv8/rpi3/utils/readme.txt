Instructions to get the ToolChain and Gdb

To build and debug sweb with armv8 debian/ubuntu/mint based linux distributions are recommended.

The compiler can be taken from the repo:

sudo apt-get install gcc-aarch64-linux-gnu
sudo apt-get install g++-aarch64-linux-gnu

The qemu version with the raspberry pi3 emulation is 2.12. If it is not contained within the repo of your distribution use the
"build_qemu_rpi.sh" script to clone and build the qemu.

For debugging use the android-aarch64-gdb the gdb-multiarch does not work properly, just clone it from google or from this link:
https://github.com/Meninblack007/aarch64-linux-android-4.9.git
