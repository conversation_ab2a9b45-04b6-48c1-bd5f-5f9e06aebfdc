# install dependencies
sudo apt-get install zlib1g-dev libglib2.0-dev zlib1g-dev libpixman-1-dev
sudo apt-get install libaio-dev libbluetooth-dev libbrlapi-dev libbz2-dev
sudo apt-get install libcap-dev libcap-ng-dev libcurl4-gnutls-dev libgtk-3-dev
sudo apt-get install libibverbs-dev libjpeg8-dev libncurses5-dev libnuma-dev
sudo apt-get install librbd-dev librdmacm-dev
sudo apt-get install libsasl2-dev libsdl1.2-dev libseccomp-dev libsnappy-dev libssh2-1-dev
sudo apt-get install libvde-dev libvdeplug-dev libvte-2.90-dev libxen-dev liblzo2-dev
# download the archive 
wget https://download.qemu.org/qemu-2.12.0.tar.xz
# extract the archive
tar xvJf qemu-2.12.0.tar.xz
# go into the qemu folder
cd qemu-2.12.0
# setup the build environment to use the aarch64 target
./configure --target-list=aarch64-softmmu --enable-modules --enable-tcg-interpreter --enable-debug-tcg --enable-debug
# build the source code with 8 threads
make -j8
# copy the build code to the bin and ld locations
sudo make install
# check if the now installed QEMU supports the raspi3 device
qemu-system-aarch64 -M help