#include "ArchBoardSpecific.h"

#include "KeyboardManager.h"
#include "board_constants.h"
#include "InterruptUtils.h"
#include "ArchCommon.h"
#include "assert.h"
#include "offsets.h"
#include "ArchInterrupts.h"
#include "Scheduler.h"
#include "FrameBufferConsole.h"
#include "kprintf.h"
#include "paging-definitions.h"
#include "ArchMemory.h"

#define PHYSICAL_MEMORY_AVAILABLE (PAGE_ENTRIES * PAGE_SIZE * 4)

pointer framebuffer;

pointer ArchBoardSpecific::getVESAConsoleLFBPtr()
{
  return framebuffer;
}

size_t ArchBoardSpecific::getUsableMemoryRegion(size_t region __attribute__((unused)), pointer &start_address, pointer &end_address, size_t &type)
{
    start_address = 0;
    end_address = PHYSICAL_MEMORY_AVAILABLE;
    type = 1;

    return 0;
}

void ArchBoardSpecific::frameBufferInit()
{
    //for detailed description about the mailboxsystem:
    //https://github.com/raspberrypi/firmware/wiki/Mailboxes

    uint32 mailbox_buffer[22]  __attribute__ ((aligned (0x20)));

    mailbox_buffer[1] = 0;

    mailbox_buffer[2] = MAILBOX_ATTR_SET_PHYSICAL_WIDTH_HIGHT;
    mailbox_buffer[3] = 8;
    mailbox_buffer[4] = 0;
    mailbox_buffer[5] = (uint32)ArchCommon::getVESAConsoleWidth();
    mailbox_buffer[6] = (uint32)ArchCommon::getVESAConsoleHeight();

    mailbox_buffer[7] = MAILBOX_ATTR_SET_VIRTUAL_WIDTH_HIGHT;
    mailbox_buffer[8] = 8;
    mailbox_buffer[9] = 0;
    mailbox_buffer[10] = (uint32) ArchCommon::getVESAConsoleWidth();
    mailbox_buffer[11] = (uint32) ArchCommon::getVESAConsoleHeight();

    mailbox_buffer[12] = MAILBOX_ATTR_SET_DEPTH;
    mailbox_buffer[13] = 4;
    mailbox_buffer[14] = 0;
    mailbox_buffer[15] = (uint32) ArchCommon::getVESAConsoleBitsPerPixel();

    mailbox_buffer[16] = MAILBOX_ATTR_ALLOC_BUFFER;
    mailbox_buffer[17] = 8;
    mailbox_buffer[18] = 0;
    mailbox_buffer[19] = 0;
    mailbox_buffer[20] = 0;

    mailbox_buffer[21] = 0;

    mailbox_buffer[0] = sizeof(mailbox_buffer);

    uint32* mailbox_read   = (uint32*) MAILBOX_READ;
    uint32* mailbox_write  = (uint32*) MAILBOX_WRITE;
    uint32* mailbox_status = (uint32*) MAILBOX_STATUS;

    //packet consists of address plus mailbox channel
    uint32 packet = (uint32) ((VIRTUAL_TO_PHYSICAL_BOOT((size_t)&mailbox_buffer) & ~0xF) | MAILBOX_CHANNEL_PROPERTY_TAGS);


    while (*mailbox_status & MAILBOX_FULL) //wait until mailbox is empty
        asm volatile("nop");

    *mailbox_write = packet; //throw packet into mailbox

    while(true)
    {
        while (*mailbox_status & MAILBOX_EMPTY) //wait until mailbox is empty
            asm volatile("nop");

        if(packet == *mailbox_read) //checked if finished
            break;
    }

    mailbox_buffer[19] &= 0x3FFFFFFF; //apply mask to get the correct physical offset

    assert(mailbox_buffer[1] == (uint32)MAILBOX_RESPONSE);
    assert(mailbox_buffer[19] != 0);

    framebuffer = IDENT_MAPPING_START | mailbox_buffer[19];
}

void ArchBoardSpecific::onIdle()
{

}

size_t timer_loading_value = 0;
void ArchBoardSpecific::enableTimer()
{
#ifdef VIRTUALIZED_QEMU
        size_t timer_prescaler = 20;     //1 is 1 second 2 is 500ms
        size_t timer_frequency = 0;

        asm volatile ("MRS %[ps], CNTFRQ_EL0" : [ps]"=r" (timer_frequency));   //get frequency

        timer_loading_value = timer_frequency / timer_prescaler;

        asm volatile ("MSR CNTV_TVAL_EL0, %[ps]" : : [ps]"r" (timer_loading_value));  //set compare register

        //route interrupt to core0
        //https://www.raspberrypi.org/documentation/hardware/raspberrypi/bcm2836/QA7_rev3.4.pdf

        uint32* core0_irq_control = (uint32*)(IDENT_MAPPING_START | ARM_CORE0_TIM_IRQCNTL);

        *core0_irq_control = (1 << 3);

        asm volatile ("MSR CNTV_CTL_EL0, %[ps]" : : [ps]"r" (0x01)); //enable timer
#else
        uint32* pic_base_enable = (uint32*)(IDENT_MAPPING_START | PYHSICAL_MMIO_OFFSET |0xB218);
        *pic_base_enable = 0x1;

        uint32* timer_load = (uint32*)(IDENT_MAPPING_START | PYHSICAL_MMIO_OFFSET |0xB400);
        //uint32* timer_value = timer_load + 1;
        *timer_load = 0x1000;
        uint32* timer_control = timer_load + 2;
        *timer_control = (1 << 7) | (1 << 5) | (1 << 2);
        uint32* timer_clear = timer_load + 3;
        *timer_clear = 0x1;
#endif
}

void ArchBoardSpecific::setTimerFrequency(uint32 freq)
{
  (void)freq;
  debug(A_BOOT, "Sorry, setTimerFrequency not implemented!\n");
}

void ArchBoardSpecific::disableTimer()
{
}

void ArchBoardSpecific::enableKBD()
{
    uint32* irq_enable = (uint32*)(IDENT_MAPPING_START | PYHSICAL_MMIO_OFFSET |0xB214);
    *irq_enable |= 1 << 25;

    uint32* gpu_int_routing = (uint32*)(IDENT_MAPPING_START | 0x4000000C);
    *gpu_int_routing = 0;
}

void ArchBoardSpecific::disableKBD()
{

}

void ArchBoardSpecific::keyboard_irq_handler()
{
  KeyboardManager::instance()->serviceIRQ();
}

void resetTimer()
{
    asm volatile ("MSR CNTV_TVAL_EL0, %[ps]" : : [ps]"r" (timer_loading_value)); //reset value
}

extern void timer_irq_handler();

void ArchBoardSpecific::timer0_irq_handler()
{
#ifdef VIRTUALIZED_QEMU
	resetTimer();
	timer_irq_handler();
#else
	uint32* timer_raw = (uint32*) (IDENT_MAPPING_START | PYHSICAL_MMIO_OFFSET | 0xB410);
	if ((*timer_raw & 0x1) != 0) {
		assert(!ArchInterrupts::testIFSet());
		uint32* timer_clear = (uint32*) (IDENT_MAPPING_START | PYHSICAL_MMIO_OFFSET | 0xB40C);
		*timer_clear = 0x1;

		timer_irq_handler();
	}
#endif
}

#define IRQ(X) ((*pic) & (1 << X))
void ArchBoardSpecific::irq_handler()
{
#ifdef VIRTUALIZED_QEMU
    uint32* core0_irq_data = (uint32*) (IDENT_MAPPING_START
            | ARM_CORE0_IRQ_SOURCE);

    if (*core0_irq_data & (1 << 3))
        timer0_irq_handler();
#else
	uint32* pic = (uint32*) (IDENT_MAPPING_START | PYHSICAL_MMIO_OFFSET | 0xB200);
	if (IRQ(0))
		timer0_irq_handler();
#endif

    uint32* core0_int_src = (uint32*) (IDENT_MAPPING_START | 0x40000060);
    uint32* irq_int_pendd2 = (uint32*) (IDENT_MAPPING_START | PYHSICAL_MMIO_OFFSET | 0xB208);

    if((*core0_int_src & (1 << 8)) && (*irq_int_pendd2 & (1 << 25)))
    {
        keyboard_irq_handler();
    }
}



