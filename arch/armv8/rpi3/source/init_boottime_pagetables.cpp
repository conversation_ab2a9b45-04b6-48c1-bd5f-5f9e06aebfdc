#include "types.h"
#include "paging-definitions.h"
#include "offsets.h"
#include "init_boottime_pagetables.h"

extern "C" void uartWritePreboot(const char *str);

//see ArchMemory.cpp
extern Level1Entry kernel_paging_level1[];
extern Level2Entry kernel_paging_level2[];
extern Level3Entry kernel_paging_level3[];

extern "C" void initialiseBootTimePaging()
{
    Level1Entry *kernel_paging_level1_start = (Level1Entry*)(((size_t)&kernel_paging_level1) & ~LINK_BASE);
    Level2Entry *kernel_paging_level2_start = (Level2Entry*)(((size_t)&kernel_paging_level2) & ~LINK_BASE);
    Level3Entry *kernel_paging_level3_start = (Level3Entry*)(((size_t)&kernel_paging_level3) & ~LINK_BASE);

    Level2Entry *kernel_paging_level2_ident_start = kernel_paging_level2_start;
    Level2Entry *kernel_paging_level2_kernel_start = kernel_paging_level2_start + (2 * PAGE_ENTRIES);

    //clear the paging levels
    for(size_t index = 0; index < PAGE_ENTRIES * NUMBER_KERNEL_PAGEING_TABLES; index++)
        ((size_t *)kernel_paging_level1_start)[index] = 0;


    //for the ident_mapping
    kernel_paging_level1_start[0].entry_descriptor_type = ENTRY_DESCRIPTOR_TABLE;
    kernel_paging_level1_start[0].table_address = ((size_t)kernel_paging_level2_ident_start) / PAGE_SIZE;

    kernel_paging_level1_start[1].entry_descriptor_type = ENTRY_DESCRIPTOR_TABLE;
    kernel_paging_level1_start[1].table_address = ((size_t)(kernel_paging_level2_ident_start + PAGE_ENTRIES)) / PAGE_SIZE;

    //for the kernel pages
    kernel_paging_level1_start[256].entry_descriptor_type = ENTRY_DESCRIPTOR_TABLE;
    kernel_paging_level1_start[256].table_address = ((size_t)kernel_paging_level2_kernel_start) / PAGE_SIZE;


    size_t mmio_start_page = PYHSICAL_MMIO_OFFSET >> 21; // shift 9 + 12 bit to get 2MB pages


    // map the whole 2GB physical memory 1:1 required for boot and the ident
    for (size_t index = 0; index < (PAGE_ENTRIES * 2); index++)
    {
        mapBootTime2MBPage(kernel_paging_level2_ident_start, index, index, index >= mmio_start_page ? MEMORY_ATTR_MMIO : MEMORY_ATTR_NON_CACHABLE);

        //set shareability based on Memory type(mmio/std_ram)
        kernel_paging_level2_ident_start[index].block.shareability_field = index >= mmio_start_page ? SHARE_OSH : SHARE_ISH;
    }

    //map all the level3 pages to the level2 page
    for(size_t index = 0; index < KERNEL_LEVEL3_TABLES; index++)
    {
        kernel_paging_level2_kernel_start[index].table.entry_descriptor_type = ENTRY_DESCRIPTOR_TABLE;
        kernel_paging_level2_kernel_start[index].table.table_address = ((size_t) (kernel_paging_level3_start) / PAGE_SIZE) + index;
    }

    //map the first 2MB for kernel memory
    for(size_t index = 0; index < PAGE_ENTRIES; index++)
        mapBootTimePage(kernel_paging_level3_start,index, index);


    //set the mmu l0 selection registers, after boot ttbr0_el1 will be cleared and used for the user space
    asm volatile ("msr ttbr0_el1, %0" : : "r" (((size_t)kernel_paging_level1_start)+1));
    asm volatile ("msr ttbr1_el1, %0" : : "r" (((size_t)kernel_paging_level1_start)+1));

}

extern "C" void mapBootTime2MBPage(Level2Entry *lvl2_start, size_t table_index, size_t physical_page,size_t mem_attr)
{
    lvl2_start[table_index].block.entry_descriptor_type = ENTRY_DESCRIPTOR_BLOCK;
    lvl2_start[table_index].block.memory_attributes_index = mem_attr;
    lvl2_start[table_index].block.non_secure = 0;
    lvl2_start[table_index].block.access_permissions = 0;
    lvl2_start[table_index].block.shareability_field = SHARE_ISH;
    lvl2_start[table_index].block.access_flag = ACCESS_FLAG;
    lvl2_start[table_index].block.not_global = 0;
    lvl2_start[table_index].block.reserved_1 = 0;
    lvl2_start[table_index].block.block_address = physical_page;
    lvl2_start[table_index].block.reserved_2 = 0;
    lvl2_start[table_index].block.contiguous = 0; // maybe enable in the future, would speed things up
    lvl2_start[table_index].block.privileged_execute_never = 0;
    lvl2_start[table_index].block.execute_never = 0;
    lvl2_start[table_index].block.ingored_1 = 0;
    lvl2_start[table_index].block.ignored_2 = 0;
}

extern "C" void mapBootTimePage(Level3Entry *lvl3_start, size_t table_index, size_t physical_page)
{
    lvl3_start[table_index].entry_descriptor_type = ENTRY_DESCRIPTOR_PAGE;
    lvl3_start[table_index].memory_attributes_index = MEMORY_ATTR_STD;
    lvl3_start[table_index].non_secure = 0;
    lvl3_start[table_index].access_permissions = 0;
    lvl3_start[table_index].shareability_field = SHARE_ISH;
    lvl3_start[table_index].access_flag = ACCESS_FLAG;
    lvl3_start[table_index].not_global = 0;
    lvl3_start[table_index].page_address = physical_page;
    lvl3_start[table_index].reserved_1 = 0;
    lvl3_start[table_index].contiguous = 0;  //maybe enable in the future, would speed things up
    lvl3_start[table_index].privileged_execute_never = 0;
    lvl3_start[table_index].execute_never = 0;
    lvl3_start[table_index].ingored_1 = 0;
    lvl3_start[table_index].ignored_2 = 0;
}

extern "C" void removeBootTimeIdentMapping()
{
    asm volatile ("msr ttbr0_el1, %0" : : "r" (0));
}
