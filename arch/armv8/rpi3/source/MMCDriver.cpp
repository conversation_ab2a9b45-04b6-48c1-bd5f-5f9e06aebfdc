#include "BDManager.h"
#include "BDRequest.h"
#include "MMCDriver.h"
#include "ArchInterrupts.h"
#include "offsets.h"
#include "Scheduler.h"
#include "kprintf.h"

struct MMCI {
    uint32 arg2;
    uint32 blksizecnt;
    uint32 arg1;
    uint32 cmdtm;
    uint32 resp0;
    uint32 resp1;
    uint32 resp2;
    uint32 resp3;
    uint32 data;
    uint32 status;
    uint32 control0;
    uint32 control1;
    uint32 interrupt;
    uint32 irpt_mask;
    uint32 irpt_en;
    uint32 control2;
    uint32_t cap0;
    uint32_t cap1;
    uint32_t rsvd1;
    uint32_t rsvd2;
    uint32 force_irpt;
    uint8_t rsvd3[0x1c];
    uint32_t boot_timeout;
    uint32_t dbg_sel;
    uint32_t rsvd4;
    uint32_t rsvd5;
    uint32_t exrdfifo_cfg;
    uint32_t exrdfifo_en;
    uint32_t tune_step;
    uint32_t tune_steps_std;
    uint32_t tune_steps_ddr;
    uint8_t rsvd6[80];
    uint32_t spi_int_spt;
    uint32_t rsvd7[2];
    uint32_t slotisr_ver;
}__attribute__((packed, aligned(4)));

struct MMCI* mmci = (struct MMCI*) (IDENT_MAPPING_START | PYHSICAL_MMIO_OFFSET |0x00300000);
volatile GpioRegisters *gpio = (GpioRegisters*)(IDENT_MAPPING_START | GPIO_REGS_BASE);

//the MMC code is from:
//https://github.com/bztsrc/raspi3-tutorial/blob/master/0B_readsector/sd.c
//and rewritten to make it more readable

//for also good info about the rpi3 mmc driver see:
//https://github.com/LdB-ECM/Raspberry-Pi/blob/master/SD_FAT32/SDCard.c

// command flags
#define CMD_NEED_APP        0x80000000
#define CMD_RSPNS_48        0x00020000
#define CMD_ERRORS_MASK     0xfff9c004
#define CMD_RCA_MASK        0xffff0000

// COMMANDs
#define CMD_GO_IDLE         0x00000000
#define CMD_ALL_SEND_CID    0x02010000
#define CMD_SEND_REL_ADDR   0x03020000
#define CMD_CARD_SELECT     0x07030000
#define CMD_SEND_IF_COND    0x08020000
#define CMD_STOP_TRANS      0x0C030000
#define CMD_READ_SINGLE     0x11220010
#define CMD_WRITE_SINGLE    0x18200000
#define CMD_READ_MULTI      0x12220032
#define CMD_SET_BLOCKCNT    0x17020000
#define CMD_APP_CMD         0x37000000
#define CMD_SET_BUS_WIDTH   (0x06020000 | CMD_NEED_APP)
#define CMD_SEND_OP_COND    (0x29020000 | CMD_NEED_APP)
#define CMD_SEND_SCR        (0x33220010 | CMD_NEED_APP)

// STATUS register settings
#define SR_READ_AVAILABLE   0x00000800
#define SR_DAT_INHIBIT      0x00000002
#define SR_CMD_INHIBIT      0x00000001
#define SR_APP_CMD          0x00000020

// INTERRUPT register settings
#define INT_DATA_TIMEOUT    0x00100000
#define INT_CMD_TIMEOUT     0x00010000
#define INT_READ_RDY        0x00000020
#define INT_WRITE_RDY       0x00000010
#define INT_CMD_DONE        0x00000001

#define INT_ERROR_MASK      0x017E8000

// CONTROL register settings
#define C0_SPI_MODE_EN      0x00100000
#define C0_HCTL_HS_EN       0x00000004
#define C0_HCTL_DWITDH      0x00000002

#define C1_SRST_DATA        0x04000000
#define C1_SRST_CMD         0x02000000
#define C1_SRST_HC          0x01000000
#define C1_TOUNIT_DIS       0x000f0000
#define C1_TOUNIT_MAX       0x000e0000
#define C1_CLK_GENSEL       0x00000020
#define C1_CLK_EN           0x00000004
#define C1_CLK_STABLE       0x00000002
#define C1_CLK_INTLEN       0x00000001

// SLOTISR_VER values
#define HOST_SPEC_NUM       0x00ff0000
#define HOST_SPEC_NUM_SHIFT 16
#define HOST_SPEC_V3        2
#define HOST_SPEC_V2        1
#define HOST_SPEC_V1        0

// SCR flags
#define SCR_SD_BUS_WIDTH_4  0x00000400
#define SCR_SUPP_SET_BLKCNT 0x02000000
// added by my driver
#define SCR_SUPP_CCS        0x00000001

#define ACMD41_VOLTAGE      0x00ff8000
#define ACMD41_CMD_COMPLETE 0x80000000
#define ACMD41_CMD_CCS      0x40000000
#define ACMD41_ARG_HC       0x51ff8000


#define SD_OK                0
#define SD_TIMEOUT          -1
#define SD_ERROR            -2


size_t sd_scr[2], sd_rca, sd_hv;

uint32 mmc_error = 0;

//wait for given ammount of milliseconds
void NO_OPTIMIZE mmcWaitMicroSeconds(size_t milliseconds)
{
    size_t frequency = 0;
    size_t timer_value = 0;
    size_t curr_value = 0;

    asm volatile ("mrs %0, cntfrq_el0" : "=r"(frequency));   // get the timer frequency
    asm volatile ("mrs %0, cntpct_el0" : "=r"(timer_value)); // get the current timer value

    timer_value += ((frequency / 1000) * milliseconds) / 1000;

    do
    {
        asm volatile ("mrs %0, cntpct_el0" : "=r"(curr_value));
    }while(curr_value < timer_value);
}

//wait for given ammount of cycles
void NO_OPTIMIZE mmcWaitCycles(uint32 cycles)
{
    while(cycles--)
        asm volatile("nop");
}

// Wait for data or command ready
int NO_OPTIMIZE mmcGetStatus(uint32 mask)
{
    int cnt = 500000;

    while((mmci->status & mask) && !(mmci->interrupt & INT_ERROR_MASK) && cnt--)
        mmcWaitMicroSeconds(1);

    mmcWaitMicroSeconds(1);
    return (cnt <= 0 || (mmci->interrupt  & INT_ERROR_MASK)) ? SD_ERROR : SD_OK;
}

// Wait for interrupt
int NO_OPTIMIZE mmcWaifForInterrupt(uint32 mask)
{
    uint32 tmp_value = 0;

    int cnt = 1000000;

    while(!(mmci->interrupt & (mask | INT_ERROR_MASK)) && cnt--)
        mmcWaitMicroSeconds(1);

    tmp_value = mmci->interrupt;

    if(cnt <= 0 || (tmp_value & INT_CMD_TIMEOUT) || (tmp_value & INT_DATA_TIMEOUT) )
    {
        kprintfd("%d\n",cnt);
        assert(false);
        mmci->interrupt = tmp_value;
        return SD_TIMEOUT;
    }
    else if(tmp_value & INT_ERROR_MASK)
    {
        assert(false);
        mmci->interrupt = tmp_value;
        return SD_ERROR;
    }

    mmci->interrupt = mask;
    return 0;
}

//send command to sd
int NO_OPTIMIZE mmcSendCommand(uint32 code, uint32 arg)
{
    uint32 tmp_value = 0;
    mmc_error = SD_OK;

    if(code & CMD_NEED_APP)
    {
        tmp_value = mmcSendCommand(CMD_APP_CMD | (sd_rca ? CMD_RSPNS_48 : 0), sd_rca);

        if(sd_rca && !tmp_value)
            assert(false && "MMC Error: failed to send SD APP command");

        code &= ~CMD_NEED_APP;
    }

    if(mmcGetStatus(SR_CMD_INHIBIT))
        assert(false && "MMC ERROR: EMMC busy");

    debug(MMC_DRIVER, "MMC: Send Command: %x  %x\n", code , arg);
    //mmcWaitMicroSeconds(2600000);
    mmcWaitCycles(10000);

    mmci->interrupt=(uint32)mmci->interrupt;
    mmci->arg1=arg;
    mmci->cmdtm = code;

    if(code==CMD_SEND_OP_COND)
        mmcWaitMicroSeconds(1000);
    else if(code==CMD_SEND_IF_COND || code==CMD_APP_CMD)
        mmcWaitMicroSeconds(100);

    if((tmp_value=mmcWaifForInterrupt(INT_CMD_DONE)))
        assert(false && "MMC ERROR: failed to send EMMC command");

    tmp_value = mmci->resp0;

    if(code == CMD_GO_IDLE || code == CMD_APP_CMD)
        return 0;
    else if(code == (CMD_APP_CMD | CMD_RSPNS_48))
        return tmp_value & SR_APP_CMD;
    else if(code == CMD_SEND_OP_COND)
        return tmp_value;
    else if(code == CMD_SEND_IF_COND)
        return tmp_value == arg ? SD_OK : SD_ERROR;
    else if(code == CMD_ALL_SEND_CID)
    {
        tmp_value |= mmci->resp3;
        tmp_value |= mmci->resp2;
        tmp_value |= mmci->resp1;
        return tmp_value;
    }
    else if(code == CMD_SEND_REL_ADDR)
    {
        mmc_error = CMD_ERRORS_MASK &
               (((tmp_value & 0x1fff) << 0)
               |((tmp_value & 0x2000) << 6)
               |((tmp_value & 0x4000) << 8)
               |((tmp_value & 0x8000) << 8));

        return tmp_value & CMD_RCA_MASK;
    }

    return tmp_value & CMD_ERRORS_MASK;
}

//read block from sdcard
int NO_OPTIMIZE mmcReadBlock(uint32 block_address, uint8 *buffer)
{
    debug(MMC_DRIVER,"MMC: Reading Block: %x with buffer: %p  from mmc card\n", block_address, buffer);

    assert(mmcGetStatus(SR_DAT_INHIBIT) == 0 && "MMC ERROR: Timeout");

    uint32 *buf = (uint32*)buffer;

    mmci->blksizecnt = (1 << 16) | 512;

    if(sd_scr[0] & SCR_SUPP_CCS)
    {
        mmcSendCommand(CMD_READ_SINGLE,block_address);
        assert(mmc_error == 0 && "");
    }

    if(!(sd_scr[0] & SCR_SUPP_CCS))
    {
        mmcSendCommand(CMD_READ_SINGLE,block_address * 512);
        assert(mmc_error == 0 && "");
    }

    assert(mmcWaifForInterrupt(INT_READ_RDY) == 0 && "MMC ERROR: Timeout while waiting for ready to read");

    for(int index = 0; index < 128; index++)
        buf[index] = mmci->data;

    return 0;
}

//read block from sdcard
int NO_OPTIMIZE mmcWriteBlock(uint32 block_address, uint8 *buffer)
{
    debug(MMC_DRIVER,"MMC: Reading Block: %x with buffer: %p  from mmc card\n", block_address, buffer);

    assert(mmcGetStatus(SR_DAT_INHIBIT) == 0 && "MMC ERROR: Timeout");

    uint32 *buf = (uint32*)buffer;

    mmci->blksizecnt = (1 << 16) | 512;

    if(sd_scr[0] & SCR_SUPP_CCS)
    {
        mmcSendCommand(CMD_WRITE_SINGLE, block_address);
        assert(mmc_error == 0 && "");
    }

    if(!(sd_scr[0] & SCR_SUPP_CCS))
    {
        mmcSendCommand(CMD_WRITE_SINGLE, block_address * 512);
        assert(mmc_error == 0 && "");
    }

    assert(mmcWaifForInterrupt(INT_WRITE_RDY) == 0 && "MMC ERROR: Timeout while waiting for ready to read");

    for(int index = 0; index < 128; index++)
        mmci->data = buf[index];

    return 0;
}

//set the mmc clock frequency
int NO_OPTIMIZE mmcSetClock(uint32 f)
{
    uint32 divisor, c = 41666666 / f, x, shift = 32, h = 0;

    int cnt = 100000;

    while((mmci->status & (SR_CMD_INHIBIT|SR_DAT_INHIBIT)) && cnt--)
        mmcWaitMicroSeconds(10);

    assert(cnt > 0 && "MMC ERROR: timeout waiting for inhibit flag");

    mmci->control1 &= ~C1_CLK_EN;

    mmcWaitMicroSeconds(10);

    x = c - 1;

    if(!x)
        shift = 0;
    else
    {
        if(!(x & 0xffff0000u)) { x <<= 16; shift -= 16; }
        if(!(x & 0xff000000u)) { x <<= 8;  shift -= 8; }
        if(!(x & 0xf0000000u)) { x <<= 4;  shift -= 4; }
        if(!(x & 0xc0000000u)) { x <<= 2;  shift -= 2; }
        if(!(x & 0x80000000u)) { x <<= 1;  shift -= 1; }
        if(shift > 0) shift--;
        if(shift > 7) shift = 7;
    }

    if(sd_hv > HOST_SPEC_V2)
        divisor = c;
    else
        divisor = (1 << shift);

    if(divisor <= 2)
    {
        divisor = 2;
        shift = 0;
    }

    debug(MMC_DRIVER, "MMC Clock divisor: %x  shift: %x\n", divisor, shift);

    if(sd_hv > HOST_SPEC_V2)
        h = (divisor & 0x300) >> 2;

    divisor = (((divisor & 0x0ff) << 8) | h);

    mmci->control1 = (mmci->control1 & 0xffff003f) | divisor;
    mmcWaitMicroSeconds(10);
    mmci->control1 |= C1_CLK_EN;
    mmcWaitMicroSeconds(10);

    cnt = 10000;

    while(!(mmci->control1 & C1_CLK_STABLE) && cnt--)
        mmcWaitMicroSeconds(10);

    assert(cnt > 0 && "MMC ERROR: failed to get stable clock");

    return SD_OK;
}

//init the mmc
int NO_OPTIMIZE mmcInit()
{
    uint32_t tmp_val = 0;

    // GPIO_CD
    tmp_val = gpio->GPFSEL4;
    tmp_val &= ~(7 << (7 * 3));
    gpio->GPFSEL4 = tmp_val;
    gpio->GPPUD = 2;
    mmcWaitCycles(150);
    gpio->GPPUDCLK1 = (1 << 15);
    mmcWaitCycles(150);
    gpio->GPPUD = 0;
    gpio->GPPUDCLK1 = 0;
    tmp_val = gpio->GPHEN1;
    tmp_val |= (1 << 15);
    gpio->GPHEN1 = tmp_val;

    // GPIO_CLK, GPIO_CMD
    tmp_val = gpio->GPFSEL4;
    tmp_val |= (7 << (8 * 3)) | (7 << (9 * 3));
    gpio->GPFSEL4 = tmp_val;
    gpio->GPPUD = 2;
    mmcWaitCycles(150);
    gpio->GPPUDCLK1 = (1 << 16) | (1 << 17);
    mmcWaitCycles(150);
    gpio->GPPUD = 0;
    gpio->GPPUDCLK1 = 0;

    // GPIO_DAT0, GPIO_DAT1, GPIO_DAT2, GPIO_DAT3
    tmp_val = gpio->GPFSEL5;
    tmp_val |= (7 << (0 * 3)) | (7 << (1 * 3)) | (7 << (2 * 3)) | (7 << (3 * 3));
    gpio->GPFSEL5 = tmp_val;
    gpio->GPPUD = 2;
    mmcWaitCycles(150);
    gpio->GPPUDCLK1 = (1 << 18) | (1 << 19) | (1 << 20) | (1 << 21);
    mmcWaitCycles(150);
    gpio->GPPUD = 0;
    gpio->GPPUDCLK1 = 0;

    debug(MMC_DRIVER,"MMC: GPIO set up\n");

    long tmp_var, cnt, ccs = 0;

    sd_hv = (mmci->slotisr_ver & HOST_SPEC_NUM) >> HOST_SPEC_NUM_SHIFT;

    // Reset the card.
    mmci->control0 = 0;
    mmci->control1 |= C1_SRST_HC;

    cnt=10000;
    do
    {
        mmcWaitMicroSeconds(10);
    } while((mmci->control1 & C1_SRST_HC) && cnt--);

    assert(cnt > 0 && "MMC ERROR: failed to reset");

    debug(MMC_DRIVER,"MMC: reset ok\n");

    mmci->control1 |= C1_CLK_INTLEN | C1_TOUNIT_MAX;
    mmcWaitMicroSeconds(10);

    // Set clock to setup frequency.
    assert(mmcSetClock(400000) == 0 && "MMC ERROR: while setting clock");

    mmci->irpt_en = 0xffffffff;
    mmci->irpt_mask  = 0xffffffff;

    sd_scr[0] = 0;
    sd_scr[1] = 0;
    sd_rca = 0;
    mmc_error = 0;

    mmcSendCommand(CMD_GO_IDLE, 0);
    assert(mmc_error == 0);

    mmcSendCommand(CMD_SEND_IF_COND, 0x000001AA);
    assert(mmc_error == 0);

    cnt=6;
    tmp_var=0;
    while(!(tmp_var & ACMD41_CMD_COMPLETE) && cnt--)
    {
        mmcWaitCycles(400);

        tmp_var = mmcSendCommand(CMD_SEND_OP_COND,ACMD41_ARG_HC);

        debug(MMC_DRIVER,"EMMC: CMD_SEND_OP_COND returned \n");

        if(tmp_var & ACMD41_CMD_COMPLETE)
            debug(MMC_DRIVER,"COMPLETE \n");

        if(tmp_var & ACMD41_VOLTAGE)
            debug(MMC_DRIVER,"VOLTAGE \n");

        if(tmp_var & ACMD41_CMD_CCS)
            debug(MMC_DRIVER,"CCS \n");

        debug(MMC_DRIVER,"%zd \n",tmp_var >> 32);
        debug(MMC_DRIVER,"%zd\n",tmp_var);

        if((int)mmc_error != (int)SD_TIMEOUT && mmc_error != SD_OK )
            assert(false && "MMC ERROR: EMMC ACMD41 returned error");
    }

    assert((tmp_var & ACMD41_CMD_COMPLETE) && cnt);
    assert(tmp_var & ACMD41_VOLTAGE);

    if(tmp_var & ACMD41_CMD_CCS)
        ccs = SCR_SUPP_CCS;

    mmcSendCommand(CMD_ALL_SEND_CID, 0);

    sd_rca = mmcSendCommand(CMD_SEND_REL_ADDR, 0);

    debug(MMC_DRIVER, "EMMC: CMD_SEND_REL_ADDR returned %zx \n", sd_rca);
    assert(mmc_error == 0);
    assert(mmcSetClock(25000000) == 0 && "MMC ERROR: while setting clock");

    mmcSendCommand(CMD_CARD_SELECT, sd_rca);
    assert(mmc_error == 0);

    assert(mmcGetStatus(SR_DAT_INHIBIT) == 0);

    mmci->blksizecnt = (1<<16) | 8;
    mmcSendCommand(CMD_SEND_SCR, 0);
    assert(mmc_error == 0);
    assert(mmcWaifForInterrupt(INT_READ_RDY) == 0);

    tmp_var = 0;
    cnt = 100000;
    while(tmp_var < 2 && cnt)
    {
        if( mmci->status & SR_READ_AVAILABLE )
            sd_scr[tmp_var++] = mmci->data;
        else
            mmcWaitMicroSeconds(1);
    }

    assert(tmp_var == 2);

    if(sd_scr[0] & SCR_SD_BUS_WIDTH_4)
    {
        mmcSendCommand(CMD_SET_BUS_WIDTH, sd_rca|2);
        assert(mmc_error == 0);
        mmci->control0 |= C0_HCTL_DWITDH;
    }

    sd_scr[0] &= ~SCR_SUPP_CCS;
    sd_scr[0] |= ccs;

    return SD_OK;
}

MMCDriver::MMCDriver() : SPT(63), lock_("MMCDriver::lock_"), rca_(0), sector_size_(512), num_sectors_(210672)
{
    mmcInit();
}

MMCDriver::~MMCDriver()
{

}

uint32 MMCDriver::addRequest( BDRequest * br)
{
  ScopeLock lock(lock_);
  debug(MMC_DRIVER, "addRequest %d!\n", br->getCmd() );

  int32 res = -1;

  switch( br->getCmd() )
  {
    case BDRequest::BD_READ:
      res = readSector( br->getStartBlock(), br->getNumBlocks(), br->getBuffer() );
      break;
    case BDRequest::BD_WRITE:
      res = writeSector( br->getStartBlock(), br->getNumBlocks(), br->getBuffer() );
      break;
    default:
      res = -1;
      break;
  }

  debug(MMC_DRIVER, "addRequest:No IRQ operation !!\n");
  br->setStatus( BDRequest::BD_DONE );
  return res;
}
int sd_readblock(unsigned int block_address, unsigned char *buffer, unsigned int num);

int32 MMCDriver::readBlock ( uint32 address, void *buffer )
{
  debug(MMC_DRIVER,"readBlock: address: %x, buffer: %p\n",address, buffer);

  mmcReadBlock(address / sector_size_, (unsigned char*)buffer);

  return 0;
}

int32 MMCDriver::readSector ( uint32 start_sector, uint32 num_sectors, void *buffer )
{
  debug(MMC_DRIVER,"readSector: start: %x, num: %x, buffer: %p\n",start_sector, num_sectors, buffer);
  for (uint32 i = 0; i < num_sectors; ++i)
  {
    readBlock((start_sector + i) * sector_size_, (char*)buffer + i * sector_size_);
  }
  return 0;
}

int32 MMCDriver::writeBlock ( uint32 address, void *buffer)
{
    debug(MMC_DRIVER, "writeBlock: address: %x, buffer: %p\n", address, buffer);

    mmcWriteBlock(address / sector_size_, (uint8*) buffer);

    return 0;
}

int32 MMCDriver::writeSector ( uint32 start_sector, uint32 num_sectors, void * buffer)
{
  debug(MMC_DRIVER,"writeSector: start: %x, num: %x, buffer: %p\n",start_sector, num_sectors, buffer);
  for (uint32 i = 0; i < num_sectors; ++i)
  {
    writeBlock((start_sector + i) * sector_size_, (char*)buffer + i * sector_size_);
  }
  return 0;
}

uint32 MMCDriver::getNumSectors()
{
  return num_sectors_;
}

uint32 MMCDriver::getSectorSize()
{
  return sector_size_;
}

void MMCDriver::serviceIRQ()
{
}

