#include "debug_bochs.h"
#include "offsets.h"
#include "KeyboardManager.h"
#include "board_constants.h"


void writeChar2Bochs( char char2Write )
{
  /* Wait until the serial buffer is empty */
  while (*UART0_FR & SERIAL_BUFFER_FULL)
      asm volatile("nop");
  /* Put our character, c, into the serial buffer */
  *UART0_DR = char2Write;
}

void writeLine2Bochs( const char * line2Write )
{
  uint8 counter = 0;

  while(*line2Write && (counter++ < 250))
      writeChar2Bochs( *line2Write++ );
}
