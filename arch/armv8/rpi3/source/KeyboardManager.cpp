#include "KeyboardManager.h"
#include "kprintf.h"
#include "Console.h"
#include "offsets.h"
#include "Scheduler.h"
#include "PageManager.h"

  uint32 const KeyboardManager::STANDARD_KEYMAP[KEY_MAPPING_SIZE] =
  {
    0, 0, 0, 0, // 4
    'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', // 17
    'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', // 30
    '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '\n', 0, '\b', // 43
    '\t', ' ', '-', '=', '[', ']', '\\', '#', ';', '\'', '`', // 54
    ',', '.', '/', KB<PERSON>_META_CAPS, KEY_F1, KEY_F2, KEY_F3, KEY_F4, // 62
    KEY_F5, KEY_F6, KEY_F7, KEY_F8, KEY_F9, KEY_F10, KEY_F11, KEY_F12, // 70
    KEY_PRNT, KBD_META_SCRL, KEY_PAUSE, KEY_INS, KEY_HOME, KEY_PGUP, // 76
    KEY_DEL, KEY_PGDN, KEY_RT, KEY_LFT, KEY_DN, KEY_UP, KBD_META_NUM, // 83
    0, '*', '-', '+', '\n', '1', '2', '3', '4', '5', '6', '7', '8', // 96
    '9', '0', '.', '\\', 0, 0, '=', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // 114
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 // 128
  };

  uint32 const KeyboardManager::E0_KEYS[KEY_MAPPING_SIZE] =
  {
    0, 0, 0, 0, // 4
    'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', // 17
    'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', // 30
    '!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '\n', 0, '\b', // 43
    '\t', ' ', '_', '+', '{', '}', '|', '~', ':', '"', '~', // 54
    '<', '>', '?', KBD_META_CAPS, KEY_F1, KEY_F2, KEY_F3, KEY_F4, // 62
    KEY_F5, KEY_F6, KEY_F7, KEY_F8, KEY_F9, KEY_F10, KEY_F11, KEY_F12, // 70
    KEY_PRNT, KBD_META_SCRL, KEY_PAUSE, KEY_INS, KEY_HOME, KEY_PGUP, // 76
    KEY_DEL, KEY_PGDN, KEY_RT, KEY_LFT, KEY_DN, KEY_UP, KBD_META_NUM, // 83
    0, '*', '-', '+', '\n', KEY_END, KEY_DN, KEY_PGDN, KEY_LFT, '5', KEY_RT, KEY_HOME, KEY_UP, // 96
    KEY_PGUP, KEY_INS, KEY_DEL, '|', 0, 0, '=', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // 114
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 // 128
  };

KeyboardManager *KeyboardManager::instance_ = 0;

extern struct UsbDevice *Devices[];

KeyboardManager::KeyboardManager() :
    keyboard_buffer_(256), extended_scancode(0), keyboard_status_(0), usb_kbd_addr_(0), current_key_(0), next_is_up_(0)
{


}

KeyboardManager::~KeyboardManager()
{
}

void KeyboardManager::kb_wait()
{
}

void KeyboardManager::send_cmd(uint8 cmd __attribute__((unused)), uint8 port __attribute__((unused)))
{
}

void KeyboardManager::serviceIRQ( void )
{
    char data = 0;

    if (!(*UART0_FR & 0x10))
    {
        data = (char) (*UART0_DR);

        if (data != 0)
        {
            if (data == '\r')
                data = '\n';

            if (data == 127)
                data = '\b';

            keyboard_buffer_.put(data);
        }
    }
}

void KeyboardManager::modifyKeyboardStatus(uint8 sc __attribute__((unused)))
{
  return;
}

void KeyboardManager::emptyKbdBuffer()
{
}


void KeyboardManager::setLEDs( void )
{
}

uint32 KeyboardManager::convertScancode( uint8 scancode )
{
  return scancode;
}
