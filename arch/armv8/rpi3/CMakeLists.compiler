#toolchains
INCLUDE(CMakeForceCompiler)

# this one is important
SET(CMAKE_SYSTEM_NAME Generic)
SET(CMAKE_TRY_COMPILE_TARGET_TYPE STATIC_LIBRARY)

# which compilers to use for C and C++

find_program(CMAKE_ASM_COMPILER aarch64-linux-gnu-gcc)
find_program(CMAKE_C_COMPILER aarch64-linux-gnu-gcc)
find_program(CMAKE_CXX_COMPILER aarch64-linux-gnu-g++)

find_program(LD_EXECUTABLE aarch64-linux-gnu-gcc)
find_program(LD_EXECUTABLE_PURE aarch64-linux-gnu-ld)
find_program(OBJCOPY_EXECUTABLE aarch64-linux-gnu-objcopy)

if(${CMAKE_VERSION} VERSION_LESS "3.6.0") 
    CMAKE_FORCE_CXX_COMPILER(${CMAKE_CXX_COMPILER} STATIC_LIBRARY)	
    CMAKE_FORCE_C_COMPILER(${CMAKE_C_COMPILER} STATIC_LIBRARY)
endif()
