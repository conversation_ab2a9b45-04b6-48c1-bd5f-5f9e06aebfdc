INCLUDE(CMakeForceCompiler)
SET(CMAKE_SYSTEM_NAME Generic)
SET(CMAKE_TRY_COMPILE_TARGET_TYPE STATIC_LIBRARY)
find_program(CMAKE_C_COMPILER aarch64-linux-gnu-gcc)

set(ARCH_APPEND_LD_ARGUMENTS -Wl,-no-whole-archive -Wl,-lgcc)

set(ARCH_USERSPACE_COMPILE_OPTIONS -Wall -Werror  -std=gnu11 -g -O0 -static -nostdinc -fno-builtin -nostdlib -fno-stack-protector -fno-common -Werror=implicit-function-declaration -Wno-error=unused-variable -fno-stack-clash-protection)
set(ARCH_USERSPACE_LINKER_OPTIONS -static)
