#pragma once

#include <types.h>

#define BOARD_LOAD_BASE 0
/**
 * at this address the mmios are mapped
 */
#define PYHSICAL_MMIO_OFFSET 0x3F000000

#define SERIAL_BASE             (PYHSICAL_MMIO_OFFSET | 0x201000)
#define BCM2837_PL011_REGS_BASE (PYHSICAL_MMIO_OFFSET | 0x201000)
#define AUX_REGS_BASE           (PYHSICAL_MMIO_OFFSET | 0x215000)
#define GPIO_REGS_BASE          (PYHSICAL_MMIO_OFFSET | 0x200000)

#define UART0_DR        ((volatile unsigned int*)(IDENT_MAPPING_START | SERIAL_BASE))
#define UART0_FR        ((volatile unsigned int*)(IDENT_MAPPING_START | SERIAL_BASE | SERIAL_FLAG_REGISTER))

#define MAILBOX_READ   (IDENT_MAPPING_START | PYHSICAL_MMIO_OFFSET | 0xb880)
#define MAILBOX_WRITE  (IDENT_MAPPING_START | PYHSICAL_MMIO_OFFSET | 0xb8A0)
#define MAILBOX_STATUS (IDENT_MAPPING_START | PYHSICAL_MMIO_OFFSET | 0xb898)

#define MAILBOX_ATTR_ALLOC_BUFFER             (0x40001)
#define MAILBOX_ATTR_SET_PHYSICAL_WIDTH_HIGHT (0x48003)
#define MAILBOX_ATTR_SET_VIRTUAL_WIDTH_HIGHT  (0x48004)
#define MAILBOX_ATTR_SET_DEPTH                (0x48005)

#define MAILBOX_CHANNEL_PROPERTY_TAGS         (0x08)

#define MAILBOX_FULL                          (1 << 31)
#define MAILBOX_EMPTY                         (1 << 30)
#define MAILBOX_RESPONSE                      (1 << 31)

#define ARM_CORE0_TIM_IRQCNTL   0x40000040
#define ARM_CORE0_IRQ_SOURCE    0x40000060

#define SERIAL_FLAG_REGISTER 0x18
#define SERIAL_BUFFER_FULL (1 << 5)

#define HCD_DESIGNWARE_BASE (void*)(IDENT_MAPPING_START | PYHSICAL_MMIO_OFFSET | 0x980000)

typedef struct
{
    uint32 GPFSEL0;
    uint32 GPFSEL1;
    uint32 GPFSEL2;
    uint32 GPFSEL3;
    uint32 GPFSEL4;
    uint32 GPFSEL5;
    uint32 Reserved;
    uint32 GPSET0;
    uint32 GPSET1;
    uint32 Reserved1;
    uint32 GPCLR0;
    uint32 GPCLR1;
    uint32 Reserved3;
    uint32 GPLEV0;
    uint32 GPLEV1;
    uint32 Reserved4;
    uint32 GPEDS0;
    uint32 GPEDS1;
    uint32 Reserved5;
    uint32 GPREN0;
    uint32 GPREN1;
    uint32 Reserved6;
    uint32 GPFEN0;
    uint32 GPFEN1;
    uint32 Reserved7;
    uint32 GPHEN0;
    uint32 GPHEN1;
    uint32 Reserved8;
    uint32 GPLEN0;
    uint32 GPLEN1;
    uint32 Reserved9;
    uint32 GPAREN0;
    uint32 GPAREN1;
    uint32 Reserved10;
    uint32 GPAFEN0;
    uint32 GPAFEN1;
    uint32 Reserved11;
    uint32 GPPUD;
    uint32 GPPUDCLK0;
    uint32 GPPUDCLK1;
    uint32 Reserved12;
    uint32 Test;

}__attribute__((packed)) GpioRegisters;

typedef struct
{
    uint32 IRQ;         //0x00
    uint32 ENABLES;     //0x04
    char unused1 [0x38];  //0x40
    uint32 MU_IER;      //0x44
    uint32 MU_IIR;      //0x48
    uint32 MU_LCR;      //0x4c
    uint32 MU_MCR;      //0x50
    uint32 MU_LSR;      //0x54
    uint32 MU_MSR;      //0x58
    uint32 MU_SCRATCH;  //0x5c
    uint32 MU_CNTL;     //0x60
    uint32 MU_STAT;     //0x64
    uint32 MU_BAUD;     //0x68
    char unused2 [0x14];  //0x6c
    uint32 SPI0_CNTL0;  //0x80
    uint32 SPI0_CNTL1;  //0x84
    uint32 SPI0_STAT;   //0x88
    char unused3 [0x04];  //0x8c
    uint32 SPI0_IO;     //0x90
    uint32 SPI0_PEEK;   //0x94
    char unused4 [28];    //0x98
    uint32 SPI1_CNTL0;  //0xC0
    uint32 SPI1_CNTL1;  //0xC4
    uint32 SPI1_STAT;   //0xC8
    char unused5 [4];     //0xCC
    uint32 SPI1_IO;     //0xD0
    uint32 SPI1_PEEK;   //0xD4
}__attribute__((packed)) AuxRegisters;


typedef struct
{
    uint32 DR;              //0x00
    uint32 RSRECR;          //0x04
    char unused[0x10];        //0x08
    uint32 FR;              //0x18
    char unused1[0x4];        //0x1C
    uint32 ILPR;            //0x20
    uint32 IBRD;            //0x24
    uint32 FBRD;            //0x28
    uint32 LCRH;            //0x2C
    uint32 CR;              //0x30
    uint32 IFLS;            //0x34
    uint32 IMSC;            //0x38
    uint32 RIS;             //0x3C
    uint32 MIS;             //0x40
    uint32 ICR;             //0x44
    uint32 DMACR;           //0x48
    char unused2[0x4 + 0x30]; //0x4C
    uint32 ITCR;            //0x80
    uint32 ITIP;            //0x84
    uint32 ITOP;            //0x88
    uint32 TDR;             //0x8C

}__attribute__((packed)) PL011UartRegisters;
