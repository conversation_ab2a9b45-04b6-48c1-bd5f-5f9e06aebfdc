include(CheckCCompilerFlag)

set(KERNEL_BINARY kernel.x)

set(KERNEL_CMAKE_CXX_FLAGS ${CMAKE_CXX_FLAGS} -std=gnu++17 -O2 -gdwarf-2 -Wall -Wextra -Werror -Wno-nonnull-compare -nostdinc -nostdlib -nostartfiles -nodefaultlibs -nostdinc++ -fno-builtin -fno-rtti -fno-exceptions -fno-stack-protector -ffreestanding -Wno-strict-aliasing -fshort-wchar ${NOPICFLAG})
set(KERNEL_CMAKE_C_FLAGS ${CMAKE_C_FLAGS} -std=gnu11 -O2 -gdwarf-2 -Wall -Wextra -Werror -nostdinc -nostartfiles -nodefaultlibs -nostdlib -fno-builtin -fno-exceptions -fno-stack-protector -ffreestanding -Wno-strict-aliasing -fshort-wchar ${NOPICFLAG})

check_c_compiler_flag(-mno-outline-atomics HAS_MNO_OUTLINE_ATOMICS)
if (HAS_MNO_OUTLINE_ATOMICS)
    set(KERNEL_CMAKE_C_FLAGS ${KERNEL_CMAKE_C_FLAGS} -mno-outline-atomics)
    set(KERNEL_CMAKE_CXX_FLAGS ${KERNEL_CMAKE_CXX_FLAGS} -mno-outline-atomics)
endif()


MACRO(ARCH2OBJ ARCHOBJ_LIBNAME LIBRARY_NAME)
ENDMACRO(ARCH2OBJ)

set(ARCH_LD_ARGUMENTS -Wl,--build-id=none -Wl,-z -Wl,max-page-size=0x1000 -nostdinc -nostdlib -nodefaultlibs)
set(KERNEL_LD_ARGUMENT ${ARCH_LD_ARGUMENTS} -Wl,-Map=../kernel.map ${NOPIEFLAG})
set(ARCH_APPEND_LD_ARGUMENTS -Wl,-lgcc)


set(ADD_LD_ARGUMENT -Wl,"${PROJECT_BINARY_DIR}/lib/debug_info.o")

set(KERNEL_IMAGE_OBJCOPY
COMMAND ${PROJECT_BINARY_DIR}/add-dbg ${PROJECT_BINARY_DIR}/kernel.x ${PROJECT_BINARY_DIR}/kernel.dbg
COMMAND touch ${PROJECT_BINARY_DIR}/debug_info.c
COMMAND ${CMAKE_C_COMPILER} -c ${PROJECT_BINARY_DIR}/debug_info.c -o ${PROJECT_BINARY_DIR}/lib/debug_info.o
COMMAND "${OBJCOPY_EXECUTABLE}" --add-section .swebdbg="${PROJECT_BINARY_DIR}/kernel.dbg" --set-section-flags .swebdbg=load,data ${PROJECT_BINARY_DIR}/lib/debug_info.o
COMMAND rm ${PROJECT_BINARY_DIR}/debug_info.c
)


if ("${VIRTUALIZED_QEMU}" STREQUAL "1")
     set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -DVIRTUALIZED_QEMU")
endif ("${VIRTUALIZED_QEMU}" STREQUAL "1")


# sdcard: Create an sdcard for the raspberry pi
add_custom_target(sdcard
  COMMAND ${PROJECT_SOURCE_DIR}/arch/arm/rpi/utils/makesdcard.sh "${PROJECT_SOURCE_DIR}/arch/arm/rpi/utils/boot/"
  COMMENT "Creating SD card"
  WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
  )

# sdcardq: Create an sdcard for the raspberry pi (quiet mode)
add_custom_target(sdcardq
  COMMAND ${PROJECT_SOURCE_DIR}/arch/arm/rpi/utils/makesdcard.sh "${PROJECT_SOURCE_DIR}/arch/arm/rpi/utils/boot/" y /dev/mmcblk0
  COMMENT "Creating SD card in quiet mode"
  WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
  )

add_custom_target(qemu
  COMMAND qemu-system-aarch64 -M raspi3 -cpu cortex-a53 -m 1024 -drive file=${HDD_IMAGE},if=sd -no-reboot -kernel kernel.x -serial stdio -d guest_errors,unimp
  COMMENT "Executing `qemu-system-aarch64 -M raspi3 -cpu cortex-a53 -m 1024 -no-reboot -kernel kernel.x -serial stdio -d guest_errors,unimp`"
  WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
  COMMAND reset -I
  )

# qemugdb: Run qemu in non debugging mode
add_custom_target(qemugdb
  COMMAND qemu-system-aarch64 -M raspi3 -cpu cortex-a53 -m 1024 -drive file=${HDD_IMAGE},if=sd -no-reboot -kernel kernel.x -serial stdio -d guest_errors,unimp -s -S
  COMMENT "Executing `qemu-system-aarch64 -M raspi3 -cpu cortex-a53 -m 1024 -no-reboot -kernel kernel.x -serial stdio -d guest_errors,unimp -s -S`"
  WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
  COMMAND reset -I
  )

add_custom_target(lst
  COMMAND rm -rf kernel.asm
  COMMAND aarch64-linux-gnu-objdump -d kernel.x > kernel.lst
  COMMAND reset -I
  )
  
  add_custom_target(image
  COMMAND rm -rf kernel8.img
  COMMAND aarch64-linux-gnu-objcopy -O binary kernel.x kernel8.img
  COMMAND reset -I
  )

# build_qemu_rpi: Build qemu with raspberry pi support (includes source download)
#add_custom_target(build_qemu_rpi
#  COMMAND ${PROJECT_SOURCE_DIR}/arch/arm/rpi/utils/build_qemu_rpi.sh
#  WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
#  COMMAND reset -I
#  )

