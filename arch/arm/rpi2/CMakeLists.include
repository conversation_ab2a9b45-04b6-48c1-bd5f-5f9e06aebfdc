set(KERNEL_BINARY kernel.x)

set(ARCH_RPI_KERNEL_CFLAGS -O0 -gstabs2 -Wall -Wextra -Werror -Wno-error=format -nostdinc -nostdlib -nostartfiles -nodefaultlibs -fno-builtin -fno-exceptions -fno-stack-protector -ffreestanding -mapcs -marm -Wno-strict-aliasing -march=armv6 -fshort-wchar ${NOPICFLAG})

set(KERNEL_CMAKE_CXX_FLAGS ${CMAKE_CXX_FLAGS} -std=gnu++17 -Wno-nonnull-compare -nostdinc++ -fno-rtti ${ARCH_RPI_KERNEL_CFLAGS})
set(KERNEL_CMAKE_C_FLAGS   ${CMAKE_C_FLAGS}   -std=gnu11 ${ARCH_RPI_KERNEL_CFLAGS})


set(ARCH_LD_ARGUMENTS -Wl,--build-id=none -Wl,-z,max-page-size=0x1000 -nostdinc -nostdlib -nodefaultlibs)
set(KERNEL_LD_ARGUMENT ${ARCH_LD_ARGUMENTS} ${NOPIEFLAG})
set(ARCH_APPEND_LD_ARGUMENTS -Wl,-lgcc)


MACRO(ARCH2OBJ ARCHOBJ_LIBNAME LIBRARY_NAME)
ENDMACRO(ARCH2OBJ)

set(KERNEL_IMAGE_OBJCOPY )


# sdcard: Create an sdcard for the raspberry pi
add_custom_target(sdcard
  COMMAND ${PROJECT_SOURCE_DIR}/arch/arm/rpi/utils/makesdcard.sh "${PROJECT_SOURCE_DIR}/arch/arm/rpi/utils/boot/"
  COMMENT "Creating SD card"
  WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
  )

# sdcardq: Create an sdcard for the raspberry pi (quiet mode)
add_custom_target(sdcardq
  COMMAND ${PROJECT_SOURCE_DIR}/arch/arm/rpi/utils/makesdcard.sh "${PROJECT_SOURCE_DIR}/arch/arm/rpi/utils/boot/" y /dev/mmcblk0
  COMMENT "Creating SD card in quiet mode"
  WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
  )

# qemu: Run qemu in non debugging mode
add_custom_target(qemu
    COMMAND qemu-system-arm -kernel kernel.x -cpu arm1176 -m 1024 -M raspi2 -no-reboot -drive if=sd,file=${HDD_IMAGE} -serial stdio -d guest_errors,unimp
    COMMENT "Executing `qemu-system-arm -kernel kernel.x -cpu arm1176 -m 1024 -M raspi2 -no-reboot -drive if=sd,file=${HDD_IMAGE} -serial stdio -d guest_errors,unimp`"
    WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
    COMMAND reset -I
    )

# qemugdb: Run qemu in non debugging mode
add_custom_target(qemugdb
    COMMAND qemu-system-arm -kernel kernel.x -cpu arm1176 -m 512 -M raspi2 -no-reboot -drive if=sd,file=${HDD_IMAGE} -serial stdio -d guest_errors,unimp -s -S
    COMMENT "Executing `qemu-system-arm -kernel kernel.x -cpu arm1176 -m 512 -M raspi2 -no-reboot -drive if=sd,file=${HDD_IMAGE} -serial stdio -d guest_errors,unimp -s -S`"
    WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
    COMMAND reset -I
    )

