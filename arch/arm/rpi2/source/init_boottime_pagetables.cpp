#include "types.h"
#include "paging-definitions.h"
#include "offsets.h"
#include "init_boottime_pagetables.h"

extern "C" void initialiseBootTimePaging()
{
  PageDirEntry *pde_start = (PageDirEntry*)(((char*)kernel_page_directory) - PHYSICAL_TO_VIRTUAL_OFFSET);
  PageTableEntry *pte_start = (PageTableEntry*) VIRTUAL_TO_PHYSICAL_BOOT((pointer )kernel_page_tables);

  uint32 i;
  // clear the page dir
  for (i = 0; i < 4096; ++i)
    *((uint32*)pde_start + i) = 0;
  // 1 : 1 mapping of the first 8 mbs
  for (i = 0; i < 8; ++i)
    mapBootTimePage(pde_start, i, i);
  // map 16 mb PTs for kernel
  for (i = 0; i < 16; ++i)
  {
    pde_start[2048 + i].pt.size = 1;
    pde_start[2048 + i].pt.offset = i % 4;
    pde_start[2048 + i].pt.pt_ppn = ((pointer) &pte_start[PAGE_TABLE_ENTRIES * i]) / PAGE_SIZE;
  }
  // clear the page tables
  for (i = 0; i < 16 * PAGE_TABLE_ENTRIES; ++i)
    *((uint32*)pte_start + i) = 0;
  // map kernel into PTs
  size_t kernel_last_page = (size_t)VIRTUAL_TO_PHYSICAL_BOOT((pointer)&kernel_end_address) / PAGE_SIZE;
  extern size_t ro_data_end_address;
  size_t last_ro_data_page = (size_t)VIRTUAL_TO_PHYSICAL_BOOT((pointer)&ro_data_end_address) / PAGE_SIZE;

  for (i = 0; i < last_ro_data_page; ++i)
  {
    pte_start[i].size = 2;
    pte_start[i].permissions = 1;
    pte_start[i].page_ppn = i;
  }
  for (; i < kernel_last_page; ++i)
  {
    pte_start[i].size = 2;
    pte_start[i].permissions = 1;
    pte_start[i].page_ppn = i;
  }
  // 3gb 1:1 mapping
  for (i = 0; i < 1024; ++i)
    mapBootTimePage(pde_start, 3072 + i, i);
  // map devices from 0x81000000 upwards

  unsigned int mmio_base = 0x3f0;

  mapBootTimePage(pde_start,0x860,mmio_base + 2);  // pl011
  mapBootTimePage(pde_start,0x8C0,mmio_base + 3);  // emmc
  mapBootTimePage(pde_start,0x900,mmio_base);  // most devices (ic, timer, gpu, ...)

  mapBootTimePage(pde_start,0x909,mmio_base + 9);  // map for csud

  for (i = 0; i < 8; ++i)
    mapBootTimePage(pde_start,0xb00 + i,0x5c0 +i);  // framebuffer TODO: this might be not necessary
  // still plenty of room for more memory mapped devices
}

extern "C" void removeBootTimeIdentMapping()
{
  // we will not remove anything because we need the first 8 mb 1:1 mapped
}
