#!/bin/bash
yn="$2"
if [ "$yn" != "y" ]; then
  echo "You have to partition an sd card as follows:"
  echo "First partition: the boot partition. FAT32, at least 16 MiB, less than 64 MiB."
  echo "Second partition: the user progs partition. MinixFS, at least 16 MiB, less than 64 MiB."
  echo "Third partition: the rest."
  while [[ "$yn" != "y" && "$yn" != "n" ]]; do
    echo "Do you have a properly partitioned sd card? [y/n]"
    read yn
  done
  if [ "$yn" = "n" ]; then
    echo "Then create one first..."
    return 1
  fi
fi
dev="$3"
while [[ "$dev" = "" || ! -e "$dev" ]]; do
  echo "Which device to mount? (the sd card, i.e. /dev/mmcblk0)"
  read dev
	if [[ "$dev" = "" || ! -e "$dev" ]]; then
		dev="/dev/mmcblk0"
	fi
done
echo "Mounting ${dev}p1 using gvfs-mount ..."
mountpoint=`gvfs-mount -d ${dev}p1`
mountpoint=`echo $mountpoint | cut -d " " -f4-`
echo "$mountpoint"
arm-linux-gnueabi-objcopy kernel.x -O binary kernel.img
cp kernel.img $mountpoint
cp $1/* $mountpoint
gvfs-mount -u $mountpoint
echo "Mounting ${dev}p2 using gvfs-mount ..."
mountpoint=`gvfs-mount -d ${dev}p2`
mountpoint=`echo $mountpoint | cut -d " " -f4-`
echo "$mountpoint"
needsudo=0
cp userspace/*.sweb $mountpoint
cp userspace/data/* $mountpoint
if [[ $needsudo -eq 1 || $? -ne 0 ]]; then
  echo "Partition can only be accesed with sudo..."
  sudo cp userspace/*.sweb $mountpoint
fi
gvfs-mount -u $mountpoint

