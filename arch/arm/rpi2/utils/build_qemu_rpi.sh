#!/bin/bash
echo "Where shall the qemu-rpi source directory be?"
read qemu_dir
echo $qemu_dir
echo "Cloning https://github.com/Torlus/qemu.git to $qemu_dir"
git clone https://github.com/Torlus/qemu.git $qemu_dir
cd $qemu_dir
git checkout rpi
git pull
echo "Configuring qemu-system-arm-rpi"
./configure --enable-curses --enable-sdl --target-list=arm-softmmu --disable-vnc --disable-xen --disable-docs --enable-debug --enable-debug-info --enable-linux-aio
echo "Build with how many cores?"
read cores
echo "Building qemu-system-arm-rpi ..."
make -i -j $cores
if [ -e arm-softmmu/qemu-system-arm ]; then
  echo "Build was SUCCESSFUL!"
else
  echo "Build FAILED!"
fi
echo "Copying file qemu-system-arm to /usr/local/bin/qemu-system-arm-rpi"
cp -vf arm-softmmu/qemu-system-arm /usr/local/bin/qemu-system-arm-rpi
if [ $? -ne 0 ]; then
  echo "COMMAND FAILED: Copying file qemu-system-arm to /usr/local/bin/qemu-system-arm-rpi"
  echo "We will retry with sudo and apply chmod 755 to it..."
  sudo cp arm-softmmu/qemu-system-arm /usr/local/bin/qemu-system-arm-rpi
  sudo chmod 755 /usr/local/bin/qemu-system-arm-rpi
fi
echo "/usr/local/bin/qemu-system-arm-rpi SUCCESSFULLY INSTALLED!"
