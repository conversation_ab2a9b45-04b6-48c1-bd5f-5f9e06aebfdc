/******************************************************************************
* device/hub.h
*  by <PERSON>
*
* A light weight implementation of the USB protocol stack fit for a simple
* driver.
*
* device/hub.h contains definitions relating to the USB hub device.
******************************************************************************/

#pragma once

#ifdef __cplusplus
extern "C"
{
#endif

#include <usbd/descriptors.h>
#include <usbd/device.h>
#include <usb_types.h>

/**
  \brief The hub descriptor information.

  The hub descriptor structure defined in the USB2.0 manual section 
  *********.
*/
struct HubDescriptor {
  u8 DescriptorLength; // +0x0
  enum DescriptorType DescriptorType : 8; // +0x1
  u8 PortCount; // +0x2
  struct {
    enum HubPortControl {
      Global = 0,
      Individual = 1,     
    } PowerSwitchingMode : 2; // @0
    bool Compound : 1; // @2
    enum HubPortControl OverCurrentProtection : 2; // @3
    unsigned ThinkTime : 2; // in +1*8FS units @5
    bool Indicators : 1; // @7
    unsigned _reserved8_15 : 8; // @8
  } __attribute__ ((__packed__)) Attributes; // +0x3
  u8 PowerGoodDelay; // +0x5
  u8 MaximumHubPower; // +0x6
  u8 Data[]; // +0x7 the data consists of n bytes describing port detatchability, followed by n bytes for compatiblity. n = roundup(ports/8).
} __attribute__ ((__packed__));

/**
  \brief Encapsulates the current status of a hub.

  The hub status structure defined in ********* of the USB2.0 
  standard.
*/
struct HubStatus {
  bool LocalPower : 1; // @0
  bool OverCurrent : 1; // @1
  unsigned _reserved2_15 : 14; // @2
} __attribute__ ((__packed__));

/**
  \brief Encapsulates the change in current status of a hub.

  The hub status change structure defined in ********* of the USB2.0 
  standard.
*/
struct HubStatusChange {
  bool LocalPowerChanged : 1; // @0
  bool OverCurrentChanged : 1; // @1
  unsigned _reserved2_15 : 14; // @2
} __attribute__ ((__packed__));

/**
  \brief Encapsulates the full status of a hub.

  The hub status structure defined in ********* of the USB2.0 standard.
*/
struct HubFullStatus {
  struct HubStatus Status;
  struct HubStatusChange Change;
} __attribute__ ((__packed__));
/**
  \brief Encapsulates the current status of a hub port.

  The hub port status structure defined in 11.24.2.7.1 of the USB2.0 
  standard.
*/
struct HubPortStatus {
  bool Connected : 1; // @0
  bool Enabled : 1; // @1
  bool Suspended : 1; // @2
  bool OverCurrent : 1; // @3
  bool Reset : 1; // @4
  unsigned _reserved5_7 : 3; // @5
  bool Power : 1; // @8
  bool LowSpeedAttatched : 1; // @9
  bool HighSpeedAttatched : 1; // @10
  bool TestMode : 1; // @11
  bool IndicatorControl : 1; // @12
  unsigned _reserved13_15 : 3; // @13
} __attribute__ ((__packed__));

/**
  \brief Encapsulates the change in current status of a hub port.

  The hub port status change structure defined in 11.24.2.7.2 of the USB2.0 
  standard.
*/
struct HubPortStatusChange {
  bool ConnectedChanged : 1; // @0
  bool EnabledChanged : 1; // @1
  bool SuspendedChanged : 1; // @2
  bool OverCurrentChanged : 1; // @3
  bool ResetChanged : 1; // @4
  unsigned _reserved5_15 : 11; // @5
} __attribute__ ((__packed__));

/**
  \brief Encapsulates the full status of a hub port.

  The hub port status structure defined in 11.24.2.7 of the USB2.0 standard.
*/
struct HubPortFullStatus {
  struct HubPortStatus Status;
  struct HubPortStatusChange Change;
} __attribute__ ((__packed__));

/**
  \brief A feature of a hub port.

  The feautres of a hub port that can be altered.
*/
enum HubPortFeature {
  FeatureConnection = 0,
  FeatureEnable = 1,
  FeatureSuspend = 2,
  FeatureOverCurrent = 3,
  FeatureReset = 4,
  FeaturePower = 8,
  FeatureLowSpeed = 9,
  FeatureHighSpeed = 10,
  FeatureConnectionChange = 16,
  FeatureEnableChange = 17,
  FeatureSuspendChange = 18,
  FeatureOverCurrentChange = 19,
  FeatureResetChange = 20,
};

/** The DeviceDriver field in UsbDriverDataHeader for hubs. */
#define DeviceDriverHub 0x48554230

/** 
  \brief Hub specific data.

  The contents of the driver data field for hubs. 
*/
struct HubDevice {  
  struct UsbDriverDataHeader Header;
  struct HubFullStatus Status;
  struct HubDescriptor *Descriptor;
  u32 MaxChildren;
  struct HubPortFullStatus PortStatus[MaxChildrenPerDevice];
  struct UsbDevice *Children[MaxChildrenPerDevice];
};

/**
  \brief A feature of a hub.

  The feautres of a hub that can be altered.
*/
enum HubFeature {
  FeatureHubPower = 0,
  FeatureHubOverCurrent = 1,
};


/**
  \brief Performs all necesary hub related initialisation.

  Loads a hub device and enumerates its children.
*/
Result HubAttach(struct UsbDevice *device, u32 interfaceNumber);

/**
  \brief Resets a port on a hub.

  Resets a port on a hub. No validation.
*/
Result HubPortReset(struct UsbDevice *device, u8 port);

/**
  \brief Checks the connection status of a port.

  Checks for a change in the connection status of a port. If it has changed
  performs necessary actions such as enumerating a new device or deallocating
  an old one.
*/
Result HubCheckConnection(struct UsbDevice *device, u8 port);

/**
  \brief Checks all hubs for new devices.

  Recursively checks the hub tree for new devices being attached, and for old
  devices being removed.
*/
void HubRecursiveCheck(struct UsbDevice *device);

#ifdef __cplusplus
}
#endif

