/******************************************************************************
* device/hid/report.h
*  by <PERSON>
*
* A light weight implementation of the USB protocol stack fit for a simple
* driver.
*
* device/hid/report.h contains definitions relating to human interface
* device reports, used to communicate the functionality of any given hid 
* device.
******************************************************************************/

#pragma once

#include <usb_types.h>

#ifdef __cplusplus
extern "C"
{
#endif

/**
  \brief An item in an hid report descriptor.

  The hid report item sturcture defined in the USB HID 1.11 manual in
  *******. There are two such types defined here, long and short. Since long
  is based upon short. So we can resuse the structure to this effect.
*/
struct HidReportItem {
  unsigned Size : 2; // Value is 1 << (size - 1) @0 
  enum HidReportTag {
    TagMainInput = 0x20,
    TagMainOutput = 0x24,
    TagMainFeature = 0x2c,
    TagMainCollection = 0x28,
    TagMainEndCollection = 0x30,
    TagGlobalUsagePage = 0x1,
    TagGlobalLogicalMinimum = 0x5,
    TagGlobalLogicalMaximum = 0x9,
    TagGlobalPhysicalMinimum = 0xd,
    TagGlobalPhysicalMaximum = 0x11,
    TagGlobalUnitExponent = 0x15,
    TagGlobalUnit = 0x19,
    TagGlobalReportSize = 0x1d,
    TagGlobalReportId = 0x21,
    TagGlobalReportCount = 0x25,
    TagGlobalPush = 0x29,
    TagGlobalPop = 0x2d,
    TagLocalUsage = 0x2,
    TagLocalUsageMinimum = 0x6,
    TagLocalUsageMaximum = 0xa,
    TagLocalDesignatorIndex = 0xe,
    TagLocalDesignatorMinimum = 0x12,
    TagLocalDesignatorMaximum = 0x16,
    TagLocalStringIndex = 0x1e,
    TagLocalStringMinimum = 0x22,
    TagLocalStringMaximum = 0x26,
    TagLocalDelimiter = 0x2a,
    TagLong = 0x3f,
  } Tag : 6; // @2
} __attribute__ ((__packed__));

/**
  \brief A main input, output or feature in an hid report descriptor.

  The hid main input, output or feature item sturcture defined in the USB HID
  1.11 manual in *******. 
*/
struct HidMainItem {
  bool Constant : 1; // Data=0,Constant=1 @0
  bool Variable : 1; // Array=0,Variable=1 @1 
  bool Relative : 1; // Absolute=0,Relative=1 @2
  bool Wrap : 1; // NoWrap=0,Wrap=1 @3
  bool NonLinear : 1; // Linear=0,NonLinear=1 @4
  bool NoPreferred : 1; // PreferredState=0,NoPreferred=1 @5
  bool Null : 1; // NoNull=0,NullState=1 @6
  bool Volatile : 1; // NonVolatile=0,Volatile=1 Inputs cannot be volatile @7
  bool BufferedBytes : 1; // BitField=0,BufferedBytes=1 @8
  unsigned _reserved9_31 : 23; // @9
} __attribute__ ((__packed__));

/**
  \brief A main collection index in an hid report descriptor.

  The hid main collection index used in the collection item defined in the 
  USB HID 1.11 manual in *******. 
*/
enum HidMainCollection {
  Physical = 0,
  Application = 1,
  Logical = 2,
  Report = 3,
  NamedArray = 4,
  UsageSwitch = 5,
  UsageModifier = 6,
};

/**
  \brief Values of the hid page usage field in a report.

  Values that the hid page usage can take in a hid report descriptor. Defined
  in section 3 table 1 of the HID 1.11 usage tables.
*/
enum HidUsagePage {
  Undefined = 0,
  GenericDesktopControl = 1,
  SimulationControl = 2,
  VrControl = 3,
  SportControl = 4,
  GameControl = 5,
  GenericDeviceControl = 6,
  KeyboardControl = 7,
  Led = 8,
  Button = 9,
  Ordinal = 10,
  Telephony = 11,
  Consumer = 12,
  Digitlizer = 13,
  PidPage = 15,
  Unicode = 16,
  _HidUsagePage = 0xffff,
};

/**
  \brief Values of the hid desktop page usage in a report.

  Values that usage numbers in the desktop page represent. Defined in 
  section 4 table 6 of the HID 1.11 usage tables. Only items below 0x48 are 
  included here for brevity.
*/
enum HidUsagePageDesktop {
  DesktopPoint = 1,
  DesktopMouse = 2,
  DesktopJoystick = 4,
  DesktopGamePad = 5,
  DesktopKeyboard = 6,
  DesktopKeypad = 7,
  DesktopMultiAxisController = 8,
  DesktopTablePcControl = 9,
  DesktopX = 0x30,
  DesktopY = 0x31,
  DesktopZ = 0x32,
  DesktopRX = 0x33,
  DesktopRY = 0x34,
  DesktopRZ = 0x35,
  DesktopSlider = 0x36,
  DesktopDial = 0x37,
  DesktopWheel = 0x38,
  DesktopHatSwitch = 0x39,
  DesktopCountedBuffer = 0x3a,
  DesktopByteCount = 0x3b,
  DesktopMotionWakeup = 0x3c,
  DesktopStart = 0x3d,
  DesktopSelect = 0x3e,
  DesktopVX = 0x40,
  DesktopVY = 0x41,
  DesktopVZ = 0x42,
  DesktopVbrX = 0x43,
  DesktopVbrY = 0x44,
  DesktopVbrZ = 0x45,
  DesktopVno = 0x46,
  DesktopFeatureNotification = 0x47,
  DesktopResolutionMultiplier = 0x48,
  _HidUsagePageDesktopDummy = 0xffff,
};

/**
  \brief Values of the hid keyboard page usage in a report.

  Values that usage numbers in the keyboard page represent. Defined in 
  section 10 table 12 of the HID 1.11 usage tables. Key definitions are not
  included here, as this driver, being deliberately akward, does not handle 
  them.
*/
enum HidUsagePageKeyboard {
  KeyboardErrorRollOver = 1,
  KeyboardPostFail = 2,
  KeyboardErrorUndefined = 3,
  KeyboardLeftControl = 0xe0,
  KeyboardLeftShift = 0xe1,
  KeyboardLeftAlt = 0xe2,
  KeyboardLeftGui = 0xe3,
  KeyboardRightControl = 0xe4,
  KeyboardRightShift = 0xe5,
  KeyboardRightAlt = 0xe6,
  KeyboardRightGui = 0xe7,
  _HidUsagePageKeyboard = 0xffff,
};

/**
  \brief Values of the hid led page usage in a report.

  Values that usage numbers in the keyboard page represent. Defined in 
  section 11 table 13 of the HID 1.11 usage tables.
*/
enum HidUsagePageLed {
  LedNumberLock = 1,
  LedCapsLock = 2,
  LedScrollLock = 3,
  LedCompose = 4,
  LedKana = 5,
  LedPower = 6,
  LedShift = 7,
  LedMute = 9,
  _HidUsagePageLed = 0xffff,
};

/**
  \brief A full HID usage tag.

  A full HID usage with both the page and the tag represented in the order it 
  can appear in HID report descriptors.
*/
struct HidFullUsage {
  union {
    enum HidUsagePageDesktop Desktop : 16;
    enum HidUsagePageKeyboard Keyboard : 16;
    enum HidUsagePageLed Led : 16;
  };
  enum HidUsagePage Page : 16;
} __attribute__ ((__packed__));

/** 
  \brief A HID field units declaration.

  The units of a HID field. This declaration allows the defintion of many SI 
  units.
*/
struct HidUnit {
  enum {
    SystemNone = 0,
    /** Centimeter, Gram, Second, Kelvin, Ampere, Candela */
    StandardLinear = 1,
    /** Radian, Gram, Second, Kelvin, Ampere, Candela */
    StandardRotation = 2,
    /** Inch, Slug, Second, Fahrenheit, Ampere, Candela */
    EnglishLinear = 3,
    /** Degree, Slug, Second, Fahrenheit, Ampere, Candela */
    EnglishRotation = 4,
  } System : 4;
  signed Length : 4;
  signed Mass : 4;
  signed Time : 4;
  signed Temperature : 4;
  signed Current : 4;
  signed LuminousIntensity : 4;
  unsigned _reserved28_31 : 4;
} __attribute__ ((__packed__));

/**
  \brief A parsed report field, with value.

  A representation of a fully parsed report field complete with value for 
  easy retrieval and setting of values.
*/
struct HidParserField {
  /** Size in bits of this field. For arrays, this is per element. */
  u8 Size;
  /** Offset of this field into the report in bits */
  u8 Offset;
  /** Array fields have a number of individual fields. */
  u8 Count;
  /** Attributes of this field */
  struct HidMainItem Attributes __attribute__((aligned(4)));
  /** Usage of this field. For array elements, this is the first usage, and 
    it is assumed sequential values have sequential usage. */
  struct HidFullUsage Usage;
  /** Usage of the physical connection this device is in, if present. */
  struct HidFullUsage PhysicalUsage;
  /** The minimum value of this field. */
  s32 LogicalMinimum;
  /** The maximum value of this field. */
  s32 LogicalMaximum; 
  /** The minimum physical quantity represented by this field. */
  s32 PhysicalMinimum;
  /** The maximum physical quantity represented by this field. */
  s32 PhysicalMaximum;
  /** The units of this field's physical quantity. */
  struct HidUnit Unit;
  /** The base 10 exponenet of this field's physical quantity. */
  s32 UnitExponent;
  /** Current value of this field. This is the logical value. The value is 
    not sign extended. */
  union {
    u8 U8;
    s8 S8;
    u16 U16;
    s16 S16;
    u32 U32;
    s32 S32;
    bool Bool;
    void* Pointer;
  } Value;
};

/**
  \brief A parsed report, with values.

  A representation of a fully parsed report complete with value fields for 
  easy retrieval and setting of values.
*/
struct HidParserReport {
  /** Which report this is in the parser result. */
  u8 Index;
  /** There can be multiple fields in each report */
  u8 FieldCount;
  /** Report can have an ID. If not we use 0. */
  u8 Id;
  /** The type of this report. */
  enum HidReportType Type;
  /** Length of this report in bytes. */
  u8 ReportLength;
  /** The last report received (if not NULL). */
  u8 *ReportBuffer;
  /** Store the fields sequentially */
  struct HidParserField Fields[] __attribute__((aligned(4)));
};

/**
  \brief A parsed report descriptor, with values.

  A representation of a fully parsed report descriptor complete with value 
  fields for easy retrieval and setting of values.
*/
struct HidParserResult {
  /** Each report descriptor has an application collection with a usage */
  struct HidFullUsage Application; 
  /** There can be multiple reports */
  u8 ReportCount;
  /** The interface number that HID is available on. */
  u8 Interface;
  /** Store a pointer to each report. */
  struct HidParserReport *Report[] __attribute__((aligned(4)));
};

/**
  \brief Retrieves a value from a field.

  Reads the current value at a given index in a HID field.
*/
s32 HidGetFieldValue(struct HidParserField *field, u32 index);


#ifdef __cplusplus
}
#endif

