/******************************************************************************
* hcd/dwc/designware.h
*  by <PERSON>
*
* A light weight implementation of the USB protocol stack fit for a simple
* driver.
*
* hcd/dwc/designware.h contains definitions pertaining to the DesignWare� 
* Hi-Speed USB 2.0 On-The-Go (HS OTG) Controller.
*
* THIS SOFTWARE IS NOT AFFILIATED WITH NOR ENDORSED BY SYNOPSYS IP.
******************************************************************************/
#pragma once

#ifdef __cplusplus
extern "C"
{
#endif

#include <platform/platform.h>

#define ReceiveFifoSize 20480 /* 16 to 32768 */
#define NonPeriodicFifoSize 20480 /* 16 to 32768 */
#define PeriodicFifoSize 20480 /* 16 to 32768 */
#define ChannelCount 16
#define RequestTimeout 5000

/**
  \brief The addresses of all core registers used by the HCD.

  The address offsets of all core registers used by the DesignWare� Hi-Speed 
  USB 2.0 On-The-Go (HS OTG) Controller. Used in write and read throughs.
*/
enum CoreRegisters {
  RegOtgControl = 0x0,
  RegOtgInterrupt = 0x4,
  RegAhb = 0x8,
  RegUsb = 0xc,
  RegReset = 0x10,
  RegInterrupt = 0x14,
  RegInterruptMask = 0x18,
  RegReceivePeek = 0x1c,
  RegReceivePop = 0x20,
  RegReceiveSize = 0x24,
  RegNonPeriodicFifoSize = 0x28,
  RegNonPeriodicFifoStatus = 0x2c,
  RegI2cControl = 0x30,
  RegPhyVendorControl = 0x34,
  RegGpio = 0x38,
  RegUserId = 0x3c,
  RegVendorId = 0x40,
  RegHardware = 0x44,
  RegLowPowerModeConfiguation = 0x48,
  RegMdioControl = 0x80,
  RegMdioRead = 0x84,
  RegMdioWrite = 0x84,
  RegMiscControl = 0x88,
  RegPeriodicFifoSize = 0x100,
  RegPeriodicFifoBase = 0x104,
  RegPower = 0xe00,
};

/**
  \brief The addresses of all core registers used by the HCD.

  The address offsets of all host registers used by the DesignWare� Hi-Speed 
  USB 2.0 On-The-Go (HS OTG) Controller. Used in write and read throughs.
*/
enum HostRegisters {
  RegHostConfig = 0x400,
  RegHostFrameInterval = 0x404,
  RegHostFrameNumber = 0x408,
  RegHostFifoStatus = 0x410,
  RegHostInterrupt = 0x414,
  RegHostInterruptMask = 0x418,
  RegHostFrameList = 0x41c,
  RegHostPort = 0x440,
  RegHostChannelBase = 0x500,
  RegHostChannelCharactristicBase = RegHostChannelBase + 0x0,
  RegHostChannelSplitControl = RegHostChannelBase + 0x4,
  RegHostChannelInterrupt = RegHostChannelBase + 0x8,
  RegHostChannelInterruptMask = RegHostChannelBase + 0xc,
  RegHostChannelTransferSize = RegHostChannelBase + 0x10,
  RegHostChannelDmaAddress = RegHostChannelBase + 0x14,
  RegHostChannelDmaBuffer = RegHostChannelBase + 0x1c,
};

/**
  \brief The interrupts in the core register.

  Contains the core interrutps that controls the DesignWare� Hi-Speed USB 2.0
  On-The-Go (HS OTG) Controller.
*/
struct CoreInterrupts {
  bool CurrentMode : 1; // @0
  bool ModeMismatch : 1; // @1
  bool Otg : 1; // @2
  bool DmaStartOfFrame : 1; // @3
  bool ReceiveStatusLevel : 1; // @4
  bool NpTransmitFifoEmpty : 1; // @5
  bool ginnakeff:1; // @6
  bool goutnakeff:1; // @7
  bool ulpick:1; // @8
  bool I2c : 1; // @9
  bool EarlySuspend : 1; // @10
  bool UsbSuspend : 1; // @11
  bool UsbReset : 1; // @12
  bool EnumerationDone : 1; // @13
  bool IsochronousOutDrop : 1; // @14
  bool eopframe:1; // @15
  bool RestoreDone : 1; // @16
  bool EndPointMismatch : 1; // @17
  bool InEndPoint : 1; // @18
  bool OutEndPoint : 1; // @19
  bool IncompleteIsochronousIn : 1; // @20
  bool IncompleteIsochronousOut : 1; // @21
  bool fetsetup:1; // @22
  bool ResetDetect : 1; // @23
  bool Port : 1; // @24
  bool HostChannel : 1; // @25
  bool HpTransmitFifoEmpty : 1; // @26
  bool LowPowerModeTransmitReceived : 1; // @27
  bool ConnectionIdStatusChange : 1; // @28
  bool Disconnect : 1; // @29
  bool SessionRequest : 1; // @30
  bool Wakeup : 1; // @31
} __attribute__ ((__packed__));

/**
  \brief The interrupts in the channel registers.

  Contains the interrupts that controls the channels of the DesignWare� 
  Hi-Speed USB 2.0 On-The-Go (HS OTG) Controller.
*/
struct ChannelInterrupts {
  bool TransferComplete : 1; // @0
  bool Halt : 1; // @1
  bool AhbError : 1; // @2
  bool Stall : 1; // @3
  bool NegativeAcknowledgement : 1; // @4
  bool Acknowledgement : 1; // @5
  bool NotYet : 1; // @6
  bool TransactionError : 1; // @7
  bool BabbleError : 1; // @8
  bool FrameOverrun : 1; // @9
  bool DataToggleError : 1; // @10
  bool BufferNotAvailable : 1; // @11
  bool ExcessiveTransmission : 1; // @12
  bool FrameListRollover : 1; // @13
  unsigned _reserved14_31 : 18; // @14
} __attribute__ ((__packed__));

/**
  \brief Contains the core global registers structure that control the HCD.

  Contains the core global registers structure that controls the DesignWare�
  Hi-Speed USB 2.0 On-The-Go (HS OTG) Controller.
*/
extern volatile struct CoreGlobalRegs {
  volatile struct {
    volatile bool sesreqscs : 1;
    volatile bool sesreq : 1;
    volatile bool vbvalidoven:1;
    volatile bool vbvalidovval:1;
    volatile bool avalidoven:1;
    volatile bool avalidovval:1;
    volatile bool bvalidoven:1;
    volatile bool bvalidovval:1;
    volatile bool hstnegscs:1;
    volatile bool hnpreq:1;
    volatile bool HostSetHnpEnable : 1;
    volatile bool devhnpen:1;
    volatile unsigned _reserved12_15:4;
    volatile bool conidsts:1;
    volatile unsigned dbnctime:1;
    volatile bool ASessionValid : 1;
    volatile bool BSessionValid : 1;
    volatile unsigned OtgVersion : 1;
    volatile unsigned _reserved21:1;
    volatile unsigned multvalidbc:5;
    volatile bool chirpen:1;
    volatile unsigned _reserved28_31:4;
  } __attribute__ ((__packed__)) OtgControl; // +0x0
  volatile struct {
    volatile unsigned _reserved0_1 : 2; // @0
    volatile bool SessionEndDetected : 1; // @2
    volatile unsigned _reserved3_7 : 5; // @3
    volatile bool SessionRequestSuccessStatusChange : 1; // @8
    volatile bool HostNegotiationSuccessStatusChange : 1; // @9
    volatile unsigned _reserved10_16 : 7; // @10
    volatile bool HostNegotiationDetected : 1; // @17
    volatile bool ADeviceTimeoutChange : 1; // @18
    volatile bool DebounceDone : 1; // @19
    volatile unsigned _reserved20_31 : 12; // @20
  } __attribute__ ((__packed__)) OtgInterrupt; // +0x4
  volatile struct {
    volatile bool InterruptEnable : 1; // @0
    // In accordance with the SoC-Peripherals manual, broadcom redefines 
    // the meaning of bits 1:4 in this structure.
    volatile enum {
      Length4 = 0,
      Length3 = 1,
      Length2 = 2,
      Length1 = 3,
    } AxiBurstLength : 2; // @1
    volatile unsigned _reserved3 : 1; // @3
    volatile bool WaitForAxiWrites : 1; // @4
    volatile bool DmaEnable : 1; // @5
    volatile unsigned _reserved6 : 1; // @6
    volatile enum EmptyLevel {
      Empty = 1,
      Half = 0,
    } TransferEmptyLevel : 1; // @7
    volatile enum EmptyLevel PeriodicTransferEmptyLevel : 1; // @8
    volatile unsigned _reserved9_20 : 12; // @9
    volatile bool remmemsupp:1; // @21
    volatile bool notialldmawrit:1; // @22
    volatile enum {
      Incremental = 0,
      Single = 1, // (default)
    } DmaRemainderMode : 1; // @23
    volatile unsigned _reserved24_31 : 8; // @24
  } __attribute__ ((__packed__)) Ahb; // +0x8
  volatile struct {
    volatile unsigned toutcal:3; // @0
    volatile bool PhyInterface : 1; // @3
    volatile enum UMode {
      ULPI,
      UTMI,
    }  ModeSelect : 1; // @4
    volatile bool fsintf:1; // @5
    volatile bool physel:1; // @6
    volatile bool ddrsel:1; // @7
    volatile bool SrpCapable : 1; // @8
    volatile bool HnpCapable : 1; // @9
    volatile unsigned usbtrdtim:4; // @10
    volatile unsigned reserved1:1; // @14
    /* PHY lower power mode clock select */
    volatile bool phy_lpm_clk_sel:1; // @15
    volatile bool otgutmifssel:1; // @16
    volatile bool UlpiFsls : 1; // @17
    volatile bool ulpi_auto_res:1; // @18
    volatile bool ulpi_clk_sus_m:1; // @19
    volatile bool UlpiDriveExternalVbus : 1; // @20
    volatile bool ulpi_int_vbus_indicator:1; // @21
    volatile bool TsDlinePulseEnable : 1; // @22
    volatile bool indicator_complement:1; // @23
    volatile bool indicator_pass_through:1; // @24
    volatile bool ulpi_int_prot_dis:1; // @25
    volatile bool ic_usb_capable:1; // @26
    volatile bool ic_traffic_pull_remove:1; // @27
    volatile bool tx_end_delay:1; // @28
    volatile bool force_host_mode:1; // @29
    volatile bool force_dev_mode:1; // @30
    volatile unsigned _reserved31:1; // @31
  } __attribute__ ((__packed__)) Usb; // +0xc
  volatile struct CoreReset {
    volatile bool CoreSoft : 1; // @0
    volatile bool HclkSoft : 1; // @1
    volatile bool HostFrameCounter : 1; // @2
    volatile bool InTokenQueueFlush : 1; // @3
    volatile bool ReceiveFifoFlush : 1; // @4
    volatile bool TransmitFifoFlush : 1; // @5
    volatile enum CoreFifoFlush {
      FlushNonPeriodic = 0,
      FlushPeriodic1 = 1,
      FlushPeriodic2 = 2,
      FlushPeriodic3 = 3,
      FlushPeriodic4 = 4,
      FlushPeriodic5 = 5,
      FlushPeriodic6 = 6,
      FlushPeriodic7 = 7,
      FlushPeriodic8 = 8,
      FlushPeriodic9 = 9,
      FlushPeriodic10 = 10,
      FlushPeriodic11 = 11,
      FlushPeriodic12 = 12,
      FlushPeriodic13 = 13,
      FlushPeriodic14 = 14,
      FlushPeriodic15 = 15,
      FlushAll = 16,
    } TransmitFifoFlushNumber : 5; // @6
    volatile unsigned _reserved11_29 : 19; // @11
    volatile bool DmaRequestSignal : 1; // @30
    volatile bool AhbMasterIdle : 1; // @31
  } __attribute__ ((__packed__)) Reset;  // +0x10
  volatile struct CoreInterrupts Interrupt; // +0x14
  volatile struct CoreInterrupts InterruptMask; // +0x18
  volatile struct {
    volatile struct ReceiveStatus {
      volatile unsigned ChannelNumber : 4; // @0
      volatile unsigned bcnt : 11; // @4
      volatile unsigned dpid : 2; // @15
      volatile enum {
        InPacket = 2,
        InTransferComplete = 3,
        DataToggleError = 5,
        ChannelHalted = 7,
      } PacketStatus : 4; // @17
      volatile unsigned _reserved21_31 : 11; // @21
    } __attribute__ ((__packed__)) Peek; // Read Only +0x1c
    volatile const struct ReceiveStatus Pop; // Read Only +0x20
    volatile u32 Size; // +0x24
  } __attribute__ ((__packed__)) Receive; // +0x1c
  volatile struct {
    volatile struct FifoSize {
      volatile unsigned StartAddress : 16; // @0
      volatile unsigned Depth : 16; // @16
    } __attribute__ ((__packed__)) Size; // +0x28
    volatile const struct {
      volatile unsigned SpaceAvailable : 16; // @0
      volatile unsigned QueueSpaceAvailable : 8; // @16
      volatile unsigned Terminate : 1; // @24
      volatile enum {
        InOut = 0,
        ZeroLengthOut = 1,
        PingCompleteSplit = 2,
        ChannelHalt = 3,
      } TokenType : 2; // @25
      volatile unsigned Channel : 4; // @27
      volatile unsigned Odd : 1; // @31
    } __attribute__ ((__packed__)) Status; // Read Only +0x2c
  } __attribute__ ((__packed__)) NonPeriodicFifo; // +0x28
  volatile struct {
    unsigned ReadWriteData : 8; // @0
    unsigned RegisterAddress : 8; // @8
    unsigned Address : 7; // @16
    bool I2cEnable : 1; // @23
    bool Acknowledge : 1; // @24
    bool I2cSuspendControl : 1; // @25
    unsigned I2cDeviceAddress : 2; // @26
    unsigned _reserved28_29 : 2; // @28
    bool ReadWrite : 1; // @30
    bool bsydne : 1; // @31
  } __attribute__ ((__packed__)) I2cControl; // +0x30
  volatile u32 PhyVendorControl; // +0x34
  volatile u32 Gpio; // +0x38
  volatile u32 UserId; // +0x3c
  volatile const u32 VendorId; // Read Only +0x40
  volatile const struct {
    volatile const unsigned Direction0 : 2;
    volatile const unsigned Direction1 : 2;
    volatile const unsigned Direction2 : 2;
    volatile const unsigned Direction3 : 2;
    volatile const unsigned Direction4 : 2;
    volatile const unsigned Direction5 : 2;
    volatile const unsigned Direction6 : 2;
    volatile const unsigned Direction7 : 2;
    volatile const unsigned Direction8 : 2;
    volatile const unsigned Direction9 : 2;
    volatile const unsigned Direction10 : 2;
    volatile const unsigned Direction11 : 2;
    volatile const unsigned Direction12 : 2;
    volatile const unsigned Direction13 : 2;
    volatile const unsigned Direction14 : 2;
    volatile const unsigned Direction15 : 2;
    volatile const enum {
      HNP_SRP_CAPABLE,
      SRP_ONLY_CAPABLE,
      NO_HNP_SRP_CAPABLE,
      SRP_CAPABLE_DEVICE,
      NO_SRP_CAPABLE_DEVICE,
      SRP_CAPABLE_HOST,
      NO_SRP_CAPABLE_HOST,
    } OperatingMode : 3; // @32
    volatile const enum {
      SlaveOnly,
      ExternalDma,
      InternalDma,
    } Architecture : 2; // @35
    bool PointToPoint : 1; // @37
    volatile const enum {
      NotSupported,
      Utmi,
      Ulpi,
      UtmiUlpi,
    } HighSpeedPhysical : 2; // @38
    volatile const enum {
      Physical0,
      Dedicated,
      Physical2,
      Physcial3,
    } FullSpeedPhysical : 2; // @40
    volatile const unsigned DeviceEndPointCount : 4; // @42
    volatile const unsigned HostChannelCount : 4; // @46
    volatile const bool SupportsPeriodicEndpoints : 1; // @50
    volatile const bool DynamicFifo : 1; // @51
    volatile const bool multi_proc_int:1; // @52
    volatile const unsigned _reserver21 : 1; // @53
    volatile const unsigned NonPeriodicQueueDepth : 2; // @54
    volatile const unsigned HostPeriodicQueueDepth : 2; // @56
    volatile const unsigned DeviceTokenQueueDepth : 5; // @58
    volatile const bool EnableIcUsb : 1; // @63
    volatile const unsigned TransferSizeControlWidth : 4; // @64
    volatile const unsigned PacketSizeControlWidth : 3; // @68
    volatile const bool otg_func:1; // @71
    volatile const bool I2c : 1; // @72
    volatile const bool VendorControlInterface : 1; // @73
    volatile const bool OptionalFeatures : 1; // @74
    volatile const bool SynchronousResetType : 1; // @75
    volatile const bool AdpSupport : 1; // @76
    volatile const bool otg_enable_hsic:1; // @77
    volatile const bool bc_support:1; // @78
    volatile const bool LowPowerModeEnabled : 1; // @79
    volatile const unsigned FifoDepth : 16;  // @80
    volatile const unsigned PeriodicInEndpointCount : 4; // @96
    volatile const bool PowerOptimisation : 1; // @100
    volatile const bool MinimumAhbFrequency : 1; // @101
    volatile const bool PartialPowerOff : 1; // @102
    volatile const unsigned _reserved103_109 : 7;  // @103
    volatile const enum {
      Width8bit,
      Width16bit,
      Width8or16bit,
    } UtmiPhysicalDataWidth : 2; // @110
    volatile const unsigned ModeControlEndpointCount : 4; // @112
    volatile const bool ValidFilterIddigEnabled : 1; // @116
    volatile const bool VbusValidFilterEnabled : 1; // @117
    volatile const bool ValidFilterAEnabled : 1; // @118
    volatile const bool ValidFilterBEnabled : 1; // @119
    volatile const bool SessionEndFilterEnabled : 1; // @120
    volatile const bool ded_fifo_en:1; // @121
    volatile const unsigned InEndpointCount : 4; // @122
    volatile const bool DmaDescription : 1; // @126
    volatile const bool DmaDynamicDescription : 1; // @127
  } __attribute__ ((__packed__)) Hardware; // All read only +0x44
  volatile struct {
    volatile bool LowPowerModeCapable : 1; // @0
    volatile bool ApplicationResponse : 1; // @1
    volatile unsigned HostInitiatedResumeDuration : 4; // @2
    volatile bool RemoteWakeupEnabled : 1; // @6
    volatile bool UtmiSleepEnabled : 1; // @7
    volatile unsigned HostInitiatedResumeDurationThreshold : 5; // @8
    volatile unsigned LowPowerModeResponse : 2; // @13
    volatile bool PortSleepStatus : 1;  // @15
    volatile bool SleepStateResumeOk : 1; // @16
    volatile unsigned LowPowerModeChannelIndex : 4; // @17
    volatile unsigned RetryCount : 3; // @21
    volatile bool SendLowPowerMode : 1; // @24
    volatile unsigned RetryCountStatus : 3; // @25
    volatile unsigned _reserved28_29 : 2; // @28
    volatile bool HsicConnect : 1; // @30
    volatile bool InverseSelectHsic : 1; // @31
  } __attribute__ ((__packed__)) LowPowerModeConfiguration; // +0x54
  volatile const u8 _reserved58_80[0x80 - 0x58]; // No read or write +0x58
  volatile struct {
    volatile const unsigned Read : 16; // Read Only @0
    volatile unsigned ClockRatio : 4; // @16
    volatile bool FreeRun : 1; // @20
    volatile bool BithashEnable : 1; // @21
    volatile bool MdcWrite : 1; // @22
    volatile bool MdoWrite : 1; // @23
    volatile unsigned _reserved24_30 : 7; // @24
    volatile const bool Busy : 1; // @31
  } __attribute__ ((__packed__)) MdioControl; // +0x80
  volatile union {
    volatile const u32 MdioRead; // Read Only +0x84
    volatile u32 MdioWrite; // +0x84
  };
  volatile struct {
    volatile bool SessionEnd : 1; // @0
    volatile bool VbusValid : 1; // @1
    volatile bool BSessionValid : 1; // @2
    volatile bool ASessionValid : 1; // @3
    volatile const bool DischargeVbus : 1; // Read Only @4
    volatile const bool ChargeVbus : 1; // Read Only @5
    volatile const bool DriveVbus : 1; // Read Only @6
    volatile bool DisableDriving : 1; // @7
    volatile bool VbusIrqEnabled : 1; // @8
    volatile const bool VbusIrq : 1; // Cleared on Read! @9
    volatile unsigned _reserved10_15 : 6; // @10
    volatile unsigned AxiPriorityLevel : 4; // @16
    volatile unsigned _reserved20_31 : 12; // @20
  } __attribute__ ((__packed__)) MiscControl; // +0x88
  volatile u8 _reserved8c_100[0x100-0x8c]; // +0x8c
  volatile struct {
    volatile struct FifoSize HostSize; // +0x100
    volatile struct FifoSize DataSize[15]; // +0x104
  } __attribute__ ((__packed__)) PeriodicFifo; // +0x100
  volatile u8 _reserved140_400[0x400-0x140]; // +0x140
} __attribute__ ((__packed__)) *CorePhysical, *Core;

/**
  \brief Contains the host mode global registers structure that control the HCD.

  Contains the host mode global registers structure that controls the DesignWare�
  Hi-Speed USB 2.0 On-The-Go (HS OTG) Controller.
*/
extern volatile struct HostGlobalRegs {
  volatile struct {
    volatile enum {
      Clock30_60MHz,
      Clock48MHz,
      Clock6MHz
    } ClockRate : 2; // @0
    volatile bool FslsOnly : 1; // @2
    volatile unsigned _reserved3_6 : 4; // @3
    volatile unsigned en_32khz_susp:1; // @7
    volatile unsigned res_val_period:8; // @8
    volatile unsigned _reserved16_22 : 7; // @16
    volatile bool EnableDmaDescriptor : 1; // @23
    volatile unsigned FrameListEntries : 2; // @24
    volatile bool PeriodicScheduleEnable : 1; // @26
    volatile const bool PeriodicScheduleStatus : 1; // @27
    volatile unsigned reserved28_30 : 3; // @28
    volatile bool mode_chg_time:1; // @31
  } __attribute__ ((__packed__)) Config; // +0x400
  volatile struct {
    volatile unsigned Interval : 16; // @0
    volatile bool DynamicFrameReload : 1; // @16
    volatile unsigned _reserved17_31 : 15; // @17
  } __attribute__ ((__packed__)) FrameInterval; // +0x404
  volatile struct {
    volatile unsigned FrameNumber : 16; // @0
    volatile unsigned FrameRemaining : 16; // @16
  } __attribute__ ((__packed__)) FrameNumber; // +0x408
  volatile u32 _reserved40c; // + 0x40c
  volatile struct {
    volatile unsigned SpaceAvailable : 16; // @0
    volatile unsigned QueueSpaceAvailable : 8; // @16
    volatile unsigned Terminate : 1; // @24
    volatile enum {
      ZeroLength = 0,
      Ping = 1,
      Disable = 2,
    } TokenType : 2; // @25
    volatile unsigned Channel : 4; // @27
    volatile unsigned Odd : 1; // @31
  } __attribute__ ((__packed__)) FifoStatus; // +0x410
  volatile u32 Interrupt; // +0x414
  volatile u32 InterruptMask; // +0x418
  volatile u32 FrameList; // +0x41c
  volatile u8 _reserved420_440[0x440-0x420]; // +0x420
  volatile struct HostPort {
    volatile bool Connect : 1; // @0
    volatile bool ConnectDetected : 1; // @1
    volatile bool Enable : 1; // @2
    volatile bool EnableChanged : 1; // @3
    volatile bool OverCurrent : 1; // @4
    volatile bool OverCurrentChanged : 1; // @5
    volatile bool Resume : 1; // @6
    volatile bool Suspend : 1; // @7
    volatile bool Reset : 1; // @8
    volatile unsigned _reserved9 : 1; // @9
    volatile unsigned PortLineStatus : 2; // @10
    volatile bool Power : 1; // @12
    volatile unsigned TestControl : 4; // @13
    volatile UsbSpeed Speed : 2; // @17
    volatile unsigned _reserved19_31 : 13; // @19
  } __attribute__ ((__packed__)) Port; // +0x440
  volatile u8 _reserved444_500[0x500 - 0x444]; // +0x444
  volatile struct HostChannel {
    volatile struct HostChannelCharacteristic {
      volatile unsigned MaximumPacketSize : 11; // @0
      volatile unsigned EndPointNumber : 4; // @11
      volatile UsbDirection EndPointDirection : 1; // @15
      volatile unsigned _reserved16 : 1; // @16
      volatile bool LowSpeed : 1; // @17
      UsbTransfer Type : 2; // @18
      volatile unsigned PacketsPerFrame : 2; // @20
      volatile unsigned DeviceAddress : 7; // @22
      volatile unsigned OddFrame  : 1; // @29
      volatile bool Disable : 1; // @30
      volatile bool Enable : 1; // @31
    } __attribute__ ((__packed__)) Characteristic; // +0x0
    volatile struct {
      volatile unsigned PortAddress : 7; // @0
      volatile unsigned HubAddress : 7; // @7
      volatile enum {
        Middle = 0,
        End = 1,
        Begin = 2,
        All = 3,
      } TransactionPosition : 2; // @14
      volatile bool CompleteSplit : 1; // @16
      volatile unsigned _reserved17_30 : 14; // @17
      volatile bool SplitEnable : 1; // @31
    } __attribute__ ((__packed__)) SplitControl; // +0x4
    volatile struct ChannelInterrupts Interrupt; // +0x8
    volatile struct ChannelInterrupts InterruptMask; // +0xc
    volatile struct {
      volatile unsigned TransferSize : 19; // @0
      volatile unsigned PacketCount : 10; // @19
      volatile enum PacketId {
        Data0 = 0,
        Data1 = 2,
        Data2 = 1,
        MData = 3,
        Setup = 3,
      } PacketId : 2; // @29
      volatile bool DoPing : 1; // @31
    } __attribute__ ((__packed__)) TransferSize; // +0x10
    volatile void* DmaAddress;  // +0x14
    volatile u32 _reserved18; // +0x18
    volatile u32 _reserved1c; // +0x1c
  } __attribute__ ((__packed__)) Channel[ChannelCount]; // +0x500
  volatile u8 _reserved700_800[0x800 - 0x700]; // +0x700
} __attribute__ ((__packed__)) *HostPhysical, *Host;

/**
  \brief Contains the dwc power and clock gating controls.

  Contains the dwc power and clock gating structure that controls the DesignWare�
  Hi-Speed USB 2.0 On-The-Go (HS OTG) Controller.
*/
extern volatile struct PowerReg {
  volatile bool StopPClock : 1; // @0
  volatile bool GateHClock : 1; // @1
  volatile bool PowerClamp : 1; // @2
  volatile bool PowerDownModules : 1; // @3
  volatile bool PhySuspended : 1; // @4
  volatile bool EnableSleepClockGating : 1; // @5
  volatile bool PhySleeping : 1; // @6
  volatile bool DeepSleep : 1; // @7
  volatile unsigned _reserved8_31 : 24; // @8
} __attribute__ ((__packed__)) *PowerPhysical, *Power;

/** 
  \brief Indicates if the Phy has been initialised.

  Indicaes if the Phy has been initialised, as this can only be done once.
*/
extern bool PhyInitialised;

/** 
  \brief The device number of the root hub.

  We keep track of the device number of the virtual root hub we are 
  simulating.
*/
extern u32 RootHubDeviceNumber;

/**
  \brief Sends a message to the virtual root hub for processing.

  Passes a message to the virtual root hub for processing. The buffer can be 
  both in and out. 
*/
Result HcdProcessRootHubMessage(struct UsbDevice *device, 
  struct UsbPipeAddress pipe, void* buffer, u32 bufferLength,
  struct UsbDeviceRequest *request);

/** 
  \brief Writes through a register from the cache to the device.

  Causes a register in the cache to be written through to the host device. To 
  avoid unintended consequences, this method prevents modifications to 
  certain registers, or fields within registers, such as those whith 
  differing read/write behaviour.
*/
void WriteThroughReg(volatile const void* reg);
/** 
  \brief Writes through a register from the cache to the device.

  Causes a register in the cache to be written through to the host device. To 
  avoid unintended consequences, this method prevents modifications to 
  certain registers, or fields within registers, such as those whith 
  differing read/write behaviour. The mask is ored with the standard mask for
  different read write behaviour so that these can be set.
*/
void WriteThroughRegMask(volatile const void* reg, u32 maskOr);
/** 
  \brief Reads back a register from the device to the cache.

  Causes a register on the host device to be copied back to the cache. To 
  avoid unintended consequences, this method prevents reading to 
  certain registers, or fields within registers, such as those whith 
  differing read/write behaviour. Specific methods are provided in place of
  this for such functions.
*/
void ReadBackReg(volatile const void* reg);
/** 
  \brief Resets a register within the cache.

  Causes a register in the cache to be reset to it's default values. Exact 
  meanings differ, but it is generally in preparation to write back a fresh 
  value to a field.
*/
void ClearReg(volatile const void* reg);
/** 
  \brief Resets a register within the cache to all on.

  Causes a register in the cache to be reset to it's 'on' values. Exact 
  meanings differ, but it is generally in preparation to write back a 
  field mostly to 1. Used in interrutps for example to set all the 
  interrutps to 1 in preparation for reseting the interrupt flags.
*/
void SetReg(volatile const void* reg);

#ifdef __cplusplus
}
#endif

