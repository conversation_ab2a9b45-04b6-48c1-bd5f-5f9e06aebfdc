/******************************************************************************
* hcd/hcd.h
*  by <PERSON>
*
* A light weight implementation of the USB protocol stack fit for a simple
* driver.
*
* hcd/hcd.h contains definitions relating to the host controller driver of
* the USB implementation.
******************************************************************************/

#pragma once

#ifdef __cplusplus
extern "C"
{
#endif


#include <usbd/device.h>
#include <usbd/devicerequest.h>
#include <usbd/pipe.h>
#include <usb_types.h>

/**
  \brief Intialises the host controller driver for this hardware.

  Initialises the core on whatever hardware is in use. 
*/
Result HcdInitialise();

/** 
  \brief Starts the host controller driver working.

  Starts the host controller driver working. It should start processing 
  communications and be ready for commands.
*/
Result HcdStart();

/** 
  \brief Stops the host controller driver working.

  Stops the host controller driver working. This should close all connections
  and return the Hcd to a state such that a subsequent call to StopHcd would 
  restart everything. This could be to update parameters, fix a fault, etc.
*/
Result HcdStop();

/** 
  \brief Uninitialises the host controller driver.

  Unitialises the host controller driver. This should be called when the 
  driver is to be complete removed. It should power off any hardware, and 
  and return the driver to a point such that a subsequent call to 
  InitialiseHcd would restart it.
*/
Result HcdDeinitialise();

/**
  \brief Sends a control message to a device.

  Sends a control message to a device. Handles all necessary channel creation
  and other processing. The sequence of a control transfer is defined in the 
  USB 2.0 manual section 5.5. The host sends a setup packet (request) then 
  zero or more data packets are sent or received (to buffer, max length 
  bufferLength) and finally a status message is sent to conclude the 
  transaction. Packets larger than pipe.MaxSize are split. For low speed 
  devices pipe.MaxSize must be Bits8, and Bits64 for high speed. Low and full
  speed transactions are always split.
*/
Result HcdSumbitControlMessage(struct UsbDevice *device, 
  struct UsbPipeAddress pipe, void* buffer, u32 bufferLength,
  struct UsbDeviceRequest *request);

#include "dwc/designware20.h"

#ifdef __cplusplus
}
#endif

