/******************************************************************************
* usbd/devicerequest.h
*  by <PERSON>
*
* A light weight implementation of the USB protocol stack fit for a simple
* driver.
*
* usbd/devicerequest.h contains a definition of the standard device 
* request structure defined in USB2.0
******************************************************************************/
#pragma once

#ifdef __cplusplus
extern "C"
{
#endif

#include <usb_types.h>

/**
  \brief An encapsulated device request.

  A device request is a standard mechanism defined in USB2.0 manual section
  9.3 by which negotiations with devices occur. The request have a number of 
  parameters, and so are best implemented with a structure. As per usual,
  since this structure is arbitrary, we shall match Linux in the hopes of 
  achieving some compatibility.
*/
struct UsbDeviceRequest {
  u8 Type; // +0x0
  enum UsbDeviceRequestRequest {
    // USB requests
    GetStatus = 0,
    ClearFeature = 1,
    SetFeature = 3,
    SetAddress = 5,
    GetDescriptor = 6,
    SetDescriptor = 7,
    GetConfiguration = 8,
    SetConfiguration = 9,
    GetInterface = 10,
    SetInterface = 11,
    SynchFrame = 12,
    // HID requests
    GetReport = 1,
    GetIdle = 2,
    GetProtocol = 3,
    SetReport = 9,
    SetIdle = 10,
    SetProtocol = 11,
  } Request : 8; // +0x1
  u16 Value; // +0x2
  u16 Index; // +0x4
  u16 Length; // +0x6
} __attribute__ ((__packed__));


#ifdef __cplusplus
}
#endif

