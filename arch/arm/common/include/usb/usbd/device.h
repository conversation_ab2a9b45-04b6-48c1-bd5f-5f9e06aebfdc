/******************************************************************************
* usbd/device.h
*  by <PERSON>
*
* A light weight implementation of the USB protocol stack fit for a simple
* driver.
*
* usbd/device.h contains a definition of a device structure used for 
* storing devices and the device tree.
******************************************************************************/
#pragma once

#ifdef __cplusplus
extern "C"
{
#endif

#include <usbd/descriptors.h>
#include <usb_types.h>

/** 
  \brief The maximum number of children a device could have, by implication, this is 
  the maximum number of ports a hub supports. 
  
  This is theoretically 255, as 8 bits are used to transfer the port count in
  a hub descriptor. Practically, no hub has more than 10, so we instead allow 
  that many. Increasing this number will waste space, but will not have 
  adverse consequences up to 255. Decreasing this number will save a little 
  space in the HubDevice structure, at the risk of removing support for an 
  otherwise valid hub. 
*/
#define MaxChildrenPerDevice 10
/** 
  \brief The maximum number of interfaces a device configuration could have. 

  This is theoretically 255 as one byte is used to transfer the interface 
  count in a configuration descriptor. In practice this is unlikely, so we 
  allow an arbitrary 8. Increasing this number wastes (a lot) of space in 
  every device structure, but should not have other consequences up to 255.
  Decreasing this number reduces the overheads of the UsbDevice structure, at
  the cost of possibly rejecting support for an otherwise supportable device. 
*/
#define MaxInterfacesPerDevice 8
/** 
  \brief The maximum number of endpoints a device could have (per interface). 
  
  This is theoretically 16, as four bits are used to transfer the endpoint 
  number in certain device requests. This is possible in practice, so we 
  allow that many. Decreasing this number reduces the space in each device
  structure considerably, while possible removing support for otherwise valid
  devices. This number should not be greater than 16.
*/
#define MaxEndpointsPerDevice 16

/**
  \brief Status of a USB device.

  Stores the status of a USB device. Statuses as defined in 9.1 of the USB2.0 
  manual.
*/
enum UsbDeviceStatus {
  Attached = 0,
  Powered = 1,
  Default = 2,
  Addressed = 3,
  Configured = 4,
};


/**
  \brief Status of a USB transfer.

  Stores the status of the last transfer a USB device did.
*/
enum UsbTransferError {
  NoError = 0,
  Stall = 1 << 1,
  BufferError = 1 << 2,
  Babble = 1 << 3,
  NoAcknowledge = 1 << 4,
  CrcError = 1 << 5,
  BitError = 1 << 6,
  ConnectionError = 1 << 7,
  AhbError = 1 << 8,
  NotYetError = 1 << 9,
  Processing = 1 << 31
};

/**
  \brief Start of a device specific data field.

  The first two words of driver data in a UsbDevice. The  DeviceDriver field 
  is a code which uniquely identifies the driver that set the driver data 
  field (i.e. the lowest driver in the stack above the USB driver). The 
  DataSize is the size in bytes of the device specific data field. 
*/
struct UsbDriverDataHeader {
  u32 DeviceDriver;
  u32 DataSize;
};

/**
  \brief Structure to store the details of a USB device that has been 
  detectd.

  Stores the details about a connected USB device. This is not directly part
  of the USB standard, and is instead a mechanism used to control the device
  tree.
*/
struct UsbDevice {
  u32 Number;

  UsbSpeed Speed;
  enum UsbDeviceStatus Status;
  volatile u8 ConfigurationIndex;
  u8 PortNumber;
  volatile enum UsbTransferError Error __attribute__((aligned(4)));
  
  // Generic device handlers
  /** Handler for detaching the device. The device driver should not issue further requests to the device. */
  void (*DeviceDetached)(struct UsbDevice *device) __attribute__((aligned(4)));
  /** Handler for deallocation of the device. All memory in use by the device driver should be deallocated. */
  void (*DeviceDeallocate)(struct UsbDevice *device);
  /** Handler for checking for changes to the USB device tree. Only hubs need handle with this. */
  void (*DeviceCheckForChange)(struct UsbDevice *device);
  /** Handler for removing a child device from this device. Only hubs need handle with this. */
  void (*DeviceChildDetached)(struct UsbDevice *device, struct UsbDevice *child);
  /** Handler for reseting a child device of this device. Only hubs need handle with this. */
  Result (*DeviceChildReset)(struct UsbDevice *device, struct UsbDevice *child);
  /** Handler for reseting a child device of this device. Only hubs need handle with this. */
  Result (*DeviceCheckConnection)(struct UsbDevice *device, struct UsbDevice *child);
  
  volatile struct UsbDeviceDescriptor Descriptor __attribute__((aligned(4)));
  volatile struct UsbConfigurationDescriptor Configuration __attribute__((aligned(4)));
  volatile struct UsbInterfaceDescriptor Interfaces[MaxInterfacesPerDevice] __attribute__((aligned(4)));
  volatile struct UsbEndpointDescriptor Endpoints[MaxInterfacesPerDevice][MaxEndpointsPerDevice] __attribute__((aligned(4)));
  struct UsbDevice *Parent __attribute__((aligned(4)));
  volatile void *FullConfiguration;
  volatile struct UsbDriverDataHeader *DriverData;
  volatile u32 LastTransfer;
};

#define InterfaceClassAttachCount 16

/**
  \brief Methods to attach a particular interface for a particular class.

  The class of the interface is the index into this array of methods. The
  array is populated by ConfigurationLoad().
*/
extern Result (*InterfaceClassAttach[InterfaceClassAttachCount])(struct UsbDevice *device, u32 interfaceNumber);

#ifdef __cplusplus
}
#endif

