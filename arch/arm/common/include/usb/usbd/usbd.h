/******************************************************************************
* usbd/usbd.h
*  by <PERSON>
*
* A light weight implementation of the USB protocol stack fit for a simple
* driver.
*
* usbd/usbd.h contains definitions relating to the generic USB driver. USB
* is designed such that this driver's interface would be virtually the same
* across all systems, and in fact its implementation varies little either.
******************************************************************************/

#pragma once

#ifdef __cplusplus
extern "C"
{
#endif

#include <usb_types.h>
#include <usbd/device.h>
#include <usbd/devicerequest.h>
#include <usbd/pipe.h>

/**
  \brief Performs all necessary operationg to start the USB driver.

  Initialises the USB driver by performing necessary interfactions with the
  host controller driver, and enumerating the device tree.
*/
Result UsbInitialise();

/**
  \brief Gets the descriptor for a given device.

  Gets the descriptor for a given device, using the index and language id 
  specified. The returned value is not longer than length.
*/
Result UsbGetDescriptor(struct UsbDevice *device, enum DescriptorType type, 
  u8 index, u16 langId, void* buffer, u32 length, u32 minimumLength, u8 recipient);

/**
  \brief Sends a control message synchronously to a given device.

  Sends a contorl message synchronously to a given device, then waits for 
  completion. If the timeout is reached returns ErrorTimeout. This puts
  device into an inconsistent state, so best not to use it until processing
  is unset.
*/
Result UsbControlMessage(struct UsbDevice *device, 
  struct UsbPipeAddress pipe, void* buffer, u32 bufferLength,
  struct UsbDeviceRequest *request, u32 timeout);

/**
  \brief Allocates memory to a new device.

  Sets the value in the parameter device to the address of a new device 
  allocated on the heap, which then has appropriate default values.
*/
Result UsbAllocateDevice(struct UsbDevice **device);

/*
  \brief Deallocates the memory and resources of a USB device.

  Recursively deallocates the device and all children. Deallocates any class
  specific data, as well as the device structure itself and releases the 
  device number.
*/
void UsbDeallocateDevice(struct UsbDevice *device);

/**
  \brief Recursively enumerates a new device.

  Recursively enumerates a new device that has been allocated. This assigns
  an address, determines what the device is, and, if it is a hub, will 
  configure the device recursively look for new devices. If not, it will 
  configure the device with the default configuration.
*/
Result UsbAttachDevice(struct UsbDevice *device);

/**
  \brief Returns a description for a device.

  Returns a description for a device. This is not read from the device, this 
  is just generated given by the driver.
*/
const char* UsbGetDescription(struct UsbDevice *device);

/**
  \brief Returns a pointer to the root hub device.

  On a Universal Serial Bus, there exists a root hub. This if often a virtual
  device, and typically represents a one port hub, which is the physical 
  universal serial bus for this computer. It is always address 1. It is 
  present to allow uniform software manipulation of the universal serial bus 
  itself.
*/
struct UsbDevice *UsbGetRootHub();

/**
  \brief Scans the entire USB tree for changes.

  Recursively calls HubCheckConnection on all ports on all hubs connected to 
  the root hub.
*/
void UsbCheckForChange();


#ifdef __cplusplus
}
#endif

