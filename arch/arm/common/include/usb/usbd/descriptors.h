/******************************************************************************
* usbd/descriptors.h
*  by <PERSON>
*
* A light weight implementation of the USB protocol stack fit for a simple
* driver.
*
* usbd/descriptors.h contains structures defined in the USB standard that
* describe various aspects of USB.
******************************************************************************/
#pragma once

#ifdef __cplusplus
extern "C"
{
#endif

#include <usb_types.h>

/**
  \brief The descriptor type field from the header of USB descriptors.

  The descriptor type in the header of all USB descriptor sturctures defined
  in the USB 2.0 manual in 9.6.
*/
enum DescriptorType {
  Device = 1,
  Configuration = 2,
  String = 3,
  Interface = 4,
  Endpoint = 5,
  DeviceQualifier = 6,
  OtherSpeedConfiguration = 7,
  InterfacePower = 8,
  Hid = 33,
  HidReport = 34,
  HidPhysical = 35, 
  Hub = 41,
};

/**
  \brief The header of USB descriptor information.

  The header of all USB descriptor sturctures defined in the USB 2.0 manual 
  in 9.6.
*/
struct UsbDescriptorHeader {
  u8 DescriptorLength; // +0x0
  enum DescriptorType DescriptorType : 8; // +0x1
} __attribute__ ((__packed__));

/**
  \brief The device descriptor information.

  The device descriptor sturcture defined in the USB 2.0 manual in 9.6.1.
*/
enum DeviceClass {
  DeviceClassInInterface = 0x00,
  DeviceClassCommunications = 0x2,
  DeviceClassHub = 0x9,
  DeviceClassDiagnostic = 0xdc,
  DeviceClassMiscellaneous = 0xef,
  DeviceClassVendorSpecific = 0xff,
};
struct UsbDeviceDescriptor {
  u8 DescriptorLength; // +0x0
  enum DescriptorType DescriptorType : 8; // +0x1
  u16 UsbVersion; // (in BCD 0x210 = USB2.10) +0x2
  enum DeviceClass Class : 8; // +0x4
  u8 SubClass; // +0x5
  u8 Protocol; // +0x6
  u8 MaxPacketSize0; // +0x7
  u16 VendorId; // +0x8
  u16 ProductId; // +0xa
  u16 Version; // +0xc
  u8 Manufacturer; // +0xe
  u8 Product; // +0xf
  u8 SerialNumber; // +0x10
  u8 ConfigurationCount; // +0x11
} __attribute__ ((__packed__));

/**
  \brief The device qualifier descriptor information.

  The device descriptor qualifier sturcture defined in the USB 2.0 manual in 
  9.6.2.
*/
struct UsbDeviceQualifierDescriptor {
  u8 DescriptorLength; // +0x0
  enum DescriptorType DescriptorType : 8; // +0x1
  u16 UsbVersion; // (in BCD 0x210 = USB2.10) +0x2
  enum DeviceClass Class : 8; // +0x4
  u8 SubClass; // +0x5
  u8 Protocol; // +0x6
  u8 MaxPacketSize0; // +0x7
  u8 ConfigurationCount; // +0x8
  u8 _reserved9; // +0x9
} __attribute__ ((__packed__));

/**
  \brief The configuration descriptor information.

  The configuration descriptor structure defined in the USB2.0 manual section
  9.6.3.
*/
struct UsbConfigurationDescriptor {
  u8 DescriptorLength; // +0x0
  enum DescriptorType DescriptorType : 8; // +0x1
  u16 TotalLength; // +0x2
  u8 InterfaceCount; // +0x4
  u8 ConfigurationValue; // +0x5
  u8 StringIndex; // +0x6
  struct {
    unsigned _reserved0_4 : 5; // @0
    bool RemoteWakeup : 1; // @5
    bool SelfPowered : 1; // @6
    unsigned _reserved7 : 1; // @7
  } __attribute__ ((__packed__)) Attributes; // +0x7
  u8 MaximumPower; // +0x8
} __attribute__ ((__packed__));

/**
  \breif The other speed configuration descriptor.

  The other speed configuration descriptor defined in the USB2.0 manual section 
  9.6.4.
*/
struct UsbOtherSpeedConfigurationDescriptor {
  u8 DescriptorLength; // +0x0
  enum DescriptorType DescriptorType : 8; // +0x1
  u16 TotalLength; // +0x2
  u8 InterfaceCount; // +0x4
  u8 ConfigurationValue; // +0x5
  u8 StringIndex; // +0x6
  struct {
    unsigned _reserved0_4 : 5; // @0
    bool RemoteWakeup : 1; // @5
    bool SelfPowered : 1; // @6
    enum {
      Valid = 1,
    } _reserved7 : 1; // @7
  } __attribute__ ((__packed__)) Attributes; // +0x7
  u8 MaximumPower; // +0x8
} __attribute__ ((__packed__));

/**
  \brief The interface descriptor information.

  The interface descriptor structure defined in the USB2.0 manual section 
  9.6.5.
*/
struct UsbInterfaceDescriptor {
  u8 DescriptorLength; // +0x0
  enum DescriptorType DescriptorType : 8; // +0x1
  u8 Number; // +0x2
  u8 AlternateSetting; // +0x3
  u8 EndpointCount; // +0x4
  enum InterfaceClass {
    InterfaceClassReserved = 0x0,
    InterfaceClassAudio = 0x1,
    InterfaceClassCommunications = 0x2,
    InterfaceClassHid = 0x3,
    InterfaceClassPhysical = 0x5,
    InterfaceClassImage = 0x6,
    InterfaceClassPrinter = 0x7,
    InterfaceClassMassStorage = 0x8,
    InterfaceClassHub = 0x9,
    InterfaceClassCdcData = 0xa,
    InterfaceClassSmartCard = 0xb,
    InterfaceClassContentSecurity = 0xd,
    InterfaceClassVideo = 0xe,
    InterfaceClassPersonalHealthcare = 0xf,
    InterfaceClassAudioVideo = 0x10,
    InterfaceClassDiagnosticDevice = 0xdc,
    InterfaceClassWirelessController = 0xe0,
    InterfaceClassMiscellaneous = 0xef,
    InterfaceClassApplicationSpecific = 0xfe,
    InterfaceClassVendorSpecific = 0xff,
  } Class : 8; // +x05
  u8 SubClass;
  u8 Protocol;
  u8 StringIndex;
} __attribute__ ((__packed__));

/**
  \brief The endpoint descriptor information.

  The endpoint descriptor structure defined in the USB2.0 manual section 
  9.6.6.
*/
struct UsbEndpointDescriptor {
  u8 DescriptorLength; // +0x0
  enum DescriptorType DescriptorType : 8; // +0x1
  struct {
    unsigned Number : 4; // @0
    unsigned _reserved4_6 : 3; // @4
    UsbDirection Direction : 1; // @7
  } __attribute__ ((__packed__)) EndpointAddress; // +0x2
  struct {
    UsbTransfer Type : 2; // @0
    enum {
      NoSynchronisation = 0,
      Asynchronous = 1,
      Adaptive = 2,
      Synchrouns = 3,
    } Synchronisation : 2; // @2
    enum {
      Data = 0,
      Feeback = 1,
      ImplicitFeebackData = 2,
    } Usage : 2; // @4
    unsigned _reserved6_7 : 2; // @6
  } __attribute__ ((__packed__)) Attributes; // +0x3
  struct {
    unsigned MaxSize : 11; // @0
    enum {
      None = 0,
      Extra1 = 1,
      Extra2 = 2,
    } Transactions : 2; // @11
    unsigned _reserved13_15 : 3; // @13
  } __attribute__ ((__packed__)) Packet; // +0x4
  u8 Interval; // +0x6
} __attribute__ ((__packed__));

/**
  \brief The string descriptor information.

  The string descriptor structure defined in the USB2.0 manual section 
  9.6.7.
*/
struct UsbStringDescriptor {
  u8 DescriptorLength; // +0x0
  enum DescriptorType DescriptorType : 8; // +0x1
  u16 Data[]; // +0x2 amount varies
} __attribute__ ((__packed__));

#ifdef __cplusplus
}
#endif

