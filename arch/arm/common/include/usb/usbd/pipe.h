/******************************************************************************
* usbd/pipe.h
*  by <PERSON>
*
* A light weight implementation of the USB protocol stack fit for a simple
* driver.
*
* usbd/pipe.h contains definitions relating to the USB pipe structure,
* defined as part of the USB protocol. The precise details of this data 
* structure are an implementation detail, matching Linux in this case to 
* aid compatibility.
******************************************************************************/
#pragma once

#ifdef __cplusplus
extern "C"
{
#endif

#include <usb_types.h>

/**
  \brief Our implementation of the USB pipe defined in 10.5.1. 

  The Usb<PERSON>ipeAddress holds the address of a pipe. The USB standard defines 
  these as a software mechanism for communication between the USB driver and the
  host controller driver. We shall not have a concept of creating or destroying 
  pipes, as this is needless clutter, and simply just indicate the pipe by its
  physical properties. In other words, we identify the pipe by its physical 
  consequences on the USB. This is similar to Linux, and vastly reduces 
  complication, at the expense of requiring a little more sophistication on the
  sender's behalf. 
*/
struct UsbPipeAddress {
  UsbPacketSize MaxSize : 2; // @0
  UsbSpeed Speed : 2; // @2
  unsigned EndPoint : 4; // @4
  unsigned Device : 8; // @8
  UsbTransfer Type : 2; // @16
  UsbDirection Direction : 1; // @18
  unsigned _reserved19_31 : 13; // @19
} __attribute__ ((__packed__));


#ifdef __cplusplus
}
#endif

