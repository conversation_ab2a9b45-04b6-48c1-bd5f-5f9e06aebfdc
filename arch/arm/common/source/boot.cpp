#include "types.h"
#include "board_constants.h"
#include "init_boottime_pagetables.h"
#include "assert.h"
#include "kstring.h"
#include "ArchBoardSpecific.h"

extern "C" void __attribute__((naked)) PagingMode();
extern "C" void startup();
extern uint8 bss_start_address;
extern uint8 bss_end_address;
extern PageDirEntry kernel_page_directory[];
extern uint8 boot_stack[];
uint32 multicore_sync __attribute__ ((section ("multicore")));

#define BOOT_OFFSET (BOARD_LOAD_BASE - 0x80000000)

extern "C" void __attribute__((naked)) entry()
{
  asm("mov fp, #0\n"
      "mov sp, %[v]" : : [v]"r"(((uint8*)boot_stack) + BOOT_OFFSET + 0x4000)); // Set up the stack

  // Only cpu 0 is allowed to continue, stop all other cores here using a spinlock
  void (*disableMulticorePTR)(uint32*) = (void(*)(uint32*))((uint8*)ArchBoardSpecific::disableMulticore + BOOT_OFFSET);
  disableMulticorePTR((uint32 * )(((uint8 * ) & multicore_sync) + BOOT_OFFSET));

  void (*memsetPTR)(void*,uint8,size_t) = (void(*)(void*,uint8,size_t))((uint8*)&memset + BOOT_OFFSET);
  memsetPTR((void*)(&bss_start_address - BOOT_OFFSET), 0, (uint32)&bss_end_address - (uint32)&bss_start_address);

  void (*initialiseBootTimePagingPTR)() = (void(*)())((uint8*)&initialiseBootTimePaging + BOOT_OFFSET);
  initialiseBootTimePagingPTR();
  asm("mcr p15, 0, %[v], c2, c0, 0\n" : : [v]"r"(((uint8*)kernel_page_directory) + BOOT_OFFSET)); // set ttbr0
  asm("mcr p15, 0, %[v], c8, c7, 0\n" : : [v]"r"(0)); // tlb flush
  asm("mcr p15, 0, %[v], c3, c0, 0\n" : : [v]"r"(0b111)); // set domain access control (domain 0 in manager mode, domain 1 in usr mode)
  asm("mrc p15, 0, r0, c1, c0, 0\n"
      "orr r0, r0, #0x1\n"          // set paging bit
      "bic r0, r0, #0x2\n"          // disable alignment fault bit
      "orr r0, r0, #0x400000\n"     // set unaligned memory access bit
      "orr r0, r0, #0x800000\n"     // set disable subpages bit
      "mcr p15, 0, r0, c1, c0, 0\n" // enable paging
     );

  void (*PagingModePTR)() = &PagingMode; // create a blx jump instead of a bl jump
  PagingModePTR();
  assert(false && "it should be impossible to get to this point");
}

extern "C" void __attribute__((naked)) PagingMode()
{
  asm("mrs r0, cpsr\n"
      "bic r0, r0, #0xdf\n"
      "orr r0, r0, #0xdf\n"
      "msr cpsr, r0\n");
  asm("mov sp, %[v]\n" : : [v]"r"(((uint32*)boot_stack)+4096)); // Set up the stack

  void (*startupPTR)() = &startup; // create a blx jump instead of a bl jump
  startupPTR();
  assert(false && "you should never get to this point");

}

void mapBootTimePage(PageDirEntry *pde_start, uint32 pdi, uint32 ppn_1m)
{
  pde_start[pdi].page.reserved_1 = 0;
  pde_start[pdi].page.permissions = 1;
  pde_start[pdi].page.reserved_2 = 0;
  pde_start[pdi].page.domain = 0;
  pde_start[pdi].page.reserved_3 = 0;
  pde_start[pdi].page.cachable = 0;
  pde_start[pdi].page.bufferable = 0;
  pde_start[pdi].page.size = 2;
  pde_start[pdi].page.page_ppn = ppn_1m;
}
