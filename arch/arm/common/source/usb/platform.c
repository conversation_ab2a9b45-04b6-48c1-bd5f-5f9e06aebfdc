/******************************************************************************
* platform/platform.c
*  by <PERSON>
*
* A light weight implementation of the USB protocol stack fit for a simple
* driver.
*
* platform/platform.c contains code for generic system duties such as memory
* management and logging, which can be optionally disabled. 
******************************************************************************/
#include <platform/platform.h>
#include <stdarg.h>
#include "debug.h"
#include "board_constants.h"
#include "kmalloc.h"
#include "kstring.h"

void* MemoryReserve(u32 length __attribute__((unused)), void* physicalAddress) {
  return physicalAddress;
}

void* MemoryAllocate(u32 size) {
  return (void*)(kmalloc(size + 0x2000) + 0x1000);  // well i don't trust it totally right now...
}
void MemoryDeallocate(void* address) {
  kfree(address - 0x1000);
}
void MemoryCopy(void* destination, void* source, u32 length)
{
  memcpy(destination, source, length);
}

#define FLOAT_TEXT "Floats unsupported."

#ifndef NO_LOG
void LogPrintF(const char* format, u32 formatLength, ...) {
  va_list args;
  char messageBuffer[160];
  u32 messageIndex, width = 0, precision = 1, characters;
  s32 svalue; u32 value;
  bool opened, flagged, widthed = false, precisioned = false, length = false;
  bool left = false, plus = false, space = false, hash = false, zero = false, nwidth = false, period = false, nprecision = false;
  bool longInt = false, shortInt = false;
  char character; char* string;
  u8 base;
  messageIndex = 0;
  opened = false;
  
  va_start(args, formatLength);

  for (u32 index = 0; index < formatLength && messageIndex < sizeof(messageBuffer) - 1; index++) {
    if (opened) {
      if (!flagged)
        switch (format[index]) {
        case '-':
          if (!left) left = true;
          else flagged = true;
          break;
        case '+':
          if (!plus) plus = true;
          else flagged = true;
          break;
        case ' ':
          if (!space) space = true;
          else flagged = true;
          break;
        case '#':
          if (!hash) hash = true;
          else flagged = true;
          break;
        case '0':
          if (!zero) zero = true;
          else flagged = true;
          break;
        default:
          flagged = true;
        }
      if (flagged && !widthed) {
        switch (format[index]) {
        case '0': case '1':
        case '2': case '3':
        case '4': case '5':
        case '6': case '7':
        case '8': case '9':
          nwidth = true;
          width *= 10;
          width += format[index] - '0';
          continue;
        case '*':
          if (!nwidth) {
            widthed = true;
            width = va_arg(args, u32);
            continue;
          }
          else
            widthed = true;
          break;
        default:
          widthed = true;
        }     
      }
      if (flagged && widthed && !precisioned) {
        if (!period) {
          if (format[index] == '.') {
            period = true;
            precision = 0;
            continue;
          } else
            precisioned = true;
        }
        else {
          switch (format[index]) {
          case '0': case '1':
          case '2': case '3':
          case '4': case '5':
          case '6': case '7':
          case '8': case '9':
            nprecision = true;
            precision *= 10;
            precision += format[index] - '0';
            continue;
          case '*':
            if (!nprecision) {
              precisioned = true;
              precision = va_arg(args, u32);
              continue;
            }
            else
              precisioned = true;
            break;
          default:
            precisioned = true;
          }
        }
      }
      if (flagged && widthed && precisioned && !length) {
        switch (format[index]) {
        case 'h':
          length = true;
          shortInt = true;
          continue;
        case 'l':
          length = true;
          longInt = true;
          continue;
        case 'L':
          length = true;
          continue;
        default:
          length = true;
        }
      }
      if (flagged && widthed && precisioned && length) {
        character = '%';
        base = 16;
        switch (format[index]) {
        case 'c':
          character = va_arg(args, int) & 0x7f;
          // fall through
        case '%':
          messageBuffer[messageIndex++] = character;
          break;
        case 'd':
        case 'i':
          if (shortInt) svalue = (s32)((s16)va_arg(args, s32) & 0xffff);
          else if (longInt) svalue = va_arg(args, s64);
          else svalue = va_arg(args, s32);

          characters = 1;
          if (svalue < 0) {
            svalue = -svalue;
            messageBuffer[messageIndex++] = '-';
          }
          else if (plus)
            messageBuffer[messageIndex++] = '-';
          else if (space)
            messageBuffer[messageIndex++] = ' ';
          else 
            characters = 0;

          for (u32 digits = 0; digits < precision || svalue > 0; digits++, characters++) {
            for (u32 j = 0; j < digits; j++)
              if (messageIndex - j < sizeof(messageBuffer) - 1)
                messageBuffer[messageIndex - j] = messageBuffer[messageIndex - j - 1];
            if (messageIndex - digits < sizeof(messageBuffer) - 1)
              messageBuffer[messageIndex++ -digits] = '0' + (svalue % 10);
            svalue /= 10;
          }   
          
          if (characters < width) {
            if (!left)
              for (u32 i = 0; i <= characters; i++) {
                if (messageIndex - characters + width - i < sizeof(messageBuffer) - 1)
                  messageBuffer[messageIndex - characters + width - i] = 
                    messageBuffer[messageIndex - i];
              }

            for (u32 digits = characters; characters < width; characters++) {
              if (messageIndex - (!left ? digits : 0) < sizeof(messageBuffer) - 1)
                messageBuffer[messageIndex - (!left ? digits : 0)] = zero ? '0' : ' '; 
            }
          }
          break;
        case 'e':
        case 'E':
        case 'f':
        case 'g':
        case 'G':
          for (u32 i = 0; (i < width || i < sizeof(FLOAT_TEXT)) && messageIndex < sizeof(messageBuffer) - 1; i++) {
            if (i < sizeof(FLOAT_TEXT))
              messageBuffer[messageIndex++] = FLOAT_TEXT[i];
            else 
              messageBuffer[messageIndex++] = zero ? '0' : ' ';
          }
          break;
        case 'o':
          base = 8;
          // fall through
        case 'u':
          if (format[index] == 'u') base = 10;
          // fall through
        case 'x':
        case 'X':
        case 'p':
          if (shortInt) value = va_arg(args, u32) & 0xffff;
          else if (longInt) value = va_arg(args, u64);
          else value = va_arg(args, u32);

          characters = 1;
          if (plus)
            messageBuffer[messageIndex++] = '-';
          else if (space)
            messageBuffer[messageIndex++] = ' ';
          else 
            characters = 0;

          if (hash) {
            if (format[index] == 'o') {
              if (messageIndex < sizeof(messageBuffer) - 1) 
                messageBuffer[messageIndex++] = '0';
              characters++;
            }
            else if (format[index] != 'u') {
              if (messageIndex < sizeof(messageBuffer) - 1) 
                messageBuffer[messageIndex++] = '0';
              characters++;
              if (messageIndex < sizeof(messageBuffer) - 1) 
                messageBuffer[messageIndex++] = format[index];
              characters++;
            }
          }
              

          for (u32 digits = 0; digits < precision || value > 0; digits++, characters++) {
            for (u32 j = 0; j < digits; j++)
              if (messageIndex - j < sizeof(messageBuffer) - 1)
                messageBuffer[messageIndex - j] = messageBuffer[messageIndex - j - 1];
            if (messageIndex - digits < sizeof(messageBuffer) - 1)
              messageBuffer[messageIndex++ -digits] =
               (value % base) >= 10 ? format[index] - ('X' - 'A') + ((value % base) - 10) : '0' + (value % base);
            value /= base;
          }   

          if (characters < width) {
            if (!left)
              for (u32 i = 0; i <= characters; i++) {
                if (messageIndex - characters + width - i < sizeof(messageBuffer) - 1)
                  messageBuffer[messageIndex - characters + width - i] = 
                    messageBuffer[messageIndex - i];
              }

            for (u32 digits = characters; characters < width; characters++) {
              if (messageIndex - (!left ? digits : 0) < sizeof(messageBuffer) - 1)
                messageBuffer[messageIndex++ - (!left ? digits : 0)] = zero ? '0' : ' '; 
            }
          }
          break;
        case 's':
          string = va_arg(args, char*);
          for (u32 i = 0; messageIndex < sizeof(messageBuffer) - 1 && string[i] != '\0' && (!period || i < precision); i++) {
            messageBuffer[messageIndex++] = string[i];
          }
          break;
        case 'n':
          *va_arg(args, u32*) = messageIndex;
          break;
        }
        opened = false;
      }
    } else if (format[index] == '%') {
      opened = true;
      flagged = false;
      widthed = false;
      precisioned = false;
      length = false;
      width = 0; precision = 1;
      left = false; plus = false; space = false; hash = false; zero = false; nwidth = false; period = false; nprecision = false;
      longInt = false; shortInt = false;
    }
    else
      messageBuffer[messageIndex++] = format[index];
  }

  va_end(args);
  
  LogPrint(messageBuffer, messageIndex);
}
#endif

void LogPrint(const char* message, u32 messageLength)
{
  if (!(A_KB_MANAGER & 0x80000000))
    return;
  u32 counter;
  for(counter = 0; (*message != '\0') && (counter++ < messageLength); message++ )
  {
    /* Wait until the serial buffer is empty */
    while (*(volatile unsigned long*)(SERIAL_BASE + SERIAL_FLAG_REGISTER)
                                       & (SERIAL_BUFFER_FULL));
    /* Put our character, c, into the serial buffer */
    *(volatile unsigned long*)SERIAL_BASE = *message;
  }
}
