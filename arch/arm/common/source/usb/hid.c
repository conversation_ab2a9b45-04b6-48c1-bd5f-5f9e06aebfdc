/******************************************************************************
* device/hid/hid.c
*  by <PERSON>
*
* A light weight implementation of the USB protocol stack fit for a simple
* driver.
*
* device/hid/hid.c contains code relating to the generic USB human interface 
* device driver. Human interface devices have another standard on top of USB
* (oh boy!) which is actually very neat. It allows human interface devices to
* describe their buttons, sliders and dials in great detail, and allows a 
* flexible driver to handle them all. This driver merely provides methods to
* deal with these reports. More abstracted drivers for keyboards and mice and
* whatnot would no doubt be very useful.
******************************************************************************/
#include <device/hid/hid.h>
#include <device/hid/report.h>
#include <platform/platform.h>
#include <usbd/descriptors.h>
#include <usbd/device.h>
#include <types.h>
#include <usbd/usbd.h>

#define HidMessageTimeout 10

Result (*HidUsageAttach[HidUsageAttachCount])(struct UsbDevice *device, u32 interfaceNumber);

void HidLoad() 
{
  LOG_DEBUG("CSUD: HID driver version 0.1\n"); 
  InterfaceClassAttach[InterfaceClassHid] = HidAttach;
}

Result HidGetReport(struct UsbDevice *device, enum HidReportType reportType, 
  u8 reportId, u8 interface, u32 bufferLength, void* buffer) {
  Result result;
  
  if ((result = UsbControlMessage(
    device, 
    (struct UsbPipeAddress) { 
      .Type = Control, 
      .Speed = device->Speed, 
      .EndPoint = 0 , 
      .Device = device->Number, 
      .Direction = In,
      .MaxSize = SizeFromNumber(device->Descriptor.MaxPacketSize0),
    },
    buffer,
    bufferLength,
    &(struct UsbDeviceRequest) {
      .Request = GetReport,
      .Type = 0xa1,
      .Index = interface,
      .Value = (u16)reportType << 8 | reportId,
      .Length = bufferLength,
    },
    HidMessageTimeout)) != OK) 
    return result;

  return OK;
}

Result HidSetReport(struct UsbDevice *device, enum HidReportType reportType, 
  u8 reportId, u8 interface, u32 bufferLength, void* buffer) {
  Result result;
  
  if ((result = UsbControlMessage(
    device, 
    (struct UsbPipeAddress) { 
      .Type = Control, 
      .Speed = device->Speed, 
      .EndPoint = 0 , 
      .Device = device->Number, 
      .Direction = Out,
      .MaxSize = SizeFromNumber(device->Descriptor.MaxPacketSize0),
    },
    buffer,
    bufferLength,
    &(struct UsbDeviceRequest) {
      .Request = SetReport,
      .Type = 0x21,
      .Index = interface,
      .Value = (u16)reportType << 8 | reportId,
      .Length = bufferLength,
    },
    HidMessageTimeout)) != OK) 
    return result;

  return OK;
}

void BitSet(void* buffer, u32 offset, u32 length, u32 value) {
  u8* bitBuffer;
  u8 mask;

  bitBuffer = buffer;
  for (u32 i = offset / 8, j = 0; i < (offset + length + 7) / 8; i++) {
    if (offset / 8 == (offset + length - 1) / 8) {
      mask = (1 << ((offset % 8) + length)) - (1 << (offset % 8));    
      bitBuffer[i] = (bitBuffer[i] & ~mask) |
        ((value << (offset % 8)) & mask);
    } else if (i == offset / 8) {
      mask = 0x100 - (1 << (offset % 8));   
      bitBuffer[i] = (bitBuffer[i] & ~mask) |
        ((value << (offset % 8)) & mask);
      j += 8 - (offset % 8);
    } else if (i == (offset + length - 1) / 8) {
      mask = (1 << ((offset % 8) + length)) - 1;    
      bitBuffer[i] = (bitBuffer[i] & ~mask) |
        ((value >> j) & mask);
    } else {      
      bitBuffer[i] = (value >> j) & 0xff;
      j += 8;
    }      
  }
}

u32 BitGetUnsigned(void* buffer, u32 offset, u32 length) {
  u8* bitBuffer;
  u8 mask;
  u32 result;
  
  bitBuffer = buffer;
  result = 0;
  for (u32 i = offset / 8, j = 0; i < (offset + length + 7) / 8; i++) {
    if (offset / 8 == (offset + length - 1) / 8) {
      mask = (1 << ((offset % 8) + length)) - (1 << (offset % 8));    
      result = (bitBuffer[i] & mask) >> (offset % 8);
    } else if (i == offset / 8) {
      mask = 0x100 - (1 << (offset % 8));   
      j += 8 - (offset % 8);
      result = ((bitBuffer[i] & mask) >> (offset % 8)) << (length - j);
    } else if (i == (offset + length - 1) / 8) {
      mask = (1 << ((offset % 8) + length)) - 1;    
      result |= bitBuffer[i] & mask;
    } else {      
      j += 8;
      result |= bitBuffer[i] << (length - j);
    } 
  }

  return result;
}

s32 BitGetSigned(void* buffer, u32 offset, u32 length) {
  u32 result = BitGetUnsigned(buffer, offset, length);

  if (result & (1 << (length - 1)))
    result |= 0xffffffff - ((1 << length) - 1);

  return result;
}

Result HidReadDevice(struct UsbDevice *device, u8 reportNumber) {
  struct HidDevice *data;
  struct HidParserResult *parse;
  struct HidParserReport *report;
  struct HidParserField *field;
  Result result;
  u32 size;
  
  data = (struct HidDevice*)device->DriverData;
  parse = data->ParserResult;
  report = parse->Report[reportNumber];
  size = ((report->ReportLength + 7) / 8);
  if ((report->ReportBuffer == NULL) && (report->ReportBuffer = (u8*)MemoryAllocate(size)) == NULL) {
    return ErrorMemory;
  }
  if ((result = HidGetReport(device, report->Type, report->Id, data->ParserResult->Interface, size, report->ReportBuffer)) != OK) {
    if (result != ErrorDisconnected)
      LOGF("HID: Could not read %s report %p.\n", UsbGetDescription(device), report);
    return result;
  }
  
  // Uncomment this for a quick hack to view 8 bytes worth of report.
  /*
  LOGF("HID: %s.Report%d: %02x%02x%02x%02x %02x%02x%02x%02x.\n", UsbGetDescription(device), reportNumber + 1,
    *(report->ReportBuffer + 0), *(report->ReportBuffer + 1), *(report->ReportBuffer + 2), *(report->ReportBuffer + 3),
    *(report->ReportBuffer + 4), *(report->ReportBuffer + 5), *(report->ReportBuffer + 6), *(report->ReportBuffer + 7));
  */

  for (u32 i = 0; i < report->FieldCount; i++) {
    field = &report->Fields[i];
    if (field->Attributes.Variable) {
      if (field->LogicalMinimum < 0)
        field->Value.S32 = BitGetSigned(report->ReportBuffer, field->Offset, field->Size);
      else
        field->Value.U32 = BitGetUnsigned(report->ReportBuffer, field->Offset, field->Size);
    } else {
      for (u32 j = 0; j < field->Count; j++) {
        BitSet(
            field->Value.Pointer,
            j * field->Size, 
            field->Size, 
            field->LogicalMinimum < 0 ? (u32)BitGetSigned(
              report->ReportBuffer, 
              field->Offset + j * field->Size, 
              field->Size
            ) : BitGetUnsigned(
              report->ReportBuffer, 
              field->Offset + j * field->Size, 
              field->Size
            )
          );        
      }
    }
  }

  return OK;
}

Result HidWriteDevice(struct UsbDevice *device, u8 reportNumber) {
  struct HidDevice *data;
  struct HidParserResult *parse;
  struct HidParserReport *report;
  struct HidParserField *field;
  Result result;
  u32 size;
  
  data = (struct HidDevice*)device->DriverData;
  parse = data->ParserResult;
  report = parse->Report[reportNumber];
  size = ((report->ReportLength + 7) / 8);
  if ((report->ReportBuffer == NULL) && (report->ReportBuffer = (u8*)MemoryAllocate(size)) == NULL) {
    return ErrorMemory;
  }
  for (u32 i = 0; i < report->FieldCount; i++) {
    field = &report->Fields[i];
    if (field->Attributes.Variable) {
      BitSet(
          report->ReportBuffer, 
          field->Offset, 
          field->Size,
          field->Value.S32
        );
    } else {
      for (u32 j = 0; j < field->Count; j++) {
        BitSet(
            report->ReportBuffer,
            field->Offset + j * field->Size, 
            field->Size,
            BitGetSigned(
              field->Value.Pointer, 
              j * field->Size, 
              field->Size
            )
          );        
      }
    }
  }
  
  if ((result = HidSetReport(device, report->Type, report->Id, data->ParserResult->Interface, size, report->ReportBuffer)) != OK) {
    if (result != ErrorDisconnected)
      LOGF("HID: Coult not read %s report %p.\n", UsbGetDescription(device), report);
    return result;
  }

  return OK;
}

Result HidSetProtocol(struct UsbDevice *device, u8 interface, u16 protocol) {
  Result result;
  
  if ((result = UsbControlMessage(
    device, 
    (struct UsbPipeAddress) { 
      .Type = Control, 
      .Speed = device->Speed, 
      .EndPoint = 0 , 
      .Device = device->Number, 
      .Direction = Out,
      .MaxSize = SizeFromNumber(device->Descriptor.MaxPacketSize0),
    },
    NULL,
    0,
    &(struct UsbDeviceRequest) {
      .Request = SetProtocol,
      .Type = 0x21,
      .Index = interface,
      .Value = protocol,
      .Length = 0,
    },
    HidMessageTimeout)) != OK) 
    return result;

  return OK;
}

void HidEnumerateReport(void* descriptor, u16 length, void(*action)(void* data, u16 tag, u32 value), void* data) {
  struct HidReportItem *item, *current;
  u16 parsedLength, currentIndex, currentLength;
  u16 tag; // tags for short items will be stored in the low 6 bits, tags for long items will be in the top 8 bits.
  s32 value;
  
  item = descriptor;  
  current = NULL;
  parsedLength = 0;
  
  while (parsedLength < length) {
    if (current == NULL) {
      current = item;
      currentIndex = 0;
      currentLength = 1 << (current->Size - 1);
      value = 0;
      tag = current->Tag;
      if (currentLength == 0)
        current = NULL;
    } else {
      if (current->Tag == TagLong && currentIndex < 2) {
        if (currentIndex == 0) currentLength += *(u8*)item;       
        else tag |= (u16)*(u8*)item << 8;
      } else {
        value |= (u32)*(u8*)item << (8 * currentIndex);
      }
      if (++currentIndex == currentLength)
        current = NULL;
    }

    if (current == NULL) {
      if ((tag & 0x3) == 0x1) {
        if (currentLength == 1 && (value & 0x80))
          value |= 0xffffff00;
        else if (currentLength == 2 && (value & 0x8000))
          value |= 0xffff0000;
      }

      action(data, tag, value);
    }

    item++;
    parsedLength++;
  }
}

void HidEnumerateActionCountReport(void* data, u16 tag, u32 value) {
  struct {
    u8 reportCount;
    u8 indent;
    bool input, output, feature;
  } *reports = data;

  struct messages
  {
    u8 tag;
    const char* msg;
  } msgs[] =
  {
    { TagMainInput, "HID: %.*sInput(%03o)\n" }, { TagMainOutput, "HID: %.*sOutput(%03o)\n" },
    { TagMainFeature, "HID: %.*sFeature(%03o)\n" }, { TagMainCollection, "HID: %.*sCollection(%d)\n" },
    { TagMainEndCollection, "HID: %.*sEnd Collection\n" }, { TagGlobalReportId, "HID: %.*sReport ID(%d)\n" },
    { TagGlobalUsagePage, "HID: %.*sUsage Page(%d)\n" }, { TagGlobalLogicalMinimum, "HID: %.*sLogical Minimum(%d)\n" },
    { TagGlobalLogicalMaximum, "HID: %.*sLogical Maximum(%d)\n" }, { TagGlobalPhysicalMinimum, "HID: %.*sPhysical Minimum(%d)\n" },
    { TagGlobalPhysicalMaximum, "HID: %.*sPhysical Maximum(%d)\n" }, { TagGlobalUnitExponent, "HID: %.*sUnit Exponent(%d)\n" },
    { TagGlobalUnit, "HID: %.*sUnit(%d)\n" }, { TagGlobalReportSize, "HID: %.*sReport Size(%d)\n" },
    { TagGlobalReportCount, "HID: %.*sReport Count(%d)\n" }, { TagGlobalPush, "HID: %.*sPush\n" },
    { TagGlobalPop, "HID: %.*sPop\n" }, { TagLocalUsage, "HID: %.*sUsage(%u)\n" },
    { TagLocalUsageMinimum, "HID: %.*sUsage Minimum(%u)\n" }, { TagLocalUsageMaximum, "HID: %.*sUsage Maximum(%u)\n" },
    { TagLocalDesignatorIndex, "HID: %.*sDesignator Index(%u)\n" }, { TagLocalDesignatorMinimum, "HID: %.*sDesignator Minimum(%u)\n" },
    { TagLocalDesignatorMaximum, "HID: %.*sDesignator Maximum(%u)\n" }, { TagLocalStringIndex, "HID: %.*sString Index(%u)\n" },
    { TagLocalStringMinimum, "HID: %.*sString Minimum(%u)\n" }, { TagLocalStringMaximum, "HID: %.*sString Maximum(%u)\n" },
    { TagLocalDelimiter, "HID: %.*sDelimiter\n" }
  };
  u8 found = 0;
  switch (tag) {
  case TagMainInput:
    if (!reports->input) { reports->reportCount++; reports->input = true; }
    break;
  case TagMainOutput:
    if (!reports->output) { reports->reportCount++; reports->output = true; }
    break;
  case TagMainFeature:
    if (!reports->feature) { reports->reportCount++; reports->feature = true; }
    break;
  case TagGlobalReportId:
    reports->input = reports->output = reports->feature = false;
    break;
  }
  for (size_t i = 0; i < sizeof(msgs)/sizeof(msgs[0]); ++i)
  {
    if (tag == msgs[i].tag)
    {
      found = 1;
      LOG_DEBUGF(msgs[i].msg, reports->indent, "           ", value);
      break;
    }
  }
  if (!found)
  {
    LOG_DEBUGF("HID: Unexpected tag in report %d = %x.\n", tag, value);
  }
}

void HidEnumerateActionCountField(void* data, u16 tag, u32 value) {
  struct reportFields_t {
    u32 count;
    u8 current;
    u8 report;
    struct reportFields_inner_t {
      u8 reportId;
      u8 fieldCount;
      enum HidReportType type;
    } reports[];
  } *reportFields = data;
  struct reportFields_inner_t *inner;
  enum HidReportType type;
  
  type = 0;
  switch (tag) {
  case TagMainFeature: type++;
    // fall through
  case TagMainOutput: type++;
    // fall through
  case TagMainInput: type++;
    inner = NULL;
    for (u32 i = 0; i < reportFields->current; i++) {
      if (reportFields->reports[i].reportId == reportFields->report &&
        reportFields->reports[i].type == type) {
        inner = &reportFields->reports[i];
        break; 
      }
    }
    if (inner == NULL) {
      inner = &reportFields->reports[reportFields->current++];
      inner->reportId = reportFields->report;
      inner->fieldCount = 0;
      inner->type = type;
    }
    if (((struct HidMainItem*)&value)->Variable) inner->fieldCount += reportFields->count;
    else inner->fieldCount++;
    break;
  case TagGlobalReportCount:
    reportFields->count = value;
    break;
  case TagGlobalReportId:
    reportFields->report = value;
    break;
  default: break;
  }
}

void HidEnumerateActionAddField(void* data, u16 tag, u32 value) {
  struct fields_t {
    struct HidParserResult *result;
    u32 count;
    u32 size;
    struct HidFullUsage *usage;
    struct HidFullUsage physical;
    s32 logicalMinimum;
    s32 logicalMaximum; 
    s32 physicalMinimum;
    s32 physicalMaximum;
    struct HidUnit unit;
    s32 unitExponent;   
    enum HidUsagePage page;
    u8 report;
  } *fields = data;
  enum HidReportType type;
  struct HidParserReport *report;
  u32 i;

  type = 0;
  switch (tag) {
  case TagMainFeature: type++;
    // fall through
  case TagMainOutput: type++;
    // fall through
  case TagMainInput: type++;
    report = NULL;
    for (i = 0; i < fields->result->ReportCount; i++)
      if (fields->result->Report[i]->Id == fields->report &&
        fields->result->Report[i]->Type == type) {
        report = fields->result->Report[i];
        break; 
      }
    while (fields->count > 0) {
      if (*(u32*)fields->usage == 0xffffffff) fields->usage++;
      *(u32*)&(report->Fields[report->FieldCount].Attributes) = value;
      report->Fields[report->FieldCount].Count = report->Fields[report->FieldCount].Attributes.Variable ? 1 : fields->count;
      report->Fields[report->FieldCount].LogicalMaximum = fields->logicalMaximum;
      report->Fields[report->FieldCount].LogicalMinimum = fields->logicalMinimum;
      report->Fields[report->FieldCount].Offset = report->ReportLength;
      report->Fields[report->FieldCount].PhysicalMaximum = fields->physicalMaximum;
      report->Fields[report->FieldCount].PhysicalMinimum = fields->physicalMinimum;
      *(u32*)&report->Fields[report->FieldCount].PhysicalUsage = *(u32*)&fields->physical;
      report->Fields[report->FieldCount].Size = fields->size;
      *(u32*)&report->Fields[report->FieldCount].Unit = *(u32*)&fields->unit;
      report->Fields[report->FieldCount].UnitExponent = fields->unitExponent;
      if ((u16)fields->usage->Page == 0xffff) {
        *(u32*)&report->Fields[report->FieldCount].Usage = *(u32*)&fields->usage[-1];
        if (fields->usage->Desktop == fields->usage[-1].Desktop ||
          !report->Fields[report->FieldCount].Attributes.Variable)
          fields->usage -= 2;
        else
          fields->usage[-1].Desktop++;
      } else {
        *(u32*)&report->Fields[report->FieldCount].Usage = *(u32*)(fields->usage--);
      }
      if (report->Fields[report->FieldCount].Attributes.Variable) {
        fields->count--;
        report->ReportLength += report->Fields[report->FieldCount].Size;
        report->Fields[report->FieldCount].Value.U32 = 0;
      }
      else {
        fields->count = 0;
        report->ReportLength += report->Fields[report->FieldCount].Size * report->Fields[report->FieldCount].Count;
        report->Fields[report->FieldCount].Value.Pointer = MemoryAllocate(report->Fields[report->FieldCount].Size * report->Fields[report->FieldCount].Count / 8);
      }
      report->FieldCount++;
    }
    *(u32*)&fields->usage[1] = 0;
    break;
  case TagMainCollection:
    if (*(u32*)fields->usage == 0xffffffff) fields->usage++;
    switch ((enum HidMainCollection)value) {
    case Application:     
      *(u32*)&fields->result->Application = *(u32*)fields->usage;
      break;
    case Physical:
      *(u32*)&fields->physical = *(u32*)fields->usage;
      break;
    default:
      break;
    }
    fields->usage--;
    break;
  case TagMainEndCollection:
    switch ((enum HidMainCollection)value) {
    case Physical:
      *(u32*)&fields->physical = 0;
      break;
    default:
      break;
    }
    break;
  case TagGlobalUsagePage:    
    fields->page = (enum HidUsagePage)value;
    break;
  case TagGlobalLogicalMinimum:
    fields->logicalMinimum = value;
    break;
  case TagGlobalLogicalMaximum:
    fields->logicalMaximum = value;
    break;
  case TagGlobalPhysicalMinimum:
    fields->physicalMinimum = value;
    break;
  case TagGlobalPhysicalMaximum:
    fields->physicalMaximum = value;
    break;
  case TagGlobalUnitExponent:
    fields->unitExponent = value;
    break;
  case TagGlobalUnit:
    *(u32*)&fields->unit = value;
    break;
  case TagGlobalReportSize:
    fields->size = value;
    break;
  case TagGlobalReportId:
    fields->report = (u8)value;
    break;
  case TagGlobalReportCount:
    fields->count = value;
    break;
  case TagLocalUsage:
    fields->usage++;
    if (value & 0xffff0000)
      *(u32*)&fields->usage = value;
    else {
      fields->usage->Desktop = (enum HidUsagePageDesktop)value;
      fields->usage->Page = fields->page;
    }
    break;
  case TagLocalUsageMinimum:
    fields->usage++;
    if (value & 0xffff0000)
      *(u32*)&fields->usage = value;
    else {
      fields->usage->Desktop = (enum HidUsagePageDesktop)value;
      fields->usage->Page = fields->page;
    }
    break;
  case TagLocalUsageMaximum:
    fields->usage++;
    fields->usage->Desktop = (enum HidUsagePageDesktop)value;
    fields->usage->Page = (enum HidUsagePage)0xffff;
    break;
  default: break;
  }
}

Result HidParseReportDescriptor(struct UsbDevice *device, void* descriptor, u16 length) {
  Result result;
  struct HidDevice *data;
  struct HidParserResult *parse = NULL;
  struct {
    u8 reportCount;
    u8 indent;
    bool input, output, feature;
  } reports = { .reportCount = 0, .indent = 0, .input = false, .output = false, .feature = false };
  struct reportFields_t {
    u32 count;
    u8 current;
    u8 report;
    struct reportFields_inner_t {
      u8 reportId;
      u8 fieldCount;
      enum HidReportType type;
    } reports[];
  } *reportFields = NULL;
  struct fields_t {
    struct HidParserResult *result;
    u32 count;
    u32 size;
    struct HidFullUsage *usage;
    struct HidFullUsage physical;
    s32 logicalMinimum;
    s32 logicalMaximum; 
    s32 physicalMinimum;
    s32 physicalMaximum;
    struct HidUnit unit;
    s32 unitExponent;   
    enum HidUsagePage page;
    u8 report;
  } *fields = NULL;
  void* usageStack = NULL;

  data = (struct HidDevice*)device->DriverData;

  HidEnumerateReport(descriptor, length, HidEnumerateActionCountReport, &reports);
  LOG_DEBUGF("HID: Found %d reports.", reports.reportCount);

  if ((parse = MemoryAllocate(sizeof(struct HidParserResult) + 4 * reports.reportCount)) == NULL) {
    result = ErrorMemory;
    goto deallocate;
  }
  for (u32 i = 0; i < reports.reportCount; i++) {
    parse->Report[i] = NULL;
  }
  if ((reportFields = MemoryAllocate(sizeof(struct reportFields_t) + sizeof(struct reportFields_inner_t) * reports.reportCount)) == NULL) {
    result = ErrorMemory;
    goto deallocate;
  }
  parse->ReportCount = reports.reportCount;
  reportFields->count = 0;
  reportFields->current = 0;
  reportFields->report = 0;

  HidEnumerateReport(descriptor, length, HidEnumerateActionCountField, reportFields);
  for (u32 i = 0; i < reports.reportCount; i++) {
    if ((parse->Report[i] = MemoryAllocate(sizeof(struct HidParserReport) + sizeof(struct HidParserField) * reportFields->reports[i].fieldCount)) == NULL) {
      result = ErrorMemory;
      goto deallocate;
    }
    parse->Report[i]->Index = i;
    parse->Report[i]->FieldCount = 0;
    parse->Report[i]->Id = reportFields->reports[i].reportId;
    parse->Report[i]->Type = reportFields->reports[i].type;
    parse->Report[i]->ReportLength = 0;
    parse->Report[i]->ReportBuffer = NULL;
  } 
  MemoryDeallocate(reportFields);
  reportFields = NULL;

  if ((fields = MemoryAllocate(sizeof(struct fields_t))) == NULL) {
    result = ErrorMemory;
    goto deallocate;
  }
  if ((fields->usage = usageStack = MemoryAllocate(16 * sizeof(struct HidFullUsage*))) == NULL) {
    result = ErrorMemory;
    goto deallocate;
  } 
  fields->count = 0;
  fields->logicalMaximum = 0;
  fields->logicalMinimum = 0;
  fields->physicalMaximum = 0;
  fields->physicalMinimum = 0;
  fields->report = 0;
  fields->size = 0;
  *(u32*)fields->usage = 0xffffffff;
  fields->result = parse;
  HidEnumerateReport(descriptor, length, HidEnumerateActionAddField, fields);
  
  data->ParserResult = parse;
  parse = NULL;
  result = OK;
deallocate:
  if (usageStack != NULL) MemoryDeallocate(usageStack);
  if (fields != NULL) MemoryDeallocate(fields);
  if (reportFields != NULL) MemoryDeallocate(reportFields);
  if (parse != NULL) {
    for (u32 i = 0; i < reports.reportCount; i++)
      if (parse->Report[i] != NULL) MemoryDeallocate(parse->Report[i]);
    MemoryDeallocate(parse);
  }
  return result;
}

void HidDetached(struct UsbDevice *device) {
  struct HidDevice *data;
  
  if (device->DriverData != NULL) {
    data = (struct HidDevice*)device->DriverData;

    if (data->HidDetached != NULL)
      data->HidDetached(device);
  }
}

void HidDeallocate(struct UsbDevice *device) {
  struct HidDevice *data;
  struct HidParserReport *report;
  
  if (device->DriverData != NULL) {
    data = (struct HidDevice*)device->DriverData;

    if (data->HidDeallocate != NULL)
      data->HidDeallocate(device);

    if (data->ParserResult != NULL) {
      for (u32 i = 0; i < data->ParserResult->ReportCount; i++) {
        if (data->ParserResult->Report[i] != NULL) {
          report = data->ParserResult->Report[i];
          if (report->ReportBuffer != NULL)
            MemoryDeallocate(report->ReportBuffer);
          for (u32 j = 0; j < report->FieldCount; j++) 
            if (!report->Fields[j].Attributes.Variable)
              MemoryDeallocate(report->Fields[j].Value.Pointer);
          MemoryDeallocate(data->ParserResult->Report[i]);
        }
      }
      MemoryDeallocate(data->ParserResult);
    }

    MemoryDeallocate(data);
  }
  device->DeviceDeallocate = NULL;
  device->DeviceDetached = NULL;
}

Result HidAttach(struct UsbDevice *device, u32 interfaceNumber) {
  struct HidDevice *data;
  struct HidDescriptor *descriptor;
  struct UsbDescriptorHeader *header;
  void* reportDescriptor = NULL;
  Result result;
  u32 currentInterface;

  if (device->Interfaces[interfaceNumber].Class != InterfaceClassHid) {
    return ErrorArgument;
  }
  if (device->Interfaces[interfaceNumber].EndpointCount < 1) {
    LOGF("HID: Invalid HID device with fewer than one endpoints (%d).\n", device->Interfaces[interfaceNumber].EndpointCount);
    return ErrorIncompatible;
  }
  if (device->Endpoints[interfaceNumber][0].EndpointAddress.Direction != In ||
    device->Endpoints[interfaceNumber][0].Attributes.Type != Interrupt) {
    LOG("HID: Invalid HID device with unusual endpoints (0).\n");
    return ErrorIncompatible;
  }
  if (device->Interfaces[interfaceNumber].EndpointCount >= 2) {
    if (device->Endpoints[interfaceNumber][1].EndpointAddress.Direction != Out ||
      device->Endpoints[interfaceNumber][1].Attributes.Type != Interrupt) {
      LOG("HID: Invalid HID device with unusual endpoints (1).\n");
      return ErrorIncompatible;
    } 
  }
  if (device->Status != Configured) {
    LOG("HID: Cannot start driver on unconfigured device!\n");
    return ErrorDevice;
  }
  if (device->Interfaces[interfaceNumber].SubClass == 1) {
    if (device->Interfaces[interfaceNumber].Protocol == 1)
      LOG_DEBUG("HID: Boot keyboard detected.\n");
    else if (device->Interfaces[interfaceNumber].Protocol == 2)
      LOG_DEBUG("HID: Boot mouse detected.\n");
    else 
      LOG_DEBUG("HID: Unknown boot device detected.\n");
    
    LOG_DEBUG("HID: Reverting from boot to normal HID mode.\n");
    if ((result = HidSetProtocol(device, interfaceNumber, 1)) != OK) {
      LOG("HID: Could not revert to report mode from HID mode.\n");
      return result;
    }
  }

  header = (struct UsbDescriptorHeader*)device->FullConfiguration;
  descriptor = NULL;
  currentInterface = interfaceNumber + 1; // Certainly different!
  do {    
    if (header->DescriptorLength == 0) break; // List end
    switch (header->DescriptorType) {
    case Interface:
      currentInterface = ((struct UsbInterfaceDescriptor*)header)->Number;
      break;
    case Hid:
      if (currentInterface == interfaceNumber)
        descriptor = (void*)header;
      break;
    default:
      break;
    }
    
    LOG_DEBUGF("HID: Descriptor %d length %d, interface %d.\n", header->DescriptorType, header->DescriptorLength, currentInterface);

    if (descriptor != NULL) break;
    header = (void*)((u8*)header + header->DescriptorLength);
  } while (true);

  if (descriptor == NULL) {
    LOGF("HID: No HID descriptor in %s.Interface%d. Cannot be a HID device.\n", UsbGetDescription(device), interfaceNumber + 1);
    return ErrorIncompatible;
  }

  if (descriptor->HidVersion > 0x111) {
    LOGF("HID: Device uses unsupported HID version %x.%x.\n", descriptor->HidVersion >> 8, descriptor->HidVersion & 0xff);
    return ErrorIncompatible;
  }
  LOG_DEBUGF("HID: Device version HID %x.%x.\n", descriptor->HidVersion >> 8, descriptor->HidVersion & 0xff);
  
  device->DeviceDeallocate = HidDeallocate;
  device->DeviceDetached = HidDetached;
  if ((device->DriverData = MemoryAllocate(sizeof (struct HidDevice))) == NULL) {
    result = ErrorMemory;
    goto deallocate;
  }
  device->DriverData->DataSize = sizeof(struct HidDevice);
  device->DriverData->DeviceDriver = DeviceDriverHid;
  data = (struct HidDevice*)device->DriverData;
  data->Descriptor = descriptor;
  data->DriverData = NULL;
  
  if ((reportDescriptor = MemoryAllocate(descriptor->OptionalDescriptors[0].Length)) == NULL) {
    result = ErrorMemory;
    goto deallocate;
  }
  if ((result = UsbGetDescriptor(device, HidReport, 0, interfaceNumber, reportDescriptor, descriptor->OptionalDescriptors[0].Length, descriptor->OptionalDescriptors[0].Length, 1)) != OK) {
    MemoryDeallocate(reportDescriptor);
    LOGF("HID: Could not read report descriptor for %s.Interface%d.\n", UsbGetDescription(device), interfaceNumber + 1);
    goto deallocate;
  }
  if ((result = HidParseReportDescriptor(device, reportDescriptor, descriptor->OptionalDescriptors[0].Length)) != OK) {   
    MemoryDeallocate(reportDescriptor);
    LOGF("HID: Invalid report descriptor for %s.Interface%d.\n", UsbGetDescription(device), interfaceNumber + 1);
    goto deallocate;
  }

  MemoryDeallocate(reportDescriptor);
  reportDescriptor = NULL;

  data->ParserResult->Interface = interfaceNumber;
  if (data->ParserResult->Application.Page == GenericDesktopControl &&
    (u16)data->ParserResult->Application.Desktop < HidUsageAttachCount &&
    HidUsageAttach[(u16)data->ParserResult->Application.Desktop] != NULL) {
    HidUsageAttach[(u16)data->ParserResult->Application.Desktop](device, interfaceNumber);
  }

  return OK;
deallocate:
  if (reportDescriptor != NULL) MemoryDeallocate(reportDescriptor);
  HidDeallocate(device);
  return result;
}

s32 HidGetFieldValue(struct HidParserField *field, u32 index) {
  return BitGetSigned(field->Value.Pointer, index * field->Size, field->Size);
}
