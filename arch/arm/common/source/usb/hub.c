/******************************************************************************
* device/hub.c
*  by <PERSON>
*
* A light weight implementation of the USB protocol stack fit for a simple
* driver.
*
* device/hub.c contains code relating to the generic USB driver's hubs. USB
* is designed such that this driver's interface would be virtually the same
* across all systems, and in fact its implementation varies little either.
******************************************************************************/
#include <device/hub.h>
#include <hcd/hcd.h>
#include <platform/platform.h>
#include <usbd/device.h>
#include <usbd/devicerequest.h>
#include <usbd/descriptors.h>
#include <usbd/pipe.h>
#include <usbd/usbd.h>

#define ControlMessageTimeout 10

void HubLoad() 
{
  LOG_DEBUG("CSUD: Hub driver version 0.1\n"); 
  InterfaceClassAttach[InterfaceClassHub] = HubAttach;
}

Result HubReadDescriptor(struct UsbDevice *device) {
  struct UsbDescriptorHeader header;
  Result result;

  if ((result = UsbGetDescriptor(device, Hub, 0, 0, &header, sizeof(header), sizeof(header), 0x20)) != OK) {
    LOGF("HUB: Failed to read hub descriptor for %s.\n", UsbGetDescription(device));
    return result;
  }
  if (((struct HubDevice*)device->DriverData)->Descriptor == NULL &&
    (((struct HubDevice*)device->DriverData)->Descriptor = MemoryAllocate(header.DescriptorLength)) == NULL) {
    LOGF("HUB: Not enough memory to read hub descriptor for %s.\n", UsbGetDescription(device));
    return ErrorMemory;
  }
  if ((result = UsbGetDescriptor(device, Hub, 0, 0, ((struct HubDevice*)device->DriverData)->Descriptor, header.DescriptorLength, header.DescriptorLength, 0x20)) != OK) {
    LOGF("HUB: Failed to read hub descriptor for %s.\n", UsbGetDescription(device));
    return result;
  }

  return OK;
}

Result HubGetStatus(struct UsbDevice *device) {
  Result result;
  
  if ((result = UsbControlMessage(
    device, 
    (struct UsbPipeAddress) { 
      .Type = Control, 
      .Speed = device->Speed, 
      .EndPoint = 0 , 
      .Device = device->Number, 
      .Direction = In,
      .MaxSize = SizeFromNumber(device->Descriptor.MaxPacketSize0),
    },
    &((struct HubDevice*)device->DriverData)->Status,
    sizeof(struct HubFullStatus),
    &(struct UsbDeviceRequest) {
      .Request = GetStatus,
      .Type = 0xa0,
      .Length = sizeof(struct HubFullStatus),
    },
    ControlMessageTimeout)) != OK) 
    return result;
  if (device->LastTransfer < sizeof(struct HubFullStatus)) {
    LOGF("HUB: Failed to read hub status for %s.\n", UsbGetDescription(device));
    return ErrorDevice;
  }
  return OK;
}

Result HubPortGetStatus(struct UsbDevice *device, u8 port) {
  Result result;
  
  if ((result = UsbControlMessage(
    device, 
    (struct UsbPipeAddress) { 
      .Type = Control, 
      .Speed = device->Speed, 
      .EndPoint = 0, 
      .Device = device->Number, 
      .Direction = In,
      .MaxSize = SizeFromNumber(device->Descriptor.MaxPacketSize0),
    },
    &((struct HubDevice*)device->DriverData)->PortStatus[port],
    sizeof(struct HubPortFullStatus),
    &(struct UsbDeviceRequest) {
      .Request = GetStatus,
      .Type = 0xa3,
      .Index = port + 1,
      .Length = sizeof(struct HubPortFullStatus),
    },
    ControlMessageTimeout)) != OK) 
    return result;
  if (device->LastTransfer < sizeof(struct HubPortFullStatus)) {
    LOGF("HUB: Failed to read hub port status for %s.Port%d.\n", UsbGetDescription(device), port + 1);
    return ErrorDevice;
  }
  return OK;
}

Result HubChangePortFeature(struct UsbDevice *device, enum HubPortFeature feature, u8 port, bool set) {
  Result result;
  
  if ((result = UsbControlMessage(
    device, 
    (struct UsbPipeAddress) { 
      .Type = Control, 
      .Speed = device->Speed, 
      .EndPoint = 0, 
      .Device = device->Number, 
      .Direction = Out,
      .MaxSize = SizeFromNumber(device->Descriptor.MaxPacketSize0),
    },
    NULL,
    0,
    &(struct UsbDeviceRequest) {
      .Request = set ? SetFeature : ClearFeature,
      .Type = 0x23,
      .Value = (u16)feature,
      .Index = port + 1,      
    },
    ControlMessageTimeout)) != OK) 
    return result;

  return OK;
}

Result HubPowerOn(struct UsbDevice *device) {
  struct HubDevice *data;
  struct HubDescriptor *hubDescriptor;
  
  data = (struct HubDevice*)device->DriverData;
  hubDescriptor = data->Descriptor;
  LOG_DEBUGF("HUB: Powering on hub %s.\n", UsbGetDescription(device));
  
  for (u32 i = 0; i < data->MaxChildren; i++) {
    if (HubChangePortFeature(device, FeaturePower, i, true) != OK)
      LOGF("HUB: Could not power %s.Port%d.\n", UsbGetDescription(device), i + 1);
  }

  MicroDelay(hubDescriptor->PowerGoodDelay * 2000);

  return OK;
}

Result HubPortReset(struct UsbDevice *device, u8 port) {
  Result result;
  struct HubDevice *data;
  struct HubPortFullStatus *portStatus;
  u32 retry, timeout;

  data = (struct HubDevice*)device->DriverData;
  portStatus = &data->PortStatus[port];

  LOG_DEBUGF("HUB: Hub reset %s.Port%d.\n", UsbGetDescription(device), port + 1);
  for (retry = 0; retry < 3; retry++) {
    if ((result = HubChangePortFeature(device, FeatureReset, port, true)) != OK) {
      LOGF("HUB: Failed to reset %s.Port%d.\n", UsbGetDescription(device), port + 1);
      return result;
    }
    timeout = 0;
    do {
      MicroDelay(20000);
      if ((result = HubPortGetStatus(device, port)) != OK) {
        LOGF("HUB: Hub failed to get status (4) for %s.Port%d.\n", UsbGetDescription(device), port + 1);
        return result;
      }     
      timeout++;
    } while (!portStatus->Change.ResetChanged && !portStatus->Status.Enabled && timeout < 10);

    if (timeout == 10)
      continue;
    
    LOG_DEBUGF("HUB: %s.Port%d Status %x:%x.\n", UsbGetDescription(device), port + 1, *(u16*)&portStatus->Status, *(u16*)&portStatus->Change);

    if (portStatus->Change.ConnectedChanged || !portStatus->Status.Connected)
      return ErrorDevice;

    if (portStatus->Status.Enabled) 
      break;
  }

  if (retry == 3) {
    LOGF("HUB: Cannot enable %s.Port%d. Please verify the hardware is working.\n", UsbGetDescription(device), port + 1);
    return ErrorDevice;
  }

  if ((result = HubChangePortFeature(device, FeatureResetChange, port, false)) != OK) {
    LOGF("HUB: Failed to clear reset on %s.Port%d.\n", UsbGetDescription(device), port + 1);
  }
  return OK;
}

Result HubPortConnectionChanged(struct UsbDevice *device, u8 port) {
  Result result;
  struct HubDevice *data;
  struct HubPortFullStatus *portStatus;

  data = (struct HubDevice*)device->DriverData;
  portStatus = &data->PortStatus[port];
  
  if ((result = HubPortGetStatus(device, port)) != OK) {
    LOGF("HUB: Hub failed to get status (2) for %s.Port%d.\n", UsbGetDescription(device), port + 1);
    return result;
  }
  LOG_DEBUGF("HUB: %s.Port%d Status %x:%x.\n", UsbGetDescription(device), port + 1, *(u16*)&portStatus->Status, *(u16*)&portStatus->Change);
  
  if ((result = HubChangePortFeature(device, FeatureConnectionChange, port, false)) != OK) {
    LOGF("HUB: Failed to clear change on %s.Port%d.\n", UsbGetDescription(device), port + 1);
  }
  
  if ((!portStatus->Status.Connected && !portStatus->Status.Enabled) || data->Children[port] != NULL) {
    LOGF("HUB: Disconnected %s.Port%d - %s.\n", UsbGetDescription(device), port + 1, UsbGetDescription(data->Children[port]));
    UsbDeallocateDevice(data->Children[port]);
    data->Children[port] = NULL;
    if (!portStatus->Status.Connected) return OK;
  }
  
  if ((result = HubPortReset(device, port)) != OK) {
    LOGF("HUB: Could not reset %s.Port%d for new device.\n", UsbGetDescription(device), port + 1);
    return result;
  }

  if ((result = UsbAllocateDevice(&data->Children[port])) != OK) {
    LOGF("HUB: Could not allocate a new device entry for %s.Port%d.\n", UsbGetDescription(device), port + 1);
    return result;
  }
  
  if ((result = HubPortGetStatus(device, port)) != OK) {
    LOGF("HUB: Hub failed to get status (3) for %s.Port%d.\n", UsbGetDescription(device), port + 1);
    return result;
  }
  
  LOG_DEBUGF("HUB: %s.Port%d Status %x:%x.\n", UsbGetDescription(device), port + 1, *(u16*)&portStatus->Status, *(u16*)&portStatus->Change);

  if (portStatus->Status.HighSpeedAttatched) data->Children[port]->Speed = High;
  else if (portStatus->Status.LowSpeedAttatched) data->Children[port]->Speed = Low;
  else data->Children[port]->Speed = Full;
  data->Children[port]->Parent = device;
  data->Children[port]->PortNumber = port;
  if ((result = UsbAttachDevice(data->Children[port])) != OK) {
    LOGF("HUB: Could not connect to new device in %s.Port%d. Disabling.\n", UsbGetDescription(device), port + 1);
    UsbDeallocateDevice(data->Children[port]);
    data->Children[port] = NULL;
    if (HubChangePortFeature(device, FeatureEnable, port, false) != OK) {
      LOGF("HUB: Failed to disable %s.Port%d.\n", UsbGetDescription(device), port + 1);
    }
    return result;
  }
  return OK;
}

void HubDetached(struct UsbDevice *device) {
  struct HubDevice *data;

  LOG_DEBUGF("HUB: %s detached.\n", UsbGetDescription(device));
  if (device->DriverData != NULL) {
    data = (struct HubDevice*)device->DriverData;
    
    for (u32 i = 0; i < data->MaxChildren; i++) {
      if (data->Children[i] != NULL &&
        data->Children[i]->DeviceDetached != NULL)
        data->Children[i]->DeviceDetached(data->Children[i]);
    }
  }
}

void HubDeallocate(struct UsbDevice *device) {
  struct HubDevice *data;
  
  LOG_DEBUGF("HUB: %s deallocate.\n", UsbGetDescription(device));
  if (device->DriverData != NULL) {
    data = (struct HubDevice*)device->DriverData;
    
    for (u32 i = 0; i < data->MaxChildren; i++) {
      if (data->Children[i] != NULL) {
        UsbDeallocateDevice(data->Children[i]);
        data->Children[i] = NULL;
      }
    }
      
    if (data->Descriptor != NULL)
      MemoryDeallocate(data->Descriptor);
    MemoryDeallocate((void*)device->DriverData);
  }
  device->DeviceDeallocate = NULL;
  device->DeviceDetached = NULL;
  device->DeviceCheckForChange = NULL;
  device->DeviceChildDetached = NULL;
  device->DeviceChildReset = NULL;
  device->DeviceCheckConnection = NULL;
}

void HubCheckForChange(struct UsbDevice *device) {
  struct HubDevice *data;
  
  data = (struct HubDevice*)device->DriverData;
  
  for (u32 i = 0; i < data->MaxChildren; i++) {
    if (HubCheckConnection(device, i) != OK)
      continue;

    if (data->Children[i] != NULL && 
      data->Children[i]->DeviceCheckForChange != NULL)
        data->Children[i]->DeviceCheckForChange(data->Children[i]);
  }
}

void HubChildDetached(struct UsbDevice *device, struct UsbDevice *child) {
  struct HubDevice *data;
  
  data = (struct HubDevice*)device->DriverData;
  
  if (child->Parent == device && child->PortNumber < data->MaxChildren &&
    data->Children[child->PortNumber] == child)
    data->Children[child->PortNumber] = NULL;
}

Result HubChildReset(struct UsbDevice *device, struct UsbDevice *child) {
  struct HubDevice *data;
  
  data = (struct HubDevice*)device->DriverData;
  
  if (child->Parent == device && child->PortNumber < data->MaxChildren &&
    data->Children[child->PortNumber] == child)
    return HubPortReset(device, child->PortNumber);
  else
    return ErrorDevice;
}

Result HubCheckConnectionDevice(struct UsbDevice *device, struct UsbDevice *child) {
  struct HubDevice *data;
  Result result;

  data = (struct HubDevice*)device->DriverData;
  
  if (child->Parent == device && child->PortNumber < data->MaxChildren &&
    data->Children[child->PortNumber] == child) {
    if ((result = HubCheckConnection(device, child->PortNumber)) != OK)
      return result;
    return data->Children[child->PortNumber] == child ? OK : ErrorDisconnected;
  }
  else
    return ErrorArgument;
}

Result HubCheckConnection(struct UsbDevice *device, u8 port) {
  Result result;
  struct HubPortFullStatus *portStatus;
  struct HubDevice *data;

  data = (struct HubDevice*)device->DriverData;

  if ((result = HubPortGetStatus(device, port)) != OK) {
    if (result != ErrorDisconnected)
      LOGF("HUB: Failed to get hub port status (1) for %s.Port%d.\n", UsbGetDescription(device), port + 1);
    return result;
  }
  portStatus = &data->PortStatus[port];
  
  if (portStatus->Change.ConnectedChanged) {
    HubPortConnectionChanged(device, port);
  }
  if (portStatus->Change.EnabledChanged) {
    if (HubChangePortFeature(device, FeatureEnableChange, port, false) != OK) {
      LOGF("HUB: Failed to clear enable change %s.Port%d.\n", UsbGetDescription(device), port + 1);
    }
  
    // This may indicate EM interference.
    if (!portStatus->Status.Enabled && portStatus->Status.Connected && data->Children[port] != NULL) {
      LOGF("HUB: %s.Port%d has been disabled, but is connected. This can be cause by interference. Reenabling!\n", UsbGetDescription(device), port + 1);
      HubPortConnectionChanged(device, port);
    }
  }
  if (portStatus->Status.Suspended) {     
    if (HubChangePortFeature(device, FeatureSuspend, port, false) != OK) {
      LOGF("HUB: Failed to clear suspended port - %s.Port%d.\n", UsbGetDescription(device), port + 1);
    }
  }
  if (portStatus->Change.OverCurrentChanged) {    
    if (HubChangePortFeature(device, FeatureOverCurrentChange, port, false) != OK) {
      LOGF("HUB: Failed to clear over current port - %s.Port%d.\n", UsbGetDescription(device), port + 1);
    }
    HubPowerOn(device);
  }
  if (portStatus->Change.ResetChanged) {
    if (HubChangePortFeature(device, FeatureResetChange, port, false) != OK) {
      LOGF("HUB: Failed to clear reset port - %s.Port%d.\n", UsbGetDescription(device), port + 1);
    }
  }

  return OK;
}

Result HubAttach(struct UsbDevice *device, u32 interfaceNumber) {
  Result result;
  struct HubDevice *data;
  struct HubDescriptor *hubDescriptor;
  struct HubFullStatus *status;
  
  if (device->Interfaces[interfaceNumber].EndpointCount != 1) {
    LOGF("HUB: Cannot enumerate hub with multiple endpoints: %d.\n", device->Interfaces[interfaceNumber].EndpointCount);
    return ErrorIncompatible;
  }
  if (device->Endpoints[interfaceNumber][0].EndpointAddress.Direction == Out) {
    LOG("HUB: Cannot enumerate hub with only one output endpoint.\n");
    return ErrorIncompatible;
  }
  if (device->Endpoints[interfaceNumber][0].Attributes.Type != Interrupt) {
    LOG("HUB: Cannot enumerate hub without interrupt endpoint.\n");
    return ErrorIncompatible;
  }

  device->DeviceDeallocate = HubDeallocate;
  device->DeviceDetached = HubDetached;
  device->DeviceCheckForChange = HubCheckForChange;
  device->DeviceChildDetached = HubChildDetached;
  device->DeviceChildReset = HubChildReset;
  device->DeviceCheckConnection = HubCheckConnectionDevice;
  if ((device->DriverData = MemoryAllocate(sizeof(struct HubDevice))) == NULL) {
    LOG("HUB: Cannot allocate hub data. Out of memory.\n");
    return ErrorMemory;
  }
  data = (struct HubDevice*)device->DriverData;
  device->DriverData->DataSize = sizeof(struct HubDevice);
  device->DriverData->DeviceDriver = DeviceDriverHub;
  for (u32 i = 0; i < MaxChildrenPerDevice; i++)
    data->Children[i] = NULL;

  if ((result = HubReadDescriptor(device)) != OK) return result;

  hubDescriptor = data->Descriptor;
  if (hubDescriptor->PortCount > MaxChildrenPerDevice) {
    LOGF("HUB: Hub %s is too big for this driver to handle. Only the first %d ports will be used. Change MaxChildrenPerDevice in usbd/device.h.\n", UsbGetDescription(device), MaxChildrenPerDevice);
    data->MaxChildren = MaxChildrenPerDevice;
  } else
    data->MaxChildren = hubDescriptor->PortCount;

  switch (hubDescriptor->Attributes.PowerSwitchingMode) {
  case Global:
    LOG_DEBUG("HUB: Hub power: Global.\n");
    break;
  case Individual:
    LOG_DEBUG("HUB: Hub power: Individual.\n");
    break;
  default:
    LOGF("HUB: Unknown hub power type %d on %s. Driver incompatible.\n", hubDescriptor->Attributes.PowerSwitchingMode, UsbGetDescription(device));
    HubDeallocate(device);
    return ErrorIncompatible;
  }
  
  if (hubDescriptor->Attributes.Compound)
    LOG_DEBUG("HUB: Hub nature: Compound.\n");
  else
    LOG_DEBUG("HUB: Hub nature: Standalone.\n");

  switch (hubDescriptor->Attributes.OverCurrentProtection) {
  case Global:
    LOG_DEBUG("HUB: Hub over current protection: Global.\n");
    break;
  case Individual:
    LOG_DEBUG("HUB: Hub over current protection: Individual.\n");
    break;
  default:
    LOGF("HUB: Unknown hub over current type %d on %s. Driver incompatible.\n", hubDescriptor->Attributes.OverCurrentProtection, UsbGetDescription(device));
    HubDeallocate(device);
    return ErrorIncompatible;
  }

  LOG_DEBUGF("HUB: Hub power to good: %dms.\n", hubDescriptor->PowerGoodDelay * 2);
  LOG_DEBUGF("HUB: Hub current required: %dmA.\n", hubDescriptor->MaximumHubPower * 2);
  LOG_DEBUGF("HUB: Hub ports: %d.\n", hubDescriptor->PortCount);
  for (u32 i = 0; i < data->MaxChildren; i++) {
    if (hubDescriptor->Data[(i + 1) >> 3] & 1 << ((i + 1) & 0x7)) 
      LOG_DEBUGF("HUB: Hub port %d is not removable.\n", i + 1);
    else
      LOG_DEBUGF("HUB: Hub port %d is removable.\n", i + 1);
  }
  
  if ((result = HubGetStatus(device)) != OK) {
    LOGF("HUB: Failed to get hub status for %s.\n", UsbGetDescription(device));
    return result;
  }
  status = &data->Status;

  if (!status->Status.LocalPower) LOG_DEBUG("USB Hub power: Good.\n");
  else LOG_DEBUG("HUB: Hub power: Lost.\n");
  if (!status->Status.OverCurrent) LOG_DEBUG("USB Hub over current condition: No.\n");
  else LOG_DEBUG("HUB: Hub over current condition: Yes.\n");

  LOG_DEBUG("HUB: Hub powering on.\n");
  if ((result = HubPowerOn(device)) != OK) {
    LOG_DEBUG("HUB: Hub failed to power on.\n");
    HubDeallocate(device);
    return result;
  }

  if ((result = HubGetStatus(device)) != OK) {
    LOGF("HUB: Failed to get hub status for %s.\n", UsbGetDescription(device));
    HubDeallocate(device);
    return result;
  }

  if (!status->Status.LocalPower) LOG_DEBUG("USB Hub power: Good.\n");
  else LOG_DEBUG("HUB: Hub power: Lost.\n");
  if (!status->Status.OverCurrent) LOG_DEBUG("USB Hub over current condition: No.\n");
  else LOG_DEBUG("HUB: Hub over current condition: Yes.\n");

  LOG_DEBUGF("HUB: %s status %x:%x.\n", UsbGetDescription(device), *(u16*)&status->Status, *(u16*)&status->Change);
  
  for (u8 port = 0; port < data->MaxChildren; port++) {
    HubCheckConnection(device, port);
  }

  return OK;
}
