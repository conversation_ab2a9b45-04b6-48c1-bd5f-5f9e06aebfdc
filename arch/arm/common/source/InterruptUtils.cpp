#include "InterruptUtils.h"

#include "ArchBoardSpecific.h"
#include "BDManager.h"
#include "KeyboardManager.h"
#include "new.h"
#include "ArchMemory.h"
#include "ArchThreads.h"
#include "ArchCommon.h"
#include "Console.h"
#include "FrameBufferConsole.h"
#include "Terminal.h"
#include "kprintf.h"
#include "Scheduler.h"
#include "debug_bochs.h"

#include "panic.h"

#include "Thread.h"
#include "ArchInterrupts.h"
#include "backtrace.h"

#include "Loader.h"
#include "Syscall.h"
#include "paging-definitions.h"
#include "PageFaultHandler.h"

extern uint32* currentStack;
extern Console* main_console;

extern ArchThreadRegisters *currentThreadRegisters;
extern Thread *currentThread;

uint32 last_address;
uint32 count;


#define FLAG_FAULT_WRITING (0x800)
#define FLAG_STATUS_MASK (0xf)
#define FLAG_TRANSLATION_PAGE (7)
#define FLAG_TRANSLATION_SECTION (5)

//TODO extern "C" void arch_pageFaultHandler();
void pageFaultHandler(uint32 address, uint32 type)
{
  if(type != ARM4_XRQ_ABRTP)
  {
    asm("mrc p15, 0, r4, c6, c0, 0\n\
         mov %[v], r4\n": [v]"=r" (address));
  }

  size_t instruction_fault = 0;
  size_t data_fault = 0;

  asm("mrc p15, 0, r4, c5, c0, 0\n\
       mov %[v], r4\n": [v]"=r" (data_fault));
  asm("mov r4, #0x0\n\
      mcr p15, 0, r4, c5, c0, 0");
  asm("mrc p15, 0, r4, c5, c0, 1\n\
       mov %[v], r4\n": [v]"=r" (instruction_fault));
  asm("mov r4, #0x0\n\
      mcr p15, 0, r4, c5, c0, 1");

  bool present, writing, fetch;
  size_t status;
  if(data_fault)
  {
    fetch = 0;
    // the writing bit is actually useless as it was introduced only in ARMv6
    writing = data_fault & FLAG_FAULT_WRITING;
    status = data_fault & FLAG_STATUS_MASK;
  }
  else
  {
    fetch = 1;
    writing = false;
    status = instruction_fault & FLAG_STATUS_MASK;
  }
  present = !((status == FLAG_TRANSLATION_PAGE) || (status == FLAG_TRANSLATION_SECTION));

  PageFaultHandler::enterPageFault(address, currentThread->switch_to_userspace_,  present, writing, fetch);
}

void timer_irq_handler()
{
  static uint32 heart_beat_value = 0;
  const char* clock = "/-\\|";
  ((FrameBufferConsole*)main_console)->consoleSetCharacter(0,0,clock[heart_beat_value],Console::GREEN);
  heart_beat_value = (heart_beat_value + 1) % 4;

  Scheduler::instance()->incTicks();
  Scheduler::instance()->schedule();
}

void arch_uart1_irq_handler()
{
  kprintfd("arch_uart1_irq_handler\n");
  while(1);
}

void arch_mouse_irq_handler()
{
  kprintfd("arch_mouse_irq_handler\n");
  while(1);
}

void arch_swi_irq_handler()
{
  assert(!ArchInterrupts::testIFSet());
  uint32 swi = *((uint32*)((uint32)currentThreadRegisters->pc - 4)) & 0xffff;

  if (swi == 0xffff) // yield
  {
    Scheduler::instance()->schedule();
  }
  else if (swi == 0x0) // syscall
  {
    currentThread->switch_to_userspace_ = 0;
    currentThreadRegisters = currentThread->kernel_registers_;
    ArchInterrupts::enableInterrupts();
    auto ret = Syscall::syscallException(currentThread->user_registers_->r[0],
                                         currentThread->user_registers_->r[1],
                                         currentThread->user_registers_->r[2],
                                         currentThread->user_registers_->r[3],
                                         currentThread->user_registers_->r[4],
                                         currentThread->user_registers_->r[5]);
    currentThread->user_registers_->r[0] = ret;
    ArchInterrupts::disableInterrupts();
    currentThread->switch_to_userspace_ = 1;
    currentThreadRegisters =  currentThread->user_registers_;
  }
  else
  {
    kprintfd("Invalid SWI: %x\n",swi);
    assert(false);
  }
}

extern "C" void switchTTBR0(uint32);

extern "C" void exceptionHandler(uint32 type)
{
  assert(!currentThread || currentThread->isStackCanaryOK());
  debug(A_INTERRUPTS, "InterruptUtils::exceptionHandler: type = %x\n", type);
  assert((currentThreadRegisters->cpsr & (0xE0)) == 0);
  if (!currentThread)
  {
    Scheduler::instance()->schedule();
  }
  else if (type == ARM4_XRQ_IRQ) {
    ArchBoardSpecific::irq_handler();
  }
  else if (type == ARM4_XRQ_SWINT) {
    arch_swi_irq_handler();
  }
  else if (type == ARM4_XRQ_ABRTP)
  {
    pageFaultHandler(currentThreadRegisters->pc, type);
  }
  else if (type == ARM4_XRQ_ABRTD)
  {
    pageFaultHandler(currentThreadRegisters->pc, type);
  }
  else {
    kprintfd("\nCPU Fault type = %x\n",type);
    ArchThreads::printThreadRegisters(currentThread,false);
    currentThread->switch_to_userspace_ = 0;
    currentThreadRegisters = currentThread->kernel_registers_;
    ArchInterrupts::enableInterrupts();
    currentThread->kill();
    for(;;);
  }
//  ArchThreads::printThreadRegisters(currentThread,false);
  assert((currentThreadRegisters->ttbr0 & 0x3FFF) == 0 && (currentThreadRegisters->ttbr0 & ~0x3FFF) != 0);
  assert((currentThreadRegisters->cpsr & 0xE0) == 0);
  assert(currentThread->switch_to_userspace_ == 0 || (currentThreadRegisters->cpsr & 0xF) == 0);
  assert(!currentThread || currentThread->isStackCanaryOK());
  switchTTBR0(currentThreadRegisters->ttbr0);
}
