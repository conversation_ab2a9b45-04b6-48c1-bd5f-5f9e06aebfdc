#include "types.h"
#include "ArchInterrupts.h"
#include "kprintf.h"
#include "kstring.h"
#include "InterruptUtils.h"
#include "ArchThreads.h"
#include "ArchBoardSpecific.h"
#include "Thread.h"

extern uint8 boot_stack[];

#define SWITCH_CPU_MODE(MODE) \
    asm("mrs r0, cpsr \n\
         bic r0, r0, #0xdf \n\
         orr r0, r0, # " MODE " \n\
         msr cpsr, r0 \n\
        ");\

#define INTERRUPT_ENTRY() \
  asm("sub lr, lr, #4"); \
  SWI_ENTRY()

#define SWI_ENTRY() \
  asm("push {r0,r1,r2,r3,r4,r5,r6,r7,r8,r9,r10,r11,r12}");\
  asm("add sp, sp, #0x34");\
  asm("mov %[v], lr" : [v]"=r" (currentThreadRegisters->pc));\
  asm("mrs r0, spsr"); \
  asm("mov %[v], r0" : [v]"=r" (currentThreadRegisters->cpsr));\
  SWITCH_CPU_MODE("0xdf");\
  asm("mov %[v], sp" : [v]"=r" (currentThreadRegisters->sp));\
  asm("mov %[v], lr" : [v]"=r" (currentThreadRegisters->lr));\
  if (!(currentThreadRegisters->cpsr & 0xf)) { asm("mov sp, %[v]" : : [v]"r" (currentThreadRegisters->sp0)); }\
  memcpy(currentThreadRegisters->r,((uint32*)boot_stack) + 0x1000 - 13,sizeof(currentThreadRegisters->r));

#define INTERRUPT_EXIT() \
  asm("mov lr, %[v]" : : [v]"r" (currentThreadRegisters->lr));\
  asm("mov sp, %[v]" : : [v]"r" (currentThreadRegisters->sp));\
  SWITCH_CPU_MODE("0xd3");\
  asm("sub sp, sp, #0x34");\
  memcpy(((uint32*)boot_stack) + 0x1000 - 13,currentThreadRegisters->r,sizeof(currentThreadRegisters->r));\
  asm("mov lr, %[v]" : : [v]"r" (currentThreadRegisters->pc));\
  asm("mov r0, %[v]" : : [v]"r" (currentThreadRegisters->cpsr));\
  asm("msr spsr, r0"); \
  asm("pop {r0,r1,r2,r3,r4,r5,r6,r7,r8,r9,r10,r11,r12}");\
  asm("movs pc, lr");

uint32 arm4_cpsrget()
{
  uint32 r;

  asm volatile ("mrs %[ps], cpsr" : [ps]"=r" (r));
  return r;
}

void arm4_cpsrset(uint32 r)
{
  asm volatile ("msr cpsr, %[ps]" : : [ps]"r" (r));
}

#define INTERRUPT_HANDLER(TYPE) \
  void __attribute__((naked)) arch_irqHandler_##TYPE() \
  {\
    INTERRUPT_ENTRY();\
    void (*handler)(uint32 type) = &exceptionHandler;\
    handler(TYPE);\
    INTERRUPT_EXIT();\
  }\
  void __attribute__((naked)) arch_irqHandler_##TYPE()

INTERRUPT_HANDLER(ARM4_XRQ_IRQ);
INTERRUPT_HANDLER(ARM4_XRQ_FIQ);
INTERRUPT_HANDLER(ARM4_XRQ_RESET);
INTERRUPT_HANDLER(ARM4_XRQ_UNDEF);
INTERRUPT_HANDLER(ARM4_XRQ_ABRTP);

void __attribute__((naked)) arch_irqHandler_ARM4_XRQ_ABRTD()
{
  INTERRUPT_ENTRY();
  currentThreadRegisters->pc -= 4;
  void (*eh)(uint32 type) = &exceptionHandler;
  eh(ARM4_XRQ_ABRTD);
  INTERRUPT_EXIT();
}
void __attribute__((naked)) arch_irqHandler_ARM4_XRQ_SWINT()
{
  SWI_ENTRY();
  void (*eh)(uint32 type) = &exceptionHandler;
  eh(ARM4_XRQ_SWINT);
  INTERRUPT_EXIT();
}

#define B_OPCODE 0xEA000000
#define INSTALL_INTERRUPT_HANDLER(TYPE,MODE) \
  ((uint32*) 0x0)[TYPE] = B_OPCODE | (((uint32) &arch_irqHandler_ ## TYPE - (8 + (4 * TYPE))) >> 2);\
  SWITCH_CPU_MODE(MODE);\
  asm("mov sp, %[v]" : : [v]"r" (((uint32*)boot_stack) + 0x1000));\
  SWITCH_CPU_MODE("0xdf");

void ArchInterrupts::initialise()
{
  INSTALL_INTERRUPT_HANDLER(ARM4_XRQ_RESET, "0xD3");
  INSTALL_INTERRUPT_HANDLER(ARM4_XRQ_UNDEF, "0xDB");
  INSTALL_INTERRUPT_HANDLER(ARM4_XRQ_SWINT, "0xD3");
  INSTALL_INTERRUPT_HANDLER(ARM4_XRQ_ABRTP, "0xD7");
  INSTALL_INTERRUPT_HANDLER(ARM4_XRQ_ABRTD, "0xD7");
  INSTALL_INTERRUPT_HANDLER(ARM4_XRQ_IRQ, "0xD2");
  INSTALL_INTERRUPT_HANDLER(ARM4_XRQ_FIQ, "0xD1");
}

void ArchInterrupts::enableTimer()
{
  ArchBoardSpecific::enableTimer();
}

void ArchInterrupts::setTimerFrequency(uint32 freq)
{
  ArchBoardSpecific::setTimerFrequency(freq);
}

void ArchInterrupts::disableTimer()
{
  ArchBoardSpecific::disableTimer();
}

void ArchInterrupts::enableKBD()
{
  ArchBoardSpecific::enableKBD();
}

void ArchInterrupts::disableKBD()
{
  ArchBoardSpecific::disableKBD();
}

void ArchInterrupts::enableInterrupts()
{
  arm4_cpsrset(arm4_cpsrget() & ~((1 << 7) | (1 << 6)));
}

bool ArchInterrupts::disableInterrupts()
{
  uint32 r = arm4_cpsrget();
  arm4_cpsrset(r | ((1 << 7) | (1 << 6)));
  return !(r & ((1 << 7) | (1 << 6)));
}

bool ArchInterrupts::testIFSet()
{
  return !(arm4_cpsrget() & ((1 << 7) | (1 << 6)));
}

void ArchInterrupts::yieldIfIFSet()
{
  if (system_state == RUNNING && currentThread && testIFSet())
  {
    ArchThreads::yield();
  }
  else
  {
    __asm__ __volatile__("nop");
  }
}

extern "C" void memory_barrier() // from https://github.com/raspberrypi/firmware/wiki/Accessing-mailboxes
{
  asm("mcr p15, 0, %[v], c8, c7, 0\n"  //      tlb flush
      "mcr p15, 0, %[v], c7, c6, 0\n"  //      Invalidate Entire Data Cache
      "mcr p15, 0, %[v], c7, c10, 0\n" //      Clean Entire Data Cache
      "mcr p15, 0, %[v], c7, c14, 0\n" //      Clean and Invalidate Entire Data Cache
      "mcr p15, 0, %[v], c7, c10, 4\n" //      Data Synchronization Barrier
      "mcr p15, 0, %[v], c7, c10, 5\n" //      Data Memory Barrier
      :
      : [v]"r"(0));
}

extern "C" void switchTTBR0(uint32 ttbr0)
{
  memory_barrier();
  asm("mcr p15, 0, %[v], c2, c0, 0" : : [v]"r" (ttbr0));
  memory_barrier();
}
