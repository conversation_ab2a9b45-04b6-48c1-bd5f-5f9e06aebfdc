#include "KeyboardManager.h"
#include "kprintf.h"
#include "Console.h"

uint32 const KeyboardManager::STANDARD_KEYMAP[KEY_MAPPING_SIZE] = STANDARD_KEYMAP_DEF;

uint32 const KeyboardManager::E0_KEYS[KEY_MAPPING_SIZE] = E0_KEYS_DEF;

uint32 const KeyboardManager::SHIFT_KEYS[KEY_MAPPING_SIZE] = SHIFT_KEYS_DEF;

uint8 const KeyboardManager::SET1_SCANCODES[KEY_MAPPING_SIZE + 1] = {127, 67, 0, 63, 61, 59, 60, 88, 100, 68, 66, 64, 62, 15, 41, 0, 101,
                                                 56, 42, 0, 29, 16,
                                                 2, 0, 102, 0, 44, 31, 30, 17, 3, 91, 103, 46, 45, 32, 18, 5, 4, 92,
                                                 104, 57, 47, 33, 20,
                                                 19, 6, 93, 105, 49, 48, 35, 34, 21, 7, 0, 106, 0, 50, 36, 22, 8, 9, 0,
                                                 107, 51, 37, 23,
                                                 24, 11, 10, 0, 108, 52, 53, 38, 39, 25, 12, 0, 109, 0, 40, 0, 26, 13,
                                                 0, 0, 58, 54, 28,
                                                 27, 0, 43, 99, 0, 0, 86, 0, 0, 0, 0, 14, 0, 0, 79, 0, 75, 71, 0, 0,
                                                 111, 82, 83, 80, 76,
                                                 77, 72, 1, 69, 87, 78, 81, 74, 55, 73, 70, 0, 128};

KeyboardManager *KeyboardManager::instance_ = 0;

extern struct KMI* kmi;

#define KMI_GOT_STUFF_TO_READ (kmi->stat & (1<<4))

KeyboardManager::KeyboardManager() :
    keyboard_buffer_(256), extended_scancode(0), keyboard_status_(0), usb_kbd_addr_(0), current_key_(0), next_is_up_(0)
{
  kmi = (struct KMI*) 0x88000000;
  kmi->cr = 0x1C;
  kmi->data = 0xF4;
  while(!KMI_GOT_STUFF_TO_READ) {}; //wait for RX of answer
  if(kmi->data != 0xFA)
    assert(false && "Keyboard did not answer with the expected ack...");
  while(KMI_GOT_STUFF_TO_READ && kmi->data == 0xFA) {}; //keyboard sends more than one ack.. synchronizing communication with that while...
  send_cmd(0xF0); //scancode set command
  send_cmd(0x02); //set scancode set to scancode set 2.. 
  while(!KMI_GOT_STUFF_TO_READ) {}; //wait for RX of answer
  if(kmi->data != 0xFA)
    assert(false && "Keyboard did not answer with the expected ack...");
  while(KMI_GOT_STUFF_TO_READ && kmi->data == 0xFA) {}; //keyboard sends more than one ack.. synchronizing communication with that while...
}

KeyboardManager::~KeyboardManager()
{
}

void KeyboardManager::kb_wait()
{
  //basically implemented like x86, but with wait for TX data reg empty flag
  //this function waits for the command buffer to be empty...
  uint32 i;

  for (i = 0; i < 0x10000; i++)
  {
    uint8 stat = kmi->stat;
    if ((stat & (1<<6)))
      break;
  }
  if (i >= 0x10000)
    kprintfd("KeyboardManager::kb_wait: waiting on TX data reg empty did not work :-( kmi->stat=%zx\n", kmi->stat);
}

void KeyboardManager::send_cmd(uint8 cmd, uint8 port __attribute__((unused)))
{
  kb_wait();
  kmi->data = cmd;
}

void KeyboardManager::serviceIRQ(void)
{
  uint8 scancode = kmi->data;
  if(scancode == 0xf0)
    next_is_up_ = 1;
  if (scancode > 0x80)
    return;

  scancode = SET1_SCANCODES[scancode];
  if(next_is_up_)
  {
    next_is_up_ = 0;
    modifyKeyboardStatus(scancode | 0x80);
    return;
  }

  if (extended_scancode == 0xE0)
  {
    if (scancode == 0x2A || scancode == 0x36 || scancode >= E0_BASE)
    {
      extended_scancode = 0;
    }

    scancode = E0_KEYS[scancode];
  }
  else if (extended_scancode == 0xE1 && scancode == 0x1D)
  {
    extended_scancode = 0x100;
  }
  else if (extended_scancode == 0x100 && scancode == 0x45)
    scancode = E1_PAUSE;

  extended_scancode = 0;

  if (scancode == 0xFF || scancode == 0xFA || scancode == 0xFE || scancode == 0x00) // non parsable codes, ACK and keyb. buffer errors
  {
    debug(A_KB_MANAGER, "Non-parsable scancode %X \n", scancode);
  }

  if (scancode == 0xE0 || scancode == 0xE1)
  {
    extended_scancode = scancode;
  }

  modifyKeyboardStatus(scancode);
  setLEDs(); // setting the leds
  if (main_console)
  {
    keyboard_buffer_.put(scancode); // put it inside the buffer
  }

}

void KeyboardManager::modifyKeyboardStatus(uint8 sc)
{
  bool key_released = sc & 0200;

  if (key_released)
    sc &= 0x7f;

  uint32 key = convertScancode(sc);

  uint32 simple_key = key & 0xFF;
  uint32 control_key = key & 0xFF00;

  if (simple_key)
    return;

  if (key_released)
  {
    if (!((control_key & KBD_META_CAPS) || (control_key & KBD_META_NUM) || (control_key & KBD_META_SCRL)))
      keyboard_status_ &= ~control_key;
  }
  else
  {
    if (control_key & KBD_META_CAPS)
    {
      keyboard_status_ ^= KBD_META_CAPS;
    }
    else if (control_key & KBD_META_NUM)
    {
      keyboard_status_ ^= KBD_META_NUM;
    }
    else if (control_key & KBD_META_SCRL)
    {
      keyboard_status_ ^= KBD_META_SCRL;
    }
    else
      keyboard_status_ |= control_key;
  }
  return;
}

void KeyboardManager::emptyKbdBuffer()
{
  while (kbdBufferFull())
    kbdGetScancode();
}

void KeyboardManager::setLEDs(void)
{
  static uint32 last_leds = 0;
  uint32 leds = 0;
  if (keyboard_status_ & KBD_META_SCRL)
    leds |= 1;
  if (keyboard_status_ & KBD_META_NUM)
    leds |= 2;
  if (keyboard_status_ & KBD_META_CAPS)
    leds |= 4;
  if (last_leds != leds)
  {
    send_cmd(0xF4); // enable keyboard command
    send_cmd(0xED); // "set LEDs" command
    send_cmd(leds); // bottom 3 bits set LEDs
    last_leds = leds;
  }
}

uint32 KeyboardManager::convertScancode(uint8 scancode)
{
  if (isShift() ^ isCaps())
    return SHIFT_KEYS[scancode];

  return STANDARD_KEYMAP[scancode];
}
