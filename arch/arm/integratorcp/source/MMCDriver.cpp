#include "BDManager.h"
#include "BDRequest.h"
#include "MMCDriver.h"
#include "ArchInterrupts.h"

#include "Scheduler.h"
#include "kprintf.h"

#define TIMEOUT_WARNING() do { kprintfd("%s:%d: timeout. THIS MIGHT CAUSE SERIOUS TROUBLE!\n", __PRETTY_FUNCTION__, __LINE__); } while (0)

struct MMCI
{
    uint32 power;
    uint32 clock;
    uint32 argument;
    uint32 command;
    uint32 respcmd;
    uint32 response0;
    uint32 response1;
    uint32 response2;
    uint32 response3;
    uint32 datatimer;
    uint32 datalength;
    uint32 datactrl;
    uint32 datacnt;
    uint32 status;
    uint32 clear;
    uint32 mask0;
    uint32 mask1;
    uint32 reserved;
    uint32 fifo_cnt;
};

struct MMCI* mmci = (struct MMCI*) 0x8C000000;
uint32* mmci_fifo = (uint32*) 0x8C000080;

#define PL181_CMD_INDEX     0x3f
#define PL181_CMD_RESPONSE  (1 << 6)
#define PL181_CMD_LONGRESP  (1 << 7)
#define PL181_CMD_INTERRUPT (1 << 8)
#define PL181_CMD_PENDING   (1 << 9)
#define PL181_CMD_ENABLE    (1 << 10)

#define PL181_DATA_ENABLE             (1 << 0)
#define PL181_DATA_DIRECTION          (1 << 1)
#define PL181_DATA_MODE               (1 << 2)
#define PL181_DATA_DMAENABLE          (1 << 3)

#define PL181_STATUS_CMDCRCFAIL       (1 << 0)
#define PL181_STATUS_DATACRCFAIL      (1 << 1)
#define PL181_STATUS_CMDTIMEOUT       (1 << 2)
#define PL181_STATUS_DATATIMEOUT      (1 << 3)
#define PL181_STATUS_TXUNDERRUN       (1 << 4)
#define PL181_STATUS_RXOVERRUN        (1 << 5)
#define PL181_STATUS_CMDRESPEND       (1 << 6)
#define PL181_STATUS_CMDSENT          (1 << 7)
#define PL181_STATUS_DATAEND          (1 << 8)
#define PL181_STATUS_DATABLOCKEND     (1 << 10)
#define PL181_STATUS_CMDACTIVE        (1 << 11)
#define PL181_STATUS_TXACTIVE         (1 << 12)
#define PL181_STATUS_RXACTIVE         (1 << 13)
#define PL181_STATUS_TXFIFOHALFEMPTY  (1 << 14)
#define PL181_STATUS_RXFIFOHALFFULL   (1 << 15)
#define PL181_STATUS_TXFIFOFULL       (1 << 16)
#define PL181_STATUS_RXFIFOFULL       (1 << 17)
#define PL181_STATUS_TXFIFOEMPTY      (1 << 18)
#define PL181_STATUS_RXFIFOEMPTY      (1 << 19)
#define PL181_STATUS_TXDATAAVLBL      (1 << 20)
#define PL181_STATUS_RXDATAAVLBL      (1 << 21)

uint32 mmc_send_cmd(uint32 command, uint32 arg, uint32* response)
{
  mmci->argument = arg;
  if (response)
    mmci->command = command | PL181_CMD_ENABLE | PL181_CMD_RESPONSE;
  else
    mmci->command = command | PL181_CMD_ENABLE;
  if (response)
    *response = mmci->response0;
  mmci->clear = mmci->status & 0x1FF;
  return mmci->status;
}

uint32 mmc_send_acmd(uint32 command, uint32 arg, uint32* response)
{
  uint32 first_response;
  mmc_send_cmd(55, 0, &first_response);
  return mmc_send_cmd(command, arg, response);
}

MMCDriver::MMCDriver() :
    SPT(63), lock_("MMCDriver::lock_"), rca_(0), sector_size_(512), num_sectors_(0)
{
  debug(MMC_DRIVER, "MMCDriver()\n");
  uint32 response;
  // protocol from sd card specification
  mmc_send_cmd(0, 0, 0); // go to idle state
  mmc_send_acmd(41, 0xffff00ff, &response); // get ocr register 01 101001 0 0 0 1 000 0 1111111111
  assert(response == 0x80ffff00);
  mmc_send_cmd(2, 0, 0);
  mmc_send_cmd(3, 0, &response);
  rca_ = response >> 16;
  mmc_send_cmd(4, 0, 0);
  mmc_send_cmd(7, rca_ << 16, 0);
}

MMCDriver::~MMCDriver()
{

}

uint32 MMCDriver::addRequest(BDRequest * br)
{
  ScopeLock lock(lock_);
  debug(MMC_DRIVER, "addRequest %d!\n", br->getCmd());

  int32 res = -1;

  switch (br->getCmd())
  {
    case BDRequest::BD_READ:
      res = readSector(br->getStartBlock(), br->getNumBlocks(), br->getBuffer());
      break;
    case BDRequest::BD_WRITE:
      res = writeSector(br->getStartBlock(), br->getNumBlocks(), br->getBuffer());
      break;
    default:
      res = -1;
      break;
  }

  debug(MMC_DRIVER, "addRequest:No IRQ operation !!\n");
  br->setStatus(BDRequest::BD_DONE);
  return res;
}

int32 MMCDriver::readBlock(uint32 address, void *buffer)
{
  debug(MMC_DRIVER, "readBlock: address: %x, buffer: %p\n", address, buffer);
  uint32 response;
  mmc_send_cmd(17, address, &response);
  mmci->datalength = 512;
  mmci->datactrl = PL181_DATA_ENABLE | PL181_DATA_DIRECTION | PL181_DATA_MODE;
  for (uint32 j = 0; j < 8; j++)
  {
    while (mmci->status & PL181_STATUS_RXFIFOFULL)
    {
      uint32 i;
      for (i = 0; i < 16; i++)
      {
        *((uint32*) buffer + j * 16 + i) = mmci_fifo[i];
      }
    }
  }
  return 0;
}

int32 MMCDriver::readSector(uint32 start_sector, uint32 num_sectors, void *buffer)
{
  debug(MMC_DRIVER, "readSector: start: %x, num: %x, buffer: %p\n", start_sector, num_sectors, buffer);
  for (uint32 i = 0; i < num_sectors; ++i)
  {
    readBlock((start_sector + i) * sector_size_, (char*) buffer + i * sector_size_);
  }
  return 0;
}

int32 MMCDriver::writeBlock(uint32 address, void *buffer)
{
  debug(MMC_DRIVER, "writeBlock: address: %x, buffer: %p\n", address, buffer);
  uint32 response;
  mmc_send_cmd(24, address, &response);
  mmci->datalength = 512;
  mmci->datactrl = PL181_DATA_ENABLE | PL181_DATA_MODE;
  for (uint32 j = 0; j < 8; j++)
  {
    while (!(mmci->status & PL181_STATUS_TXFIFOEMPTY));
    uint32 i;
    for (i = 0; i < 16; i++)
    {
      mmci_fifo[i] = *((uint32 *) buffer + j * 16 + i);
    }
  }
  return 0;
}

int32 MMCDriver::writeSector(uint32 start_sector, uint32 num_sectors, void * buffer)
{
  debug(MMC_DRIVER, "writeSector: start: %x, num: %x, buffer: %p\n", start_sector, num_sectors, buffer);
  for (uint32 i = 0; i < num_sectors; ++i)
  {
    writeBlock((start_sector + i) * sector_size_, (char*) buffer + i * sector_size_);
  }
  return 0;
}

uint32 MMCDriver::getNumSectors()
{
  return 210672; // fixed number of sectors for now
}

uint32 MMCDriver::getSectorSize()
{
  return sector_size_;
}

void MMCDriver::serviceIRQ()
{
}
