#include "ArchBoardSpecific.h"

#include "KeyboardManager.h"
#include "board_constants.h"
#include "InterruptUtils.h"
#include "ArchCommon.h"
#include "assert.h"
#include "offsets.h"
#include "ArchInterrupts.h"
#include "Scheduler.h"
#include "FrameBufferConsole.h"
#include "kprintf.h"

#define PHYSICAL_MEMORY_AVAILABLE 8*1024*1024

pointer ArchBoardSpecific::getVESAConsoleLFBPtr()
{
  return ((PHYSICAL_MEMORY_AVAILABLE - ArchCommon::getVESAConsoleWidth() * ArchCommon::getVESAConsoleHeight() * ArchCommon::getVESAConsoleBitsPerPixel() / 8) & ~0xFFF);
}

uint32 ArchBoardSpecific::getUsableMemoryRegion(uint32 region __attribute__((unused)), pointer &start_address, pointer &end_address, uint32 &type)
{
  start_address = 0;
  end_address = ArchCommon::getVESAConsoleLFBPtr(0);
  type = 1;
  return 0;
}


extern "C" void memory_barrier();

void ArchBoardSpecific::frameBufferInit()
{
  // frame buffer initialization code from http://wiki.osdev.org/ARM_Integrator-CP_PL110_Dirty
  typedef struct _PL110MMIO
  {
      uint32 volatile tim0; //0
      uint32 volatile tim1; //4
      uint32 volatile tim2; //8
      uint32 volatile d; //c
      uint32 volatile upbase; //10
      uint32 volatile f; //14
      uint32 volatile g; //18
      uint32 volatile control; //1c
  } PL110MMIO;

  PL110MMIO *plio;

  plio = (PL110MMIO*) 0x90000000;

  /* 640x480 pixels */
  plio->tim0 = 0x3f1f3f9c;
  plio->tim1 = 0x080b61df;
  plio->upbase = getVESAConsoleLFBPtr();
  /* 16-bit color */
  plio->control = 0x1929; // 1 1000 0010 1001
}

void ArchBoardSpecific::onIdle()
{
}

void ArchBoardSpecific::enableTimer()
{
  uint32* picmmio = (uint32*)0x84000000;
  picmmio[PIC_IRQ_ENABLESET] = (1<<5);

  uint32* t0mmio = (uint32*)0x83000000;
  t0mmio[REG_LOAD] = 0x2fffff;
  t0mmio[REG_BGLOAD] = 0x2fffff;
  t0mmio[REG_CTRL] = CTRL_ENABLE | CTRL_MODE_PERIODIC | CTRL_DIV_NONE | CTRL_SIZE_32 | CTRL_INT_ENABLE;
  t0mmio[REG_INTCLR] = ~0;    /* make sure interrupt is clear (might not be mandatory) */
}

void ArchBoardSpecific::setTimerFrequency(uint32 freq)
{
  (void)freq;
  debug(A_BOOT, "Sorry, setTimerFrequency not implemented!\n");
}

void ArchBoardSpecific::disableTimer()
{
  uint32* t0mmio = (uint32*)0x83000000;
  t0mmio[REG_CTRL] = 0;
}

extern struct KMI* kmi;

void ArchBoardSpecific::enableKBD()
{
  uint32* picmmio = (uint32*)0x84000000;
  picmmio[PIC_IRQ_ENABLESET] = (1<<3);

  kmi = (struct KMI*)0x88000000;
  kmi->cr = 0x14;
  kmi->data = 0xF4;
  while(!(kmi->stat & 0x10));
}

void ArchBoardSpecific::disableKBD()
{
  kmi->cr = 0x0;
}

void ArchBoardSpecific::keyboard_irq_handler()
{
  extern struct KMI* kmi;
  while (kmi->stat & 0x10)
  {
    KeyboardManager::instance()->serviceIRQ();
  }
}

extern void timer_irq_handler();

void ArchBoardSpecific::timer0_irq_handler()
{
  uint32 *t0mmio = (uint32*)0x83000000;
  if ((t0mmio[REG_INTSTAT] & 0x1) != 0)
  {
    assert(!ArchInterrupts::testIFSet());
    t0mmio[REG_INTCLR] = 1;     /* according to the docs u can write any value */

    timer_irq_handler();
  }
}

void ArchBoardSpecific::uart0_irq_handler()
{
  kprintfd("arch_uart0_irq_handler\n");
  while(1);
}

extern void arch_swi_irq_handler();
extern void arch_uart1_irq_handler();
extern void arch_mouse_irq_handler();

#define IRQ(X) ((*pic) & (1 << X))
void ArchBoardSpecific::irq_handler()
{
  uint32* pic = (uint32*)PIC_BASE;
  if (IRQ(0))
    arch_swi_irq_handler();
  if (IRQ(1))
    uart0_irq_handler();
  if (IRQ(2))
    arch_uart1_irq_handler();
  if (IRQ(3))
    keyboard_irq_handler();
  if (IRQ(4))
    arch_mouse_irq_handler();
  if (IRQ(5))
    timer0_irq_handler();
  // 6-10 and 22-28 not implemented
}


extern "C" void ArchBoardSpecific::disableMulticore(uint32* spin) {
  (void)spin;
}
