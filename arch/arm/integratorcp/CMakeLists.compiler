#toolchains
INCLUDE(CMakeForceCompiler)

# this one is important
SET(CMAKE_SYSTEM_NAME Generic)
SET(CMAKE_TRY_COMPILE_TARGET_TYPE STATIC_LIBRARY)

# which compilers to use for C and C++
#
find_program(CMAKE_ASM_COMPILER arm-none-eabi-gcc)
find_program(CMAKE_C_COMPILER arm-none-eabi-gcc)
find_program(CMAKE_CXX_COMPILER arm-none-eabi-g++)

find_program(LD_EXECUTABLE arm-none-eabi-gcc)
find_program(OBJCOPY_EXECUTABLE arm-none-eabi-objcopy)

if(${CMAKE_VERSION} VERSION_LESS "3.6.0") 
    CMAKE_FORCE_CXX_COMPILER(${CMAKE_CXX_COMPILER} STATIC_LIBRARY)	
    CMAKE_FORCE_C_COMPILER(${CMAKE_C_COMPILER} STATIC_LIBRARY)
endif()
