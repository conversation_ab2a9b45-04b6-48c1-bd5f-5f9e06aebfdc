set(KERNEL_BINARY kernel.x)

set(ARCH_ARM_ICP_KERNEL_CFLAGS -O0 -gstabs2 -Wall -Wextra -Werror -Wno-error=format -nostdinc -nostdlib -nostartfiles -nodefaultlibs -fno-builtin -fno-exceptions -fno-stack-protector -ffreestanding -mapcs -marm -march=armv5te -Wno-strict-aliasing -fshort-wchar ${NOPICFLAG})

set(KERNEL_CMAKE_CXX_FLAGS ${CMAKE_CXX_FLAGS} -std=gnu++17 -Wno-nonnull-compare -nostdinc++ -fno-rtti ${ARCH_ARM_ICP_KERNEL_CFLAGS})
set(KERNEL_CMAKE_C_FLAGS   ${CMAKE_C_FLAGS}   -std=gnu11 ${ARCH_ARM_ICP_KERNEL_CFLAGS})

set(ARCH_LD_ARGUMENTS -Wl,--build-id=none -Wl,-z,max-page-size=0x1000 -nostdinc -nostdlib -nodefaultlibs)
set(KERNEL_LD_ARGUMENT ${ARCH_LD_ARGUMENTS} ${NOPIEFLAG})
set(ARCH_APPEND_LD_ARGUMENTS -lgcc)


MACRO(ARCH2OBJ ARCHOBJ_LIBNAME LIBRARY_NAME)
ENDMACRO(ARCH2OBJ)


set(KERNEL_IMAGE_OBJCOPY )

# qemu: Run qemu in non debugging mode
add_custom_target(qemu
	COMMAND qemu-system-arm -M integratorcp -m 8M -kernel kernel.x -serial stdio -sd ${HDD_IMAGE} -no-reboot
	COMMENT "Executing `qemu-system-arm -M integratorcp -m 8M -kernel kernel.x -serial stdio -sd ${HDD_IMAGE} -no-reboot`"
	WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
	COMMAND reset -I
	)

# qemugdb: Run qemu in debugging mode
add_custom_target(qemugdb
    COMMAND qemu-system-arm -M integratorcp -s -S -m 8M -kernel kernel.x -serial stdio -sd ${HDD_IMAGE} -no-reboot
    COMMENT "Executing `qemu-system-arm -M integratorcp -s -S -m 8M -kernel kernel.x -serial stdio -sd ${HDD_IMAGE} -no-reboot`"
	WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
	COMMAND reset -I
	)

