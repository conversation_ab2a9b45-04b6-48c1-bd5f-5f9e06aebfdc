#pragma once

#include "types.h"

#define PAGE_MAP_LEVEL_4_ENTRIES 512
#define PAGE_DIR_POINTER_TABLE_ENTRIES 512
#define PAGE_DIR_ENTRIES 512
#define PAGE_TABLE_ENTRIES 512
#define PAGE_SIZE 4096

#define PAGE_INDEX_OFFSET_BITS 12

enum PageType {PDPT, PD, PT, PAGE};

// Constants for page fault handling
#define ERROR_MASK            0x00000007
#define PAGE_FAULT_USER       0x00000004
#define PAGE_FAULT_WRITEABLE  0x00000002
#define PAGE_FAULT_PRESENT    0x00000001

typedef struct
{
  uint64 present                   :1;
  uint64 writeable                 :1;
  uint64 user_access               :1;
  uint64 write_through             :1;
  uint64 cache_disabled            :1;
  uint64 accessed                  :1;
  uint64 ignored_3                 :1;
  uint64 size                      :1; // must be 0
  uint64 ignored_2                 :4;
  uint64 page_ppn                  :28;
  uint64 reserved_1                :12; // must be 0
  uint64 ignored_1                 :11;
  uint64 execution_disabled        :1;
} __attribute__((__packed__)) PageMapLevel4Entry;

static_assert(sizeof(PageMapLevel4Entry) == 8, "PageMapLevel4Entry is not 64 bit");

struct PageDirPointerTablePageDirEntry
{
  uint64 present                   :1;
  uint64 writeable                 :1;
  uint64 user_access               :1;
  uint64 write_through             :1;
  uint64 cache_disabled            :1;
  uint64 accessed                  :1;
  uint64 ignored_3                 :1;
  uint64 size                      :1; // 0 means page directory mapped
  uint64 ignored_2                 :4;
  uint64 page_ppn                  :28;
  uint64 reserved_1                :12; // must be 0
  uint64 ignored_1                 :11;
  uint64 execution_disabled        :1;
} __attribute__((__packed__));

static_assert(sizeof(PageDirPointerTablePageDirEntry) == 8, "PageDirPointerTablePageDirEntry is not 64 bit");

struct PageDirPointerTablePageEntry
{
  uint64 present                   :1;
  uint64 writeable                 :1;
  uint64 user_access               :1;
  uint64 write_through             :1;
  uint64 cache_disabled            :1;
  uint64 accessed                  :1;
  uint64 dirty                     :1;
  uint64 size                      :1; // 1 means 1gb page mapped
  uint64 global                    :1;
  uint64 ignored_2                 :3;
  uint64 pat                       :1;
  uint64 reserved_2                :17; // must be 0
  uint64 page_ppn                  :10;
  uint64 reserved_1                :12; // must be 0
  uint64 ignored_1                 :11;
  uint64 execution_disabled        :1;
} __attribute__((__packed__));

static_assert(sizeof(PageDirPointerTablePageEntry) == 8, "PageDirPointerTablePageEntry is not 64 bit");

typedef union
{
  struct PageDirPointerTablePageDirEntry pd;
  struct PageDirPointerTablePageEntry page;
} __attribute__((__packed__)) PageDirPointerTableEntry;

struct PageDirPageTableEntry
{
  uint64 present                   :1;
  uint64 writeable                 :1;
  uint64 user_access               :1;
  uint64 write_through             :1;
  uint64 cache_disabled            :1;
  uint64 accessed                  :1;
  uint64 ignored_3                 :1;
  uint64 size                      :1; // 0 means page table mapped
  uint64 ignored_2                 :4;
  uint64 page_ppn                  :28;
  uint64 reserved_1                :12; // must be 0
  uint64 ignored_1                 :11;
  uint64 execution_disabled        :1;
} __attribute__((__packed__));

static_assert(sizeof(PageDirPageTableEntry) == 8, "PageDirPageTableEntry is not 64 bit");

struct PageDirPageEntry
{
  uint64 present                   :1;
  uint64 writeable                 :1;
  uint64 user_access               :1;
  uint64 write_through             :1;
  uint64 cache_disabled            :1;
  uint64 accessed                  :1;
  uint64 dirty                     :1;
  uint64 size                      :1;  // 1 means 2mb page mapped
  uint64 global                    :1;
  uint64 ignored_2                 :3;
  uint64 pat                       :1;
  uint64 reserved_2                :8; // must be 0
  uint64 page_ppn                  :19;
  uint64 reserved_1                :12; // must be 0
  uint64 ignored_1                 :11;
  uint64 execution_disabled        :1;
} __attribute__((__packed__));

static_assert(sizeof(PageDirPageEntry) == 8, "PageDirPageEntry is not 64 bit");

typedef union
{
  struct PageDirPageTableEntry pt;
  struct PageDirPageEntry page;
} __attribute__((__packed__)) PageDirEntry;

typedef struct
{
  uint64 present                   :1;
  uint64 writeable                 :1;
  uint64 user_access               :1;
  uint64 write_through             :1;
  uint64 cache_disabled            :1;
  uint64 accessed                  :1;
  uint64 dirty                     :1;
  uint64 size                      :1;
  uint64 global                    :1;
  uint64 cow                       :1;
  uint64 swapped                   :1;
  uint64 shm                       :1;
  uint64 page_ppn                  :28;
  uint64 reserved_1                :12; // must be 0
  uint64 ignored_1                 :10;
  uint64 stored                    :1;
  uint64 execution_disabled        :1;
} __attribute__((__packed__)) PageTableEntry;

static_assert(sizeof(PageTableEntry) == 8, "PageTableEntry is not 64 bit");
