#pragma once

#include "types.h"
#include "offsets.h"
#include "paging-definitions.h"
#include "Mutex.h"
#include "UserProcess.h"
#include "uqueue.h"

struct ArchMemoryMapping
{
  PageMapLevel4Entry *pml4;
  PageDirPointerTableEntry *pdpt;
  PageDirEntry *pd;
  PageTableEntry *pt;
  pointer page;

  uint64 pml4_ppn;
  uint64 pdpt_ppn;
  uint64 pd_ppn;
  uint64 pt_ppn;
  uint64 page_ppn;

  uint64 page_size;

  uint64 pml4i;
  uint64 pdpti;
  uint64 pdi;
  uint64 pti;
};

class ArchMemory
{
public:
  ArchMemory();
  ~ArchMemory();

  uint64 page_map_level_4_;
  static constexpr size_t RESERVED_START = 0xFFFFFFFF80000ULL;
  static constexpr size_t RESERVED_END = 0xFFFFFFFFC0000ULL;

  /**
   * ALREADY HAS IPT AND ARCHMEMORY LOCK IN IT!!!!!
   * Maps a virtual page to a physical page and creates the upper paging-hierarchy tables on demand.
   * @param virtual_page The virtual page to map
   * @param physical_page The physical page to which the virtual page shall be mapped
   * @param user_access PTE flag indicating whether the virtual page shall be accessible by threads in user-mode
   * @return True if successful, false otherwise (the PT entry already exists)
   */
  [[nodiscard]] bool mapPage(uint64 virtual_page, ustl::queue<size_t>& ppns, size_t physical_page, uint64 user_access);

  /**
   * Removes the mapping to a virtual_page by marking its PTE entry as non-valid and frees the underlying physical page.
   * Potentially de-allocates the upper paging-hierarchy tables, depending on their occupancy.
   * @param virtual_page The virtual page which shall be unmapped
   * @return Currently always returns true
   */
  bool unmapPage(uint64 virtual_page);

  /**
   * Takes a physical page number (PPN) and returns a virtual address that can be used to access given physical page.
   * https://wiki.osdev.org/Identity_Paging
   * @param ppn The physical page number
   * @param page_size Optional, defaults to 4k pages, but you need to set it to
   * 512 * 4096 or 512 * 512 * 4096 if you have a 2M or 1G page number. Not used in baseline SWEB.
   * @return Virtual Address above KERNEL_START pointing to the start of a memory segment that
   * acts as a 1:1 mapping to the given physical page
   */
  static pointer getIdentAddressOfPPN(uint64 ppn, uint32 page_size = PAGE_SIZE)
  {
    return 0xFFFFF00000000000ULL | (ppn * page_size);
  }

  /**
   * Checks whether the given virtual address is valid and mapped to physical memory.
   * @param vaddress_to_check The virtual address to check
   * @return True if mapping exists, false otherwise (accessing it would result in a pagefault)
   */
  pointer checkAddressValid(uint64 vaddress_to_check);

  /**
   * Fills out an ArchMemoryMapping object by translating the given
   * virtual page and collecting infos from the page tables along the way.
   * @param pml4 The pml4 of the ArchMemory object for which to translate the vpage
   * @param vpage The virtual page to resolve
   * @return Returns a completely or partially filled out ArchMemoryMapping object
   */
  static const ArchMemoryMapping resolveMapping(uint64 pml4, uint64 vpage);

  const ArchMemoryMapping resolveMapping(uint64 vpage);

  static uint64 get_PPN_Of_VPN_In_KernelMapping(uint64 virtual_page,
                                                uint64 *physical_page, uint64 *physical_pte_page = nullptr);

  static void mapKernelPage(uint64 virtual_page, uint64 physical_page);

  static void unmapKernelPage(uint64 virtual_page);

  static PageMapLevel4Entry *getRootOfKernelPagingStructure();

  /**
   * flush TLB
   */
  static void flushTlb();

  void setPid(int pid);

  size_t determineNeededPages(ArchMemory const &src);
  int determineNeededPagesAllocPpn(size_t vpn);

  ArchMemory(ArchMemory const &src, UserProcess *process);
  ArchMemory &operator=(ArchMemory const &src) = delete;
  mutable Mutex archmemory_lock_;

private:
  /**
   * Adds a PML4Entry, PDPTEntry, PDEntry or PTEntry to the given PML4, PDPT, PD or PT respectively.
   * (In other words, adds the reference to a new page table to a given page directory, for example.)
   *
   * @param map_ptr identity address of the physical page containing the target page table.
   * @param index Index of the PML4Entry, PDPTEntry, PDEntry or PTEntry to be added.
   * @param ppn physical page number of the new PDPT, PD, PT or page.
   * @param bzero if true, the content of the newly inserted physical page is set to 0
   * @param size if true, a 1G or 2M page is inserted, otherwise a PD or PT respectively.
   * @param user_access if true, threads in user mode may access the inserted page. Otherwise, a kernel page is mapped
   * @param writable if true, the inserted pages are writable
   */
  template <typename T>
  static void insert(pointer map_ptr, uint64 index, uint64 ppn, uint64 bzero, uint64 size, uint64 user_access, uint64 writeable);

  /**
   * Removes a PML4Entry, PDPTEntry, PDEntry or PTEntry from a given PML4, PDPT, PD or PT if it is present
   * in the first place. This is done by setting the entry to present = 0 and clearing all other bits.
   *
   * @param map_ptr identity address of the physical page containing the target page table.
   * @param index Index of the PML4Entry, PDPTEntry, PDEntry or PTEntry to be removed.
   * @return True if the table map_ptr is full of zeroes and thus able to be freed.
   */
  template <typename T>
  static bool checkAndRemove(pointer map_ptr, uint64 index);

  int pid_;

  // ustl::map<size_t, uint64> pdpt_allocced;
  // ustl::map<size_t, uint64> pd_allocced;
  // ustl::map<size_t, uint64> pt_allocced;
  ustl::vector<uint64> pages_allocced;
  //size_t total_pages_to_allocate = 0;
};
