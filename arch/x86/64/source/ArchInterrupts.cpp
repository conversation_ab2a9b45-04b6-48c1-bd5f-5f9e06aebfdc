#include "ArchInterrupts.h"
#include "8259.h"
#include "ports.h"
#include "InterruptUtils.h"
#include "ArchThreads.h"
#include "assert.h"
#include "Thread.h"

void ArchInterrupts::initialise()
{
  uint16 i;
  disableInterrupts();
  initialise8259s();
  InterruptUtils::initialise();
  for (i=0;i<16;++i)
    disableIRQ(i);
}

void ArchInterrupts::enableTimer()
{
  enableIRQ(0);
}

void ArchInterrupts::setTimerFrequency(uint32 freq) {
  uint16_t divisor;
  if(freq < (uint32)(1193180. / (1 << 16) + 1)) {
    divisor = 0;
  } else {
    divisor = (uint16)(1193180 / freq);
  }
  outportb(0x43, 0x36);
  outportb(0x40, divisor & 0xFF);
  outportb(0x40, divisor >> 8);
}

void ArchInterrupts::disableTimer()
{
  disableIRQ(0);
}

void ArchInterrupts::enableKBD()
{
  enableIRQ(1);
  enableIRQ(9);
}

void ArchInterrupts::disableKBD()
{
  disableIRQ(1);
}

void ArchInterrupts::EndOfInterrupt(uint16 number) 
{
  sendEOI(number);
}

void ArchInterrupts::enableInterrupts()
{
   asm("sti");
}

bool ArchInterrupts::disableInterrupts()
{
  uint64 ret_val;
  asm("pushfq\n"
      "popq %0\n"
      "cli"
      : "=a"(ret_val));
  return (ret_val & (1 << 9));  //testing IF Flag
}

bool ArchInterrupts::testIFSet()
{
  uint64 ret_val;

  asm("pushfq\n"
      "popq %0\n"
      : "=a"(ret_val));
  return (ret_val & (1 << 9));  //testing IF Flag
}

void ArchInterrupts::yieldIfIFSet()
{
  if (system_state == RUNNING && currentThread && testIFSet())
  {
    ArchThreads::yield();
  }
  else
  {
    asm("nop");
  }
}


struct context_switch_registers {
  uint64 ds;
  uint64 es;
  uint64 r15;
  uint64 r14;
  uint64 r13;
  uint64 r12;
  uint64 r11;
  uint64 r10;
  uint64 r9;
  uint64 r8;
  uint64 rdi;
  uint64 rsi;
  uint64 rbp;
  uint64 rbx;
  uint64 rdx;
  uint64 rcx;
  uint64 rax;
  uint64 rsp;
};

struct interrupt_registers {
  uint64 rip;
  uint64 cs;
  uint64 rflags;
  uint64 rsp;
  uint64 ss;
};

#include "kprintf.h"

extern "C" void arch_saveThreadRegisters(uint64* base, uint64 error)
{
  struct context_switch_registers* registers;
  registers = (struct context_switch_registers*) base;
  struct interrupt_registers* iregisters;
  iregisters = (struct interrupt_registers*) (base + sizeof(struct context_switch_registers)/sizeof(uint64) + error);
  ArchThreadRegisters* info = currentThreadRegisters;
  asm("fnsave %[fpu]\n"
      "frstor %[fpu]\n"
      :
      : [fpu]"m"((info->fpu))
      :);
  info->rsp = iregisters->rsp;
  info->rip = iregisters->rip;
  info->cs = iregisters->cs;
  info->rflags = iregisters->rflags;
  info->es = registers->es;
  info->ds = registers->ds;
  info->r15 = registers->r15;
  info->r14 = registers->r14;
  info->r13 = registers->r13;
  info->r12 = registers->r12;
  info->r11 = registers->r11;
  info->r10 = registers->r10;
  info->r9 = registers->r9;
  info->r8 = registers->r8;
  info->rdi = registers->rdi;
  info->rsi = registers->rsi;
  info->rbx = registers->rbx;
  info->rdx = registers->rdx;
  info->rcx = registers->rcx;
  info->rax = registers->rax;
  info->rbp = registers->rbp;
  assert(!currentThread || currentThread->isStackCanaryOK());
}

typedef struct {
    uint32 padding;
    uint64 rsp0; // actually the TSS has more fields, but we don't need them
} __attribute__((__packed__))TSS;

extern TSS g_tss;

extern "C" void arch_contextSwitch()
{
  if(outstanding_EOIs)
  {
          debug(A_INTERRUPTS, "%zu outstanding End-Of-Interrupt signal(s) on context switch. Probably called yield in the wrong place (e.g. in the scheduler)\n", outstanding_EOIs);
          assert(!outstanding_EOIs);
  }
  if (currentThread->switch_to_userspace_)
  {
    assert(currentThread->holding_lock_list_ == 0 && "Never switch to userspace when holding a lock! Never!");
    assert(currentThread->lock_waiting_on_ == 0 && "How did you even manage to execute code while waiting for a lock?");
  }
  assert(currentThread->isStackCanaryOK() && "Kernel stack corruption detected.");
  ArchThreadRegisters info = *currentThreadRegisters; // optimization: local copy produces more efficient code in this case
  g_tss.rsp0 = info.rsp0;

  asm volatile("frstor %[fpu]\n"
      "mov %[cr3], %%cr3\n"
      "push %[ss]\n"
      "push %[rsp]\n"
      "push %[rflags]\n"
      "push %[cs]\n"
      "push %[rip]\n"
      "mov %[rsi], %%rsi\n"
      "mov %[rdi], %%rdi\n"
      "mov %[es], %%es\n"
      "mov %[ds], %%ds\n"
      "mov %[r8], %%r8\n"
      "mov %[r9], %%r9\n"
      "mov %[r10], %%r10\n"
      "mov %[r11], %%r11\n"
      "mov %[r12], %%r12\n"
      "mov %[r13], %%r13\n"
      "mov %[r14], %%r14\n"
      "mov %[r15], %%r15\n"
      "mov %[rdx], %%rdx\n"
      "mov %[rcx], %%rcx\n"
      "mov %[rbx], %%rbx\n"
      "mov %[rax], %%rax\n"
      "mov %[rbp], %%rbp\n"
      "iretq" ::
      [fpu]"m"(info.fpu), [cr3]"r"(info.cr3), [ss]"m"(info.ss), [rsp]"m"(info.rsp), [rflags]"m"(info.rflags),
      [cs]"m"(info.cs), [rip]"m"(info.rip), [rsi]"m"(info.rsi), [rdi]"m"(info.rdi), [es]"m"(info.es), [ds]"m"(info.ds),
      [r8]"m"(info.r8), [r9]"m"(info.r9), [r10]"m"(info.r10), [r11]"m"(info.r11),[r12]"m"(info.r12), [r13]"m"(info.r13),
      [r14]"m"(info.r14), [r15]"m"(info.r15), [rdx]"m"(info.rdx),[rcx]"m"(info.rcx), [rbx]"m"(info.rbx),
      [rax]"m"(info.rax), [rbp]"m"(info.rbp) : "memory");
  assert(false && "This line should be unreachable");
}
