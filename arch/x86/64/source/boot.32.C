asm(".code32");
asm(".equ BASE,0xFFFFFFFF80000000");
asm(".equ PHYS_BASE,0xFFFFFFFF00000000");
#include "types.h"
#include "offsets.h"
#include "multiboot.h"

#if A_BOOT == A_BOOT | OUTPUT_ENABLED
#define PRINT(X) print(TRUNCATE(X))
#else
#define PRINT(X)
#endif

#define TRUNCATE(X) ({ volatile unsigned int x = (unsigned int)(((char*)X)+0x7FFFFFFF); (char*)(x+1); })

#define MULTIBOOT_PAGE_ALIGN (1<<0)
#define MULTIBOOT_MEMORY_INFO (1<<1)
#define MULTIBOOT_WANT_VESA (1<<2)
#define MULTIBOOT_HEADER_MAGIC (0x1BADB002)
#define MULTIBOOT_HEADER_FLAGS (MULTIBOOT_PAGE_ALIGN | MULTIBOOT_MEMORY_INFO | MULTIBOOT_WANT_VESA)
#define MULTIBOOT_CHECKSUM (-(MULTIBOOT_HEADER_MAGIC + MULTIBOOT_HEADER_FLAGS))

extern uint32 bss_start_address;
extern uint32 bss_end_address;
extern uint8 boot_stack[];
extern PageMapLevel4Entry kernel_page_map_level_4[];

extern uint32 tss_selector;

SegmentDescriptor gdt[7];

struct GDT32Ptr
{
    uint16 limit;
    uint32 addr;
}__attribute__((__packed__));

typedef struct
{
    uint32 reserved_0;
    uint32 rsp0_l;
    uint32 rsp0_h;
    uint32 rsp1_l;
    uint32 rsp1_h;
    uint32 rsp2_l;
    uint32 rsp2_h;
    uint32 reserved_1;
    uint32 reserved_2;
    uint32 ist0_l;
    uint32 ist0_h;
    uint32 reserved_3[14];
    uint16 reserved_4;
    uint16 iobp;
}__attribute__((__packed__)) TSS;

TSS g_tss;

static const struct
{
    uint32 magic = MULTIBOOT_HEADER_MAGIC;
    uint32 flags = MULTIBOOT_HEADER_FLAGS;
    uint32 checksum = MULTIBOOT_CHECKSUM;
    uint32 mode = 0;
    uint32 widht = 800;
    uint32 height = 600;
    uint32 depth = 32;
} mboot __attribute__ ((section (".mboot")));

void print(const char* p)
{
  while (*p)
    asm volatile ("outb %b0, %w1" : : "a"(*p++), "d"(0xe9));
}

static void memset(char* block, char c, size_t length)
{
  for (size_t i = 0; i < length; ++i)
    block[i] = c;
}

static void setSegmentDescriptor(uint32 index, uint32 baseH, uint32 baseL, uint32 limit, uint8 dpl, uint8 code,
                                 uint8 tss)
{
  SegmentDescriptor* gdt_p = (SegmentDescriptor*) TRUNCATE(&gdt);
  gdt_p[index].baseLL = (uint16) (baseL & 0xFFFF);
  gdt_p[index].baseLM = (uint8) ((baseL >> 16U) & 0xFF);
  gdt_p[index].baseLH = (uint8) ((baseL >> 24U) & 0xFF);
  gdt_p[index].baseH = baseH;
  gdt_p[index].limitL = (uint16) (limit & 0xFFFF);
  gdt_p[index].limitH = (uint8) (((limit >> 16U) & 0xF));
  gdt_p[index].typeH = tss ? 0 : (code ? 0xA : 0xC); // 4kb + 64bit
  gdt_p[index].typeL = (tss ? 0x89 : 0x92) | ((dpl & 0x3) << 5) | (code ? 0x8 : 0); // present bit + memory expands upwards + code
}

extern "C" void entry()
{
  asm volatile("mov %ebx,multi_boot_structure_pointer - BASE");
  PRINT("Booting...\n");
  PRINT("Clearing Framebuffer...\n");
  memset((char*) 0xB8000, 0, 80 * 25 * 2);

  PRINT("Clearing BSS...\n");
  char* bss_start = TRUNCATE(&bss_start_address);
  memset(bss_start, 0, TRUNCATE(&bss_end_address) - bss_start);

  PRINT("Initializing Kernel Paging Structures...\n");
  asm volatile("movl $kernel_page_directory_pointer_table - BASE + 3, kernel_page_map_level_4 - BASE\n"
      "movl $0, kernel_page_map_level_4 - BASE + 4\n" ::: "memory");
  asm volatile("movl $kernel_page_directory - BASE + 3, kernel_page_directory_pointer_table - BASE\n"
      "movl $0, kernel_page_directory_pointer_table - BASE + 4\n" ::: "memory");
  asm volatile("movl $0x83, kernel_page_directory - BASE\n"
      "movl $0, kernel_page_directory - BASE + 4\n" ::: "memory");

  PRINT("Enable PAE...\n");
  asm volatile("mov %%cr4,%%eax\n"
      "or $0x20, %%eax\n"
      "mov %%eax,%%cr4\n" ::: "eax");

  PRINT("Setting CR3 Register...\n");
  asm volatile("mov %[pd],%%cr3" : : [pd]"r"(TRUNCATE(kernel_page_map_level_4)));

  PRINT("Enable EFER.LME and EFER.NXE...\n");
  asm volatile("mov $0xC0000080,%%ecx\n"
      "rdmsr\n"
      "or $0x900,%%eax\n"
      "wrmsr\n" ::: "ecx", "eax", "edx");

  asm volatile("push $2\n"
      "popf\n");

  PRINT("Enable Paging...\n");
  asm volatile("mov %%cr0,%%eax\n"
      "or $0x80010001,%%eax\n"
      "mov %%eax,%%cr0\n" ::: "eax", "memory");

  PRINT("Setup TSS...\n");
  TSS* g_tss_p = (TSS*) TRUNCATE(&g_tss);
  g_tss_p->ist0_h = -1U;
  g_tss_p->ist0_l = (uint32) TRUNCATE(boot_stack) | 0x80004000;
  g_tss_p->rsp0_h = -1U;
  g_tss_p->rsp0_l = (uint32) TRUNCATE(boot_stack) | 0x80004000;
  g_tss_p->iobp = -1;

  PRINT("Setup Segments...\n");
  setSegmentDescriptor(1, 0, 0, 0xFFFFFFFF, 0, 1, 0);
  setSegmentDescriptor(2, 0, 0, 0xFFFFFFFF, 0, 0, 0);
  setSegmentDescriptor(3, 0, 0, 0xFFFFFFFF, 3, 1, 0);
  setSegmentDescriptor(4, 0, 0, 0xFFFFFFFF, 3, 0, 0);
  setSegmentDescriptor(5, -1U, (uint32) TRUNCATE(&g_tss) | 0x80000000, sizeof(TSS) - 1, 0, 0, 1);

  PRINT("Loading Long Mode GDT...\n");

  struct GDT32Ptr gdt32_ptr;
  gdt32_ptr.limit = sizeof(gdt) - 1;
  gdt32_ptr.addr = (uint32) TRUNCATE(gdt);
  asm volatile("lgdt %[gdt_ptr]" : : [gdt_ptr]"m"(gdt32_ptr));
  asm volatile("mov %%ax, %%ds\n" : : "a"(KERNEL_DS));

  PRINT("Setting Long Mode Segment Selectors...\n");
  asm volatile("mov %%ax, %%ds\n"
      "mov %%ax, %%es\n"
      "mov %%ax, %%ss\n"
      "mov %%ax, %%fs\n"
      "mov %%ax, %%gs\n"
      : : "a"(KERNEL_DS) : "memory");

  PRINT("Calling entry64()...\n");
  asm volatile("ljmp %[cs],$entry64-BASE\n" : : [cs]"i"(KERNEL_CS));

  PRINT("Returned from entry64()? This should never happen.\n");
  asm volatile("hlt");
}
asm(".code64");
