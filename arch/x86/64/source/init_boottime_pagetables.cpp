#include "types.h"
#include "paging-definitions.h"
#include "offsets.h"
#include "multiboot.h"
#include "ArchCommon.h"
#include "ArchMemory.h"
#include "kprintf.h"

extern void* kernel_end_address;

extern PageDirPointerTableEntry kernel_page_directory_pointer_table[];
extern PageDirEntry kernel_page_directory[];
extern PageTableEntry kernel_page_table[];
extern PageMapLevel4Entry kernel_page_map_level_4[];

extern "C" void initialisePaging()
{
  uint32 i;

  PageMapLevel4Entry *pml4 = (PageMapLevel4Entry*)VIRTUAL_TO_PHYSICAL_BOOT(kernel_page_map_level_4);
  PageDirPointerTableEntry *pdpt1 = (PageDirPointerTableEntry*)VIRTUAL_TO_PHYSICAL_BOOT((pointer)kernel_page_directory_pointer_table);
  PageDirPointerTableEntry *pdpt2 = pdpt1 + PAGE_DIR_POINTER_TABLE_ENTRIES;
  PageDirEntry *pd1 = (PageDirEntry*)VIRTUAL_TO_PHYSICAL_BOOT((pointer)kernel_page_directory);
  PageDirEntry *pd2 = pd1 + PAGE_DIR_ENTRIES;

  PageTableEntry *pt =  (PageTableEntry*)VIRTUAL_TO_PHYSICAL_BOOT((pointer)kernel_page_table);

  // Note: the only valid address ranges are currently:
  //         * 0000 0000 0 to * 7FFF FFFF F
  //                       binary: 011 111 111 means pml4i <= 255
  //         * 8000 0000 0 to * FFFF FFFF F
  //                       binary: 100 000 000 means pml4i >= 256

  // map the first and the last PML4 entry and one for the identity mapping
  pml4[0].page_ppn = (uint64)pdpt1 / PAGE_SIZE;
  pml4[0].writeable = 1;
  pml4[0].present = 1;
  pml4[480].page_ppn = (uint64)pdpt1 / PAGE_SIZE;
  pml4[480].writeable = 1;
  pml4[480].present = 1;
  pml4[511].page_ppn = (uint64)pdpt2 / PAGE_SIZE;
  pml4[511].writeable = 1;
  pml4[511].present = 1;

  // ident mapping 0x0                <-> 0x0 --> pml4i = 0, pdpti = 0
  // ident mapping 0x* F000 0000 0000 <-> 0x0 --> pml4i = 480, pdpti = 0
  // ident mapping 0x* FFFF 8000 0000 <-> 0x0 --> pml4i = 511, pdpti = 510

  pdpt1[0].pd.page_ppn = (uint64) pd1 / PAGE_SIZE;
  pdpt1[0].pd.writeable = 1;
  pdpt1[0].pd.present = 1;
  // 1 GiB for the kernel
  pdpt2[510].pd.page_ppn = (uint64) pd2 / PAGE_SIZE;
  pdpt2[510].pd.writeable = 1;
  pdpt2[510].pd.present = 1;
  // identity map
  for (i = 0; i < PAGE_DIR_ENTRIES; ++i)
  {
    pd2[i].page.present = 0;
    pd1[i].page.page_ppn = i;
    pd1[i].page.size = 1;
    pd1[i].page.writeable = 1;
    pd1[i].page.present = 1;
  }
  // Map 8 page directories (8*512*4kb = max 16mb)
  for (i = 0; i < 8; ++i)
  {
    pd2[i].pt.writeable = 1;
    pd2[i].pt.present = 1;
    pd2[i].pt.page_ppn = ((pointer)&pt[512*i])/PAGE_SIZE;;
  }

  size_t kernel_last_page = (size_t)VIRTUAL_TO_PHYSICAL_BOOT((pointer)&kernel_end_address) / PAGE_SIZE;

  extern size_t ro_data_end_address;
  size_t last_ro_data_page = (size_t)VIRTUAL_TO_PHYSICAL_BOOT((pointer)&ro_data_end_address) / PAGE_SIZE;

  // Map the kernel page tables (first 640kib = 184 pages are unused)
  for (i = 184; i < last_ro_data_page - 256; ++i)
  {
    pt[i].present = 1;
    pt[i].writeable = 0;
    pt[i].page_ppn = i;
  }
  for (; i < kernel_last_page; ++i)
  {
    pt[i].present = 1;
    pt[i].writeable = 1;
    pt[i].page_ppn = i;
  }

  if (ArchCommon::haveVESAConsole(0))
  {
    for (i = 0; i < 8; ++i) // map the 16 MiB (8 pages) framebuffer
    {
      pd2[504+i].page.present = 1;
      pd2[504+i].page.writeable = 1;
      pd2[504+i].page.size = 1;
      pd2[504+i].page.cache_disabled = 1;
      pd2[504+i].page.write_through = 1;
      pd2[504+i].page.page_ppn = (ArchCommon::getVESAConsoleLFBPtr(0) / (PAGE_SIZE * PAGE_TABLE_ENTRIES))+i;
    }
  }
}

extern "C" void removeBootTimeIdentMapping()
{
  uint64* pml4 = (uint64*)&kernel_page_map_level_4[0];
  pml4[0] = 0;
  pml4[1] = 0;

  ArchMemory::flushTlb();
}
