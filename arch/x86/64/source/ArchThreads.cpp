#include "ArchThreads.h"
#include "ArchMemory.h"
#include "kprintf.h"
#include "paging-definitions.h"
#include "offsets.h"
#include "assert.h"
#include "Thread.h"
#include "kstring.h"

extern PageMapLevel4Entry kernel_page_map_level_4[];

void ArchThreads::initialise()
{
  currentThreadRegisters = new ArchThreadRegisters{};

  /** Enable SSE for floating point instructions in long mode **/
  asm volatile ("movq %%cr0, %%rax\n"
          "and $0xFFFB, %%ax\n"
          "or $0x2, %%ax\n"
          "movq %%rax, %%cr0\n"
          "movq %%cr4, %%rax\n"
          "orq $0x200, %%rax\n"
          "movq %%rax, %%cr4\n" : : : "rax");
}
void ArchThreads::setAddressSpace(Thread *thread, ArchMemory& arch_memory)
{
  assert(arch_memory.page_map_level_4_);
  thread->kernel_registers_->cr3 = arch_memory.page_map_level_4_ * PAGE_SIZE;
  if (thread->user_registers_)
    thread->user_registers_->cr3 = arch_memory.page_map_level_4_ * PAGE_SIZE;

  if(thread == currentThread)
  {
          asm volatile("movq %[new_cr3], %%cr3\n"
                       ::[new_cr3]"r"(arch_memory.page_map_level_4_ * PAGE_SIZE));
  }
}

void ArchThreads::createBaseThreadRegisters(ArchThreadRegisters *&info, void* start_function, void* stack)
{
  info = new ArchThreadRegisters{};
  pointer pml4 = (pointer)VIRTUAL_TO_PHYSICAL_BOOT(((pointer)ArchMemory::getRootOfKernelPagingStructure()));

  info->rflags  = 0x200;
  info->cr3     = pml4;
  info->rsp     = (size_t)stack;
  info->rbp     = (size_t)stack;
  info->rip     = (size_t)start_function;

  /* fpu (=fninit) */
  info->fpu[0] = 0xFFFF037F;
  info->fpu[1] = 0xFFFF0000;
  info->fpu[2] = 0xFFFFFFFF;
  info->fpu[3] = 0x00000000;
  info->fpu[4] = 0x00000000;
  info->fpu[5] = 0x00000000;
  info->fpu[6] = 0xFFFF0000;
}

void ArchThreads::createKernelRegisters(ArchThreadRegisters *&info, void* start_function, void* kernel_stack)
{
  createBaseThreadRegisters(info, start_function, kernel_stack);

  info->cs      = KERNEL_CS;
  info->ds      = KERNEL_DS;
  info->es      = KERNEL_DS;
  info->ss      = KERNEL_SS;
  assert(info->cr3);
}

void ArchThreads::createUserRegisters(ArchThreadRegisters *&info, void* start_function, void* user_stack, void* kernel_stack)
{
  createBaseThreadRegisters(info, start_function, user_stack);

  info->cs      = USER_CS;
  info->ds      = USER_DS;
  info->es      = USER_DS;
  info->ss      = USER_SS;
  info->rsp0    = (size_t)kernel_stack;
  assert(info->cr3);
}

void ArchThreads::changeInstructionPointer(ArchThreadRegisters *info, void* function)
{
  info->rip = (size_t)function;
}

void ArchThreads::yield()
{
  __asm__ __volatile__("int $65");
}

size_t ArchThreads::testSetLock(size_t &lock, size_t new_value)
{
  return __sync_lock_test_and_set(&lock,new_value);
}

uint64 ArchThreads::atomic_add(uint64 &value, int64 increment)
{
  int64 ret=increment;
  __asm__ __volatile__(
  "lock; xadd %0, %1;"
  :"=a" (ret), "=m" (value)
  :"a" (ret)
  :);
  return ret;
}

int64 ArchThreads::atomic_add(int64 &value, int64 increment)
{
  return (int64) ArchThreads::atomic_add((uint64 &) value, increment);
}

void ArchThreads::atomic_set(uint32& target, uint32 value)
{
  __atomic_store_n(&(target), value, __ATOMIC_SEQ_CST);
}

void ArchThreads::atomic_set(int32& target, int32 value)
{
  __atomic_store_n(&(target), value, __ATOMIC_SEQ_CST);
}

void ArchThreads::atomic_set(uint64& target, uint64 value)
{
  __atomic_store_n(&(target), value, __ATOMIC_SEQ_CST);
}

void ArchThreads::atomic_set(int64& target, int64 value)
{
  __atomic_store_n(&(target), value, __ATOMIC_SEQ_CST);
}

void ArchThreads::printThreadRegisters(Thread *thread, bool verbose)
{
  printThreadRegisters(thread,0,verbose);
  printThreadRegisters(thread,1,verbose);
}

void ArchThreads::printThreadRegisters(Thread *thread, size_t userspace_registers, bool verbose)
{
  ArchThreadRegisters *info = userspace_registers?thread->user_registers_:thread->kernel_registers_;
  if (!info)
  {
    kprintfd("%sThread: %18p, has no %s registers. %s\n",userspace_registers?"  User":"Kernel",thread,userspace_registers?"User":"Kernel",userspace_registers?"":"This should never(!) occur. How did you do that?");
  }
  else if (verbose)
  {
    kprintfd("\t\t%sThread: %18p, info: %18p\n"\
             "\t\t\t rax: %18zx  rbx: %18zx  rcx: %18zx  rdx: %18zx\n"\
             "\t\t\t rsp: %18zx  rbp: %18zx  rsp0 %18zx  rip: %18zx\n"\
             "\t\t\trflg: %18zx  cr3: %18zx\n",
             userspace_registers?"  User":"Kernel",thread,info,info->rax,info->rbx,info->rcx,info->rdx,info->rsp,info->rbp,info->rsp0,info->rip,info->rflags,info->cr3);
  }
  else
  {
    kprintfd("%sThread: %18p, info: %18p -- rax: %18zx  rbx: %18zx  rcx: %18zx  rdx: %18zx -- rsp: %18zx  rbp: %18zx  rsp0 %18zx -- rip: %18zx  rflg: %18zx  cr3: %zx\n",
             userspace_registers?"  User":"Kernel",thread,info,info->rax,info->rbx,info->rcx,info->rdx,info->rsp,info->rbp,info->rsp0,info->rip,info->rflags,info->cr3);
  }
}

extern "C" void threadStartHack();

void ArchThreads::debugCheckNewThread(Thread* thread)
{
  assert(currentThread);
  ArchThreads::printThreadRegisters(currentThread,false);
  ArchThreads::printThreadRegisters(thread,false);
  assert(thread->kernel_registers_ != 0 && thread->kernel_registers_ != currentThread->kernel_registers_ && "all threads need to have their own register sets");
  assert(thread->kernel_registers_->rsp0 == 0 && "kernel register set needs no backup of kernel esp");
  assert(thread->kernel_registers_->rsp == thread->kernel_registers_->rbp && "new kernel stack must be empty");
  assert(thread->kernel_registers_->rsp != currentThread->kernel_registers_->rsp && thread->kernel_registers_->rbp != currentThread->kernel_registers_->rbp && "all threads need their own stack");
  assert(thread->kernel_registers_->cr3 < 0x80000000 && "cr3 contains the physical page dir address");
  if (thread->user_registers_ == 0)
    return;
  assert(thread->kernel_registers_->rip == 0 && "user threads should not start execution in kernel mode");
  assert(thread->switch_to_userspace_ == 1 && "new user threads must start in userspace");
  assert(thread->kernel_registers_->rsp == thread->user_registers_->rsp0 && "esp0 should point to kernel stack");
  assert(thread->kernel_registers_->cr3 == thread->user_registers_->cr3 && "user and kernel part of a thread need to have the same page dir");
  assert(thread->user_registers_->rip != 0 && "user eip needs to be valid... execution will start there");
  if (currentThread->user_registers_ == 0)
    return;
  assert(currentThread->user_registers_->rsp0 != thread->user_registers_->rsp0 && "no 2 threads may have the same esp0 value");
}
