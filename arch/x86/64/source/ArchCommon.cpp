#include "ArchCommon.h"
#include "multiboot.h"
#include "debug_bochs.h"
#include "offsets.h"
#include "kprintf.h"
#include "kstring.h"
#include "ArchMemory.h"
#include "FrameBufferConsole.h"
#include "TextConsole.h"
#include "ports.h"
#include "SWEBDebugInfo.h"
#include "PageManager.h"
#include "KernelMemoryManager.h"
#include "Console.h"

extern Console* main_console;

extern void* kernel_end_address;

multiboot_info_t* multi_boot_structure_pointer = (multiboot_info_t*)0xDEADDEAD; // must not be in bss segment
static struct multiboot_remainder mbr __attribute__ ((section (".data"))); // must not be in bss segment

extern "C" void parseMultibootHeader()
{
  uint32 i;
  multiboot_info_t *mb_infos = *(multiboot_info_t**)VIRTUAL_TO_PHYSICAL_BOOT( (pointer)&multi_boot_structure_pointer);
  struct multiboot_remainder &orig_mbr = (struct multiboot_remainder &)(*((struct multiboot_remainder*)VIRTUAL_TO_PHYSICAL_BOOT((pointer)&mbr)));

  if (mb_infos && mb_infos->f_vbe)
  {
    struct vbe_mode* mode_info = (struct vbe_mode*)(uint64)mb_infos->vbe_mode_info;
    orig_mbr.have_vesa_console = 1;
    orig_mbr.vesa_lfb_pointer = mode_info->phys_base;
    orig_mbr.vesa_x_res = mode_info->x_resolution;
    orig_mbr.vesa_y_res = mode_info->y_resolution;
    orig_mbr.vesa_bits_per_pixel = mode_info->bits_per_pixel;
  }

  if (mb_infos && mb_infos->f_mods)
  {
    module_t * mods = (module_t*)(uint64)mb_infos->mods_addr;
    for (i=0;i<mb_infos->mods_count;++i)
    {
      orig_mbr.module_maps[i].used = 1;
      orig_mbr.module_maps[i].start_address = mods[i].mod_start;
      orig_mbr.module_maps[i].end_address = mods[i].mod_end;
      strncpy((char*)(uint64)orig_mbr.module_maps[i].name, (const char*)(uint64)mods[i].string, 256);
    }
    orig_mbr.num_module_maps = mb_infos->mods_count;
  }

  for (i=0;i<MAX_MEMORY_MAPS;++i)
  {
    orig_mbr.memory_maps[i].used = 0;
  }

  if (mb_infos && mb_infos->f_mmap)
  {
    size_t i = 0;
    memory_map * map = (memory_map*)(uint64)(mb_infos->mmap_addr);
    while((uint64)map < (uint64)(mb_infos->mmap_addr + mb_infos->mmap_length))
    {
      orig_mbr.memory_maps[i].used          = 1;
      orig_mbr.memory_maps[i].start_address = ((uint64)map->base_addr_high << 32) | ((uint64)map->base_addr_low);
      orig_mbr.memory_maps[i].end_address   = orig_mbr.memory_maps[i].start_address
                                              + (((uint64)map->length_high << 32) | ((uint64)map->length_low));
      orig_mbr.memory_maps[i].type          = map->type;

      map = (memory_map*)(((uint64)(map)) + map->size + sizeof(map->size));
      ++i;
    }
  }
}

pointer ArchCommon::getKernelEndAddress()
{
   return (pointer)&kernel_end_address;
}

pointer ArchCommon::getFreeKernelMemoryStart()
{
  pointer free_kernel_memory_start = (pointer)&kernel_end_address;
  for (size_t i = 0; i < getNumModules(); ++i)
    free_kernel_memory_start = Max(getModuleEndAddress(i),free_kernel_memory_start);
  return ((free_kernel_memory_start - 1) | 0xFFF) + 1;
}

pointer ArchCommon::getFreeKernelMemoryEnd()
{
   return KernelMemoryManager::instance()->getKernelBreak();
}


size_t ArchCommon::haveVESAConsole(size_t is_paging_set_up)
{
  if (is_paging_set_up)
    return mbr.have_vesa_console;
  else
  {
    struct multiboot_remainder &orig_mbr = (struct multiboot_remainder &)(*((struct multiboot_remainder*)VIRTUAL_TO_PHYSICAL_BOOT((pointer)&mbr)));
    return orig_mbr.have_vesa_console;
  }
}

size_t ArchCommon::getNumModules(size_t is_paging_set_up)
{
  if (is_paging_set_up)
    return mbr.num_module_maps;
  else
  {
    struct multiboot_remainder* orig_mbr = &(struct multiboot_remainder &)(*((struct multiboot_remainder*)VIRTUAL_TO_PHYSICAL_BOOT((pointer)&mbr)));
    return orig_mbr->num_module_maps;
  }
}

size_t ArchCommon::getModuleStartAddress(size_t num,size_t is_paging_set_up)
{
  if (is_paging_set_up)
    return mbr.module_maps[num].start_address | PHYSICAL_TO_VIRTUAL_OFFSET;
  else
  {
    struct multiboot_remainder &orig_mbr = (struct multiboot_remainder &)(*((struct multiboot_remainder*)VIRTUAL_TO_PHYSICAL_BOOT((pointer)&mbr)));
    return orig_mbr.module_maps[num].start_address;
  }
}

size_t ArchCommon::getModuleEndAddress(size_t num,size_t is_paging_set_up)
{
  if (is_paging_set_up)
    return mbr.module_maps[num].end_address | PHYSICAL_TO_VIRTUAL_OFFSET;
  else
  {
    struct multiboot_remainder &orig_mbr = (struct multiboot_remainder &)(*((struct multiboot_remainder*)VIRTUAL_TO_PHYSICAL_BOOT((pointer)&mbr)));
    return orig_mbr.module_maps[num].end_address;
  }
}

size_t ArchCommon::getVESAConsoleHeight()
{
  return mbr.vesa_y_res;
}

size_t ArchCommon::getVESAConsoleWidth()
{
  return mbr.vesa_x_res;
}

pointer ArchCommon::getVESAConsoleLFBPtr(size_t is_paging_set_up)
{
  if (is_paging_set_up)
    return 0xFFFFFFFFC0000000ULL - 1024ULL * 1024ULL * 16ULL;
  else
  {
    struct multiboot_remainder &orig_mbr = (struct multiboot_remainder &)(*((struct multiboot_remainder*)VIRTUAL_TO_PHYSICAL_BOOT((pointer)&mbr)));
    return orig_mbr.vesa_lfb_pointer;
  }
}

pointer ArchCommon::getFBPtr(size_t is_paging_set_up)
{
  if (is_paging_set_up)
    return PHYSICAL_TO_VIRTUAL_OFFSET | 0xB8000ULL;
  else
    return 0xB8000ULL;
}

size_t ArchCommon::getVESAConsoleBitsPerPixel()
{
  return mbr.vesa_bits_per_pixel;
}

size_t ArchCommon::getNumUseableMemoryRegions()
{
  uint32 i;
  for (i=0;i<MAX_MEMORY_MAPS;++i)
  {
    if (!mbr.memory_maps[i].used)
      break;
  }
  return i;
}

size_t ArchCommon::getUsableMemoryRegion(size_t region, pointer &start_address, pointer &end_address, size_t &type)
{
  if (region >= MAX_MEMORY_MAPS)
    return 1;

  start_address = mbr.memory_maps[region].start_address;
  end_address = mbr.memory_maps[region].end_address;
  type = mbr.memory_maps[region].type;

  return 0;
}

Console* ArchCommon::createConsole(size_t count)
{
  // deactivate cursor
  outportb(0x3d4, 0xa);
  outportb(0x3d5, 0b00100000);
  if (haveVESAConsole())
    return new FrameBufferConsole(count);
  else
    return new TextConsole(count);
}


#if (A_BOOT == A_BOOT | OUTPUT_ENABLED)
#define PRINT(X) writeLine2Bochs((const char*)VIRTUAL_TO_PHYSICAL_BOOT(X))
#else
#define PRINT(X)
#endif

extern SegmentDescriptor gdt[7];
extern "C" void startup();
extern "C" void initialisePaging();
extern uint8 boot_stack[0x4000];

struct GDTPtr
{
    uint16 limit;
    uint64 addr;
}__attribute__((__packed__)) gdt_ptr;

extern "C" void entry64()
{
  PRINT("Parsing Multiboot Header...\n");
  parseMultibootHeader();
  PRINT("Initializing Kernel Paging Structures...\n");
  initialisePaging();
  PRINT("Setting CR3 Register...\n");
  asm("mov %%rax, %%cr3" : : "a"(VIRTUAL_TO_PHYSICAL_BOOT(ArchMemory::getRootOfKernelPagingStructure())) : "memory");
  PRINT("Switch to our own stack...\n");
  asm("mov %[stack], %%rsp\n"
      "mov %[stack], %%rbp\n" : : [stack]"i"(boot_stack + 0x4000) : "memory");
  PRINT("Loading Long Mode Segments...\n");

  gdt_ptr.limit = sizeof(gdt) - 1;
  gdt_ptr.addr = (uint64)gdt;
  asm("lgdt (%%rax)" : : "a"(&gdt_ptr));
  asm("mov %%ax, %%ds\n"
      "mov %%ax, %%es\n"
      "mov %%ax, %%ss\n"
      "mov %%ax, %%fs\n"
      "mov %%ax, %%gs\n"
      : : "a"(KERNEL_DS) : "memory");
  asm("ltr %%ax" : : "a"(KERNEL_TSS));

  PRINT("Check SMEP support...\n");
  uint32 ebx=0;
  asm volatile("mov $7, %%rax\n"
               "xor %%rcx, %%rcx\n"
               "cpuid\n"
               :"=b"(ebx)::"rax", "rcx", "rdx");
  if (ebx & (1<<7))
  {
    PRINT("Enable SMEP...\n");
    asm volatile("mov %%cr4,%%rax\n"
                 "or $0x100000, %%rax\n"
                 "mov %%rax,%%cr4\n" ::: "rax", "memory");
  }

  PRINT("Calling startup()...\n");
  asm("jmp *%[startup]" : : [startup]"r"(startup));
  while (1);
}

class Stabs2DebugInfo;
Stabs2DebugInfo const *kernel_debug_info = 0;

void ArchCommon::initDebug()
{
  for (size_t i = 0; i < getNumModules(); ++i)
  {
    if (memcmp("SWEBDBG1",(char const *)getModuleStartAddress(i),8) == 0)
      kernel_debug_info = new SWEBDebugInfo((char const *)getModuleStartAddress(i),
                                              (char const *)getModuleEndAddress(i));
  }
  if (!kernel_debug_info)
    kernel_debug_info = new SWEBDebugInfo(0, 0);
}

void ArchCommon::idle()
{
  asm volatile("hlt");
}

#define STATS_OFFSET 22
#define FREE_PAGES_OFFSET STATS_OFFSET + 11*2


void ArchCommon::drawStat()
{
  const char* text  = "Free pages      F9 MemInfo   F10 Locks   F11 Stacktrace   F12 Threads";
  const char* color = "xxxxxxxxxx      xx           xxx         xxx              xxx        ";

  size_t i = 0;
  char itoa_buffer[33];

  if (haveVESAConsole()) {
    FrameBufferConsole* fb_console = static_cast<FrameBufferConsole*>(main_console);
    if (fb_console) {
      size_t row = 0;
      size_t column = STATS_OFFSET - 12;

      while (text[i]) {
        uint8 state = (color[i] == 'x' ? 0x80 : 0x08); // 0x80 -> light bakground/dark text, 0x08 -> dark background/light text 
        fb_console->consoleSetCharacter(row, column + i, text[i], state);
        i++;
      }

      memset(itoa_buffer, '\0', sizeof(itoa_buffer));
      itoa(PageManager::instance()->getNumFreePages(), itoa_buffer, 10);

      for (size_t j = 0; j < sizeof(itoa_buffer) && itoa_buffer[j] != '\0'; ++j) {
        fb_console->consoleSetCharacter(row, column + 11 + j, itoa_buffer[j], 0x08);   
      }
    }
  } 
  else 
  {
    char* fb = (char*)getFBPtr();

    while (text[i]) {
      fb[i * 2 + STATS_OFFSET] = text[i];
      fb[i * 2 + STATS_OFFSET + 1] = (char)(color[i] == 'x' ? 0x80 : 0x08);
      i++;
    }

    memset(itoa_buffer, '\0', sizeof(itoa_buffer));
    itoa(PageManager::instance()->getNumFreePages(), itoa_buffer, 10);

    for (size_t i = 0; (i < sizeof(itoa_buffer)) && (itoa_buffer[i] != '\0'); ++i) 
    {
      fb[i * 2 + FREE_PAGES_OFFSET] = itoa_buffer[i];
    }
  }
}


void ArchCommon::drawHeartBeat()
{
  const char* clock = "/-\\|";
  static uint32 heart_beat_value = 0;

  if (haveVESAConsole())
  {
    uint8 state = 0x9f;  
    char heartbeat_char = clock[heart_beat_value++ % 4];

    FrameBufferConsole* fb_console = static_cast<FrameBufferConsole*>(main_console);
    if(fb_console)
      fb_console->consoleSetCharacter(0, 0, heartbeat_char, state);
  }
  else 
  {
    char* fb = (char*)getFBPtr();
    fb[0] = clock[heart_beat_value++ % 4];
    fb[1] = (char)0x9f;
  }

  drawStat();
}

