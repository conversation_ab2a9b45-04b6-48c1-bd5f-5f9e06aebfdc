#include "ArchMemory.h"
#include "ArchInterrupts.h"
#include "kprintf.h"
#include "PageManager.h"
#include "kstring.h"
#include "ArchThreads.h"
#include "Thread.h"
#include "ProcessRegistry.h"
#include "UserThread.h"
#include "Scheduler.h"

PageMapLevel4Entry kernel_page_map_level_4[PAGE_MAP_LEVEL_4_ENTRIES] __attribute__((aligned(PAGE_SIZE)));
PageDirPointerTableEntry kernel_page_directory_pointer_table[2 * PAGE_DIR_POINTER_TABLE_ENTRIES] __attribute__((aligned(PAGE_SIZE)));
PageDirEntry kernel_page_directory[2 * PAGE_DIR_ENTRIES] __attribute__((aligned(PAGE_SIZE)));
PageTableEntry kernel_page_table[8 * PAGE_TABLE_ENTRIES] __attribute__((aligned(PAGE_SIZE)));

ArchMemory::ArchMemory() : archmemory_lock_("ARCHMEMORY::archmemory_lock_")
{
  page_map_level_4_ = PageManager::instance()->allocPPN();
  PageMapLevel4Entry *new_pml4 = (PageMapLevel4Entry *)getIdentAddressOfPPN(page_map_level_4_);
  memcpy((void *)new_pml4, (void *)kernel_page_map_level_4, PAGE_SIZE);
  memset(new_pml4, 0, PAGE_SIZE / 2); // should be zero, this is just for safety
}

ArchMemory::ArchMemory(ArchMemory const &src, UserProcess *process) : archmemory_lock_("ARCHMEMORY::archmemory_lock_")
{
  page_map_level_4_ = PageManager::instance()->allocPPN();
  size_t needed_pages_before_lock = 0;
  size_t needed_pages_in_lock = 0;
  bool num_pages_ok = false;

  while (num_pages_ok == false)
  {
    src.archmemory_lock_.acquire();
    needed_pages_before_lock = determineNeededPages(src);
    src.archmemory_lock_.release();

    for (size_t num_pages = 0; num_pages < needed_pages_before_lock; num_pages++)
    {
      pages_allocced.push_back(PageManager::instance()->allocPPN());
    }

    ProcessRegistry::instance()->ipt_lock_.acquire();
    src.archmemory_lock_.acquire();
    archmemory_lock_.acquire();
    num_pages_ok = true;

    needed_pages_in_lock = determineNeededPages(src);
    if (needed_pages_before_lock < needed_pages_in_lock)
    {
      num_pages_ok = false;
      archmemory_lock_.release();
      src.archmemory_lock_.release();
      ProcessRegistry::instance()->ipt_lock_.release();
    }
  }

  PageMapLevel4Entry *new_pml4 = (PageMapLevel4Entry *)getIdentAddressOfPPN(page_map_level_4_);
  PageMapLevel4Entry *src_pml4 = (PageMapLevel4Entry *)getIdentAddressOfPPN(src.page_map_level_4_);
  memcpy((void *)new_pml4, (void *)kernel_page_map_level_4, PAGE_SIZE);

  for (size_t pml4_i = 0; pml4_i < PAGE_MAP_LEVEL_4_ENTRIES; pml4_i++)
  {
    if (src_pml4[pml4_i].present)
    {
      if (pml4_i > (PAGE_MAP_LEVEL_4_ENTRIES / 2))
      {
        new_pml4[pml4_i] = src_pml4[pml4_i];
      }
      else
      {
        new_pml4[pml4_i] = src_pml4[pml4_i];
        PageDirPointerTableEntry *srcPDPT = (PageDirPointerTableEntry *)getIdentAddressOfPPN(src_pml4[pml4_i].page_ppn);

        assert(!pages_allocced.empty() && "Not enough pages provided for fork!");
        uint64 newPDPT_ppn = pages_allocced.back();
        pages_allocced.pop_back();
        PageDirPointerTableEntry *newPDPT = (PageDirPointerTableEntry *)getIdentAddressOfPPN(newPDPT_ppn);
        // ProcessRegistry::instance()->addToIPT(newPDPT_ppn , (uint64)newPDPT, process, PDPT);

        for (size_t PDPT_i = 0; PDPT_i < PAGE_DIR_POINTER_TABLE_ENTRIES; PDPT_i++)
        {
          if (srcPDPT[PDPT_i].pd.present)
          {
            newPDPT[PDPT_i] = srcPDPT[PDPT_i];
            PageDirEntry *srcPD = (PageDirEntry *)getIdentAddressOfPPN(srcPDPT[PDPT_i].pd.page_ppn);

            assert(!pages_allocced.empty() && "Not enough pages provided for fork!");
            uint64 newPD_ppn = pages_allocced.back();
            pages_allocced.pop_back();
            PageDirEntry *newPD = (PageDirEntry *)getIdentAddressOfPPN(newPD_ppn);

            for (size_t PD_i = 0; PD_i < PAGE_DIR_ENTRIES; PD_i++)
            {
              if (srcPD[PD_i].pt.present)
              {
                newPD[PD_i] = srcPD[PD_i];
                PageTableEntry *srcPT = (PageTableEntry *)getIdentAddressOfPPN(srcPD[PD_i].pt.page_ppn);

                assert(!pages_allocced.empty() && "Not enough pages provided for fork!");
                uint64 newPT_ppn = pages_allocced.back();
                pages_allocced.pop_back();
                PageTableEntry *newPT = (PageTableEntry *)getIdentAddressOfPPN(newPT_ppn);

                for (size_t PT_i = 0; PT_i < PAGE_TABLE_ENTRIES; PT_i++)
                {
                  if (srcPT[PT_i].present)
                  {
                    if (srcPT[PT_i].shm == 0)
                    {
                      srcPT[PT_i].cow = 1;
                      srcPT[PT_i].writeable = 0;
                    }
                    newPT[PT_i] = srcPT[PT_i];

                    size_t vpn = 0;
                    vpn |= (pml4_i << 39);
                    vpn |= (PDPT_i << 30);
                    vpn |= (PD_i << 21);
                    vpn |= (PT_i << 12);
                    size_t vpn_ = vpn >> PAGE_INDEX_OFFSET_BITS;
                    ProcessRegistry::instance()->addToIPT(newPT[PT_i].page_ppn, vpn_, process, PAGE);

                    if ( srcPT[PT_i].stored == 1)
                    {
                      size_t spn = ProcessRegistry::instance()->inverted_page_table[newPT[PT_i].page_ppn].spn;
                      auto entry = Scheduler::instance()->swap_thread_.swap_Table_.getEntry(spn);
                      assert(entry);
                      vpn_pid_tuple tuble;
                      tuble.vpn = vpn_;
                      tuble.process = process;
                      tuble.type = PAGE;
                      entry->IPT_Entry_.tuples.push_back(tuble);
                    }
                    
                  }
                  if (srcPT[PT_i].swapped)
                  {

                    size_t vpn = 0;
                    vpn |= (pml4_i << 39);
                    vpn |= (PDPT_i << 30);
                    vpn |= (PD_i << 21);
                    vpn |= (PT_i << 12);
                    size_t vpn_ = vpn >> PAGE_INDEX_OFFSET_BITS;
                    if (srcPT[PT_i].shm == 0)
                    {
                      srcPT[PT_i].cow = 1;
                      srcPT[PT_i].writeable = 0;
                    }
                    newPT[PT_i] = srcPT[PT_i];

                    auto entry = Scheduler::instance()->swap_thread_.swap_Table_.getEntry(srcPT[PT_i].page_ppn);
                    vpn_pid_tuple tuble;
                    tuble.vpn = vpn_;
                    tuble.process = process;
                    tuble.type = PAGE;
                    entry->IPT_Entry_.tuples.push_back(tuble);
                  }
                }
                newPD[PD_i].pt.page_ppn = (uint64)newPT_ppn;
              }
            }
            newPDPT[PDPT_i].pd.page_ppn = (uint64)newPD_ppn;
          }
        }
        new_pml4[pml4_i].page_ppn = (uint64)newPDPT_ppn;
      }
    }
  }

  while (pages_allocced.empty() == false)
  {
    PageManager::instance()->freePPN(pages_allocced.back());
    pages_allocced.pop_back();
  }

  archmemory_lock_.release();
  src.archmemory_lock_.release();
  ProcessRegistry::instance()->ipt_lock_.release();
}

template <typename T>
bool ArchMemory::checkAndRemove(pointer map_ptr, uint64 index)
{
  T *map = (T *)map_ptr;
  debug(A_MEMORY, "%s: page %p index %zx\n", __PRETTY_FUNCTION__, map, index);
  ((uint64 *)map)[index] = 0;
  for (uint64 i = 0; i < PAGE_DIR_ENTRIES; i++)
  {
    if (map[i].present != 0)
      return false;
  }
  return true;
}

bool ArchMemory::unmapPage(uint64 virtual_page)
{
  ArchMemoryMapping m = resolveMapping(virtual_page);

  assert(m.page_ppn != 0 && m.page_size == PAGE_SIZE && m.pt[m.pti].present);
  m.pt[m.pti].present = 0;
  // ProcessRegistry::instance()->ipt_lock_.acquire();
  debug(USERPROCESS, "page_ ppn:%d, refcount:%d ,pid: %d\n", m.page_ppn, ProcessRegistry::instance()->inverted_page_table[m.page_ppn].tuples.size(), pid_);
  ProcessRegistry::instance()->deleteFromIPT(m.page_ppn, virtual_page,pid_);
  debug(USERPROCESS, "page_ ppn:%d, refcount after delete:%d ,pid: %d\n", m.page_ppn, ProcessRegistry::instance()->inverted_page_table[m.page_ppn].tuples.size(), pid_);
  if (ProcessRegistry::instance()->inverted_page_table[m.page_ppn].tuples.size() == 0 && m.pt[m.pti].shm == 0)
  {
    PageManager::instance()->freePPN(m.page_ppn);
  }
  // ProcessRegistry::instance()->ipt_lock_.release();

  ((uint64 *)m.pt)[m.pti] = 0; // for easier debugging
  bool empty = checkAndRemove<PageTableEntry>(getIdentAddressOfPPN(m.pt_ppn), m.pti);
  if (empty)
  {
    empty = checkAndRemove<PageDirPageTableEntry>(getIdentAddressOfPPN(m.pd_ppn), m.pdi);
    PageManager::instance()->freePPN(m.pt_ppn);
    // ProcessRegistry::instance()->deleteFromIPT(m.pt_ppn, virtual_page, ((UserThread *)currentThread)->process_->pid_);
  }
  if (empty)
  {
    empty = checkAndRemove<PageDirPointerTablePageDirEntry>(getIdentAddressOfPPN(m.pdpt_ppn), m.pdpti);
    PageManager::instance()->freePPN(m.pd_ppn);
    // ProcessRegistry::instance()->deleteFromIPT(m.pd_ppn, virtual_page, ((UserThread *)currentThread)->process_->pid_);
  }
  if (empty)
  {
    checkAndRemove<PageMapLevel4Entry>(getIdentAddressOfPPN(m.pml4_ppn), m.pml4i);
    PageManager::instance()->freePPN(m.pdpt_ppn);
    // ProcessRegistry::instance()->deleteFromIPT(m.pdpt_ppn, virtual_page, ((UserThread *)currentThread)->process_->pid_);
  }

  return true;
}

template <typename T>
void ArchMemory::insert(pointer map_ptr, uint64 index, uint64 ppn, uint64 bzero, uint64 size, uint64 user_access,
                        uint64 writeable)
{
  assert(map_ptr & ~0xFFFFF00000000000ULL);
  T *map = (T *)map_ptr;
  debug(A_MEMORY, "%s: page %p index %zx ppn %zx user_access %zx size %zx\n", __PRETTY_FUNCTION__, map, index, ppn,
        user_access, size);
  if (bzero)
  {
    memset((void *)getIdentAddressOfPPN(ppn), 0, PAGE_SIZE);
    assert(((uint64 *)map)[index] == 0);
  }
  map[index].size = size;
  map[index].writeable = writeable;
  map[index].page_ppn = ppn;
  map[index].user_access = user_access;
  map[index].present = 1;
}

bool ArchMemory::mapPage(uint64 virtual_page, ustl::queue<size_t> &ppns, size_t physical_page, uint64 user_access)
{

  // archmemory_lock_.acquire();

  // debug(A_MEMORY, "%zx %zx %zx %zx\n", page_map_level_4_, virtual_page, physical_page, user_access);
  ArchMemoryMapping m = resolveMapping(page_map_level_4_, virtual_page);
  assert((m.page_size == 0) || (m.page_size == PAGE_SIZE));

  // ProcessRegistry::instance()->ipt_lock_.acquire();
  if (m.pdpt_ppn == 0)
  {
    assert(ppns.size() > 0);
    m.pdpt_ppn = ppns.front();
    ppns.pop();
    insert<PageMapLevel4Entry>((pointer)m.pml4, m.pml4i, m.pdpt_ppn, 1, 0, 1, 1);
    // ProcessRegistry::instance()->addToIPT(m.pdpt_ppn, virtual_page, ((UserThread *)currentThread)->process_, PDPT);
  }

  if (m.pd_ppn == 0)
  {
    assert(ppns.size() > 0);
    m.pd_ppn = ppns.front();
    ppns.pop();
    insert<PageDirPointerTablePageDirEntry>(getIdentAddressOfPPN(m.pdpt_ppn), m.pdpti, m.pd_ppn, 1, 0, 1, 1);
    // ProcessRegistry::instance()->addToIPT(m.pd_ppn, virtual_page, ((UserThread *)currentThread)->process_, PD);
  }

  if (m.pt_ppn == 0)
  {
    assert(ppns.size() > 0);
    m.pt_ppn = ppns.front();
    ppns.pop();

    insert<PageDirPageTableEntry>(getIdentAddressOfPPN(m.pd_ppn), m.pdi, m.pt_ppn, 1, 0, 1, 1);
    // ProcessRegistry::instance()->addToIPT(m.pt_ppn, virtual_page, ((UserThread *)currentThread)->process_, PT);
  }

  if (m.page_ppn == 0)
  {

    insert<PageTableEntry>(getIdentAddressOfPPN(m.pt_ppn), m.pti, physical_page, 0, 0, user_access, 1);

    // if (((UserThread *)currentThread)->process_->pid_ != 0)
    // {
      ProcessRegistry::instance()->addToIPT(physical_page, virtual_page, ((UserThread *)currentThread)->process_, PAGE);
      // ProcessRegistry::instance()->printIPT();
      ProcessRegistry::instance()->ppns_second_chance.push_back(physical_page);
    //}

    return true;
  }

  return false;
}

ArchMemory::~ArchMemory()
{
  assert(currentThread->kernel_registers_->cr3 != page_map_level_4_ * PAGE_SIZE && "thread deletes its own arch memory");

  ProcessRegistry::instance()->ipt_lock_.acquire();
  archmemory_lock_.acquire();
  PageMapLevel4Entry *pml4 = (PageMapLevel4Entry *)getIdentAddressOfPPN(page_map_level_4_);
  for (uint64 pml4i = 0; pml4i < PAGE_MAP_LEVEL_4_ENTRIES / 2; pml4i++) // free only lower half
  {
    if (pml4[pml4i].present)
    {
      PageDirPointerTableEntry *pdpt = (PageDirPointerTableEntry *)getIdentAddressOfPPN(pml4[pml4i].page_ppn);
      for (uint64 pdpti = 0; pdpti < PAGE_DIR_POINTER_TABLE_ENTRIES; pdpti++)
      {
        if (pdpt[pdpti].pd.present)
        {
          assert(pdpt[pdpti].pd.size == 0);
          PageDirEntry *pd = (PageDirEntry *)getIdentAddressOfPPN(pdpt[pdpti].pd.page_ppn);
          for (uint64 pdi = 0; pdi < PAGE_DIR_ENTRIES; pdi++)
          {
            if (pd[pdi].pt.present)
            {
              assert(pd[pdi].pt.size == 0);
              PageTableEntry *pt = (PageTableEntry *)getIdentAddressOfPPN(pd[pdi].pt.page_ppn);
              for (uint64 pti = 0; pti < PAGE_TABLE_ENTRIES; pti++)
              {
                if (pt[pti].swapped && pt[pti].present == 0)
                {
                  debug(USERPROCESS, "page_ ppn:%d  removed from swaptable by process %d!\n", pt[pti].page_ppn, pid_);
                  Scheduler::instance()->swap_thread_.swap_Table_.removeReference(pt[pti].page_ppn, pid_);
                }

                if (pt[pti].present)
                {
                  debug(USERPROCESS, "page_ ppn:%d, refcount:%d ,pid: %d\n", pt[pti].page_ppn, ProcessRegistry::instance()->inverted_page_table[pt[pti].page_ppn].tuples.size(), pid_);
                  ProcessRegistry::instance()->deleteFromIPT(pt[pti].page_ppn, DECONSTRUCT, pid_);
                  ///////////////
                  //check spn entry exist and delete tuple
                  size_t spn = ProcessRegistry::instance()->inverted_page_table[pt[pti].page_ppn].spn;
                  Scheduler::instance()->swap_thread_.swap_Table_.removeReference(spn,pid_);
                  debug(USERPROCESS, "page_ ppn:%d  removed from swaptable by process %d!\n", pt[pti].page_ppn, pid_);

                  
                  debug(USERPROCESS, "page_ ppn:%d, refcount after delete:%d ", pt[pti].page_ppn, ProcessRegistry::instance()->inverted_page_table[pt[pti].page_ppn].tuples.size());
                }

                if ((pt[pti].present && ProcessRegistry::instance()->inverted_page_table[pt[pti].page_ppn].tuples.size() == 0) && pt[pti].shm == 0)
                {
                  pt[pti].present = 0;
                  PageManager::instance()->freePPN(pt[pti].page_ppn);
                }
              }
              pd[pdi].pt.present = 0;
              PageManager::instance()->freePPN(pd[pdi].pt.page_ppn);
              ProcessRegistry::instance()->deleteFromIPT(pd[pdi].pt.page_ppn, DECONSTRUCT, pid_);
            }
          }
          pdpt[pdpti].pd.present = 0;
          PageManager::instance()->freePPN(pdpt[pdpti].pd.page_ppn);
          ProcessRegistry::instance()->deleteFromIPT(pdpt[pdpti].pd.page_ppn, DECONSTRUCT, pid_);
        }
      }
      pml4[pml4i].present = 0;
      PageManager::instance()->freePPN(pml4[pml4i].page_ppn);
      ProcessRegistry::instance()->deleteFromIPT(pml4[pml4i].page_ppn, DECONSTRUCT, pid_);
    }
  }
  PageManager::instance()->freePPN(page_map_level_4_);
  ProcessRegistry::instance()->deleteFromIPT(page_map_level_4_, DECONSTRUCT, pid_);
  //ProcessRegistry::instance()->printIPT();
  archmemory_lock_.release();
  ProcessRegistry::instance()->ipt_lock_.release();
}

pointer ArchMemory::checkAddressValid(uint64 vaddress_to_check)
{
  ArchMemoryMapping m = resolveMapping(page_map_level_4_, vaddress_to_check / PAGE_SIZE);
  if (m.page != 0)
  {
    debug(A_MEMORY, "checkAddressValid %zx and %zx -> true\n", page_map_level_4_, vaddress_to_check);
    return m.page | (vaddress_to_check % m.page_size);
  }
  else
  {
    debug(A_MEMORY, "checkAddressValid %zx and %zx -> false\n", page_map_level_4_, vaddress_to_check);
    return 0;
  }
}

const ArchMemoryMapping ArchMemory::resolveMapping(uint64 vpage)
{
  return resolveMapping(page_map_level_4_, vpage);
}

const ArchMemoryMapping ArchMemory::resolveMapping(uint64 pml4, uint64 vpage)
{
  assert((vpage * PAGE_SIZE < USER_BREAK || vpage * PAGE_SIZE >= KERNEL_START) &&
         "This is not a valid vpn! Did you pass an address to resolveMapping?");
  ArchMemoryMapping m;

  m.pti = vpage;
  m.pdi = m.pti / PAGE_TABLE_ENTRIES;
  m.pdpti = m.pdi / PAGE_DIR_ENTRIES;
  m.pml4i = m.pdpti / PAGE_DIR_POINTER_TABLE_ENTRIES;

  m.pti %= PAGE_TABLE_ENTRIES;
  m.pdi %= PAGE_DIR_ENTRIES;
  m.pdpti %= PAGE_DIR_POINTER_TABLE_ENTRIES;
  m.pml4i %= PAGE_MAP_LEVEL_4_ENTRIES;

  assert(pml4 < PageManager::instance()->getTotalNumPages());
  m.pml4 = (PageMapLevel4Entry *)getIdentAddressOfPPN(pml4);
  m.pdpt = 0;
  m.pd = 0;
  m.pt = 0;
  m.page = 0;
  m.pml4_ppn = pml4;
  m.pdpt_ppn = 0;
  m.pd_ppn = 0;
  m.pt_ppn = 0;
  m.page_ppn = 0;
  m.page_size = 0;
  if (m.pml4[m.pml4i].present)
  {
    m.pdpt_ppn = m.pml4[m.pml4i].page_ppn;
    m.pdpt = (PageDirPointerTableEntry *)getIdentAddressOfPPN(m.pml4[m.pml4i].page_ppn);
    if (m.pdpt[m.pdpti].pd.present && !m.pdpt[m.pdpti].pd.size) // 1gb page ?
    {
      m.pd_ppn = m.pdpt[m.pdpti].pd.page_ppn;
      if (m.pd_ppn > PageManager::instance()->getTotalNumPages())
      {
        debug(A_MEMORY, "%zx\n", m.pd_ppn);
      }
      assert(m.pd_ppn < PageManager::instance()->getTotalNumPages());
      m.pd = (PageDirEntry *)getIdentAddressOfPPN(m.pdpt[m.pdpti].pd.page_ppn);
      if (m.pd[m.pdi].pt.present && !m.pd[m.pdi].pt.size) // 2mb page ?
      {
        m.pt_ppn = m.pd[m.pdi].pt.page_ppn;
        assert(m.pt_ppn < PageManager::instance()->getTotalNumPages());
        m.pt = (PageTableEntry *)getIdentAddressOfPPN(m.pd[m.pdi].pt.page_ppn);
        if (m.pt[m.pti].present)
        {
          m.page = getIdentAddressOfPPN(m.pt[m.pti].page_ppn);
          m.page_ppn = m.pt[m.pti].page_ppn;
          assert(m.page_ppn < PageManager::instance()->getTotalNumPages());
          m.page_size = PAGE_SIZE;
        }
        if (m.pt[m.pti].present == 0 && m.pt[m.pti].swapped)
        {
          m.page_ppn = m.pt[m.pti].page_ppn;
          m.page_size = PAGE_SIZE;
        }
      }
      else if (m.pd[m.pdi].page.present)
      {
        m.page_size = PAGE_SIZE * PAGE_TABLE_ENTRIES;
        m.page_ppn = m.pd[m.pdi].page.page_ppn;
        m.page = getIdentAddressOfPPN(m.pd[m.pdi].page.page_ppn);
      }
    }
    else if (m.pdpt[m.pdpti].page.present)
    {
      m.page_size = PAGE_SIZE * PAGE_TABLE_ENTRIES * PAGE_DIR_ENTRIES;
      m.page_ppn = m.pdpt[m.pdpti].page.page_ppn;
      assert(m.page_ppn < PageManager::instance()->getTotalNumPages());
      m.page = getIdentAddressOfPPN(m.pdpt[m.pdpti].page.page_ppn);
    }
  }
  return m;
}

uint64 ArchMemory::get_PPN_Of_VPN_In_KernelMapping(size_t virtual_page, size_t *physical_page,
                                                   size_t *physical_pte_page)
{
  ArchMemoryMapping m = resolveMapping(((uint64)VIRTUAL_TO_PHYSICAL_BOOT(kernel_page_map_level_4) / PAGE_SIZE),
                                       virtual_page);
  if (physical_page)
    *physical_page = m.page_ppn;
  if (physical_pte_page)
    *physical_pte_page = m.pt_ppn;
  return m.page_size;
}

void ArchMemory::mapKernelPage(size_t virtual_page, size_t physical_page)
{
  ArchMemoryMapping mapping = resolveMapping(((uint64)VIRTUAL_TO_PHYSICAL_BOOT(kernel_page_map_level_4) / PAGE_SIZE),
                                             virtual_page);
  PageMapLevel4Entry *pml4 = kernel_page_map_level_4;
  assert(pml4[mapping.pml4i].present);
  PageDirPointerTableEntry *pdpt = (PageDirPointerTableEntry *)getIdentAddressOfPPN(pml4[mapping.pml4i].page_ppn);
  assert(pdpt[mapping.pdpti].pd.present);
  PageDirEntry *pd = (PageDirEntry *)getIdentAddressOfPPN(pdpt[mapping.pdpti].pd.page_ppn);
  assert(pd[mapping.pdi].pt.present);
  PageTableEntry *pt = (PageTableEntry *)getIdentAddressOfPPN(pd[mapping.pdi].pt.page_ppn);
  assert(!pt[mapping.pti].present);
  pt[mapping.pti].writeable = 1;
  pt[mapping.pti].page_ppn = physical_page;
  pt[mapping.pti].present = 1;
  asm volatile("movq %%cr3, %%rax; movq %%rax, %%cr3;" ::: "%rax");
}

void ArchMemory::unmapKernelPage(size_t virtual_page)
{
  ArchMemoryMapping mapping = resolveMapping(((uint64)VIRTUAL_TO_PHYSICAL_BOOT(kernel_page_map_level_4) / PAGE_SIZE),
                                             virtual_page);
  PageMapLevel4Entry *pml4 = kernel_page_map_level_4;
  assert(pml4[mapping.pml4i].present);
  PageDirPointerTableEntry *pdpt = (PageDirPointerTableEntry *)getIdentAddressOfPPN(pml4[mapping.pml4i].page_ppn);
  assert(pdpt[mapping.pdpti].pd.present);
  PageDirEntry *pd = (PageDirEntry *)getIdentAddressOfPPN(pdpt[mapping.pdpti].pd.page_ppn);
  assert(pd[mapping.pdi].pt.present);
  PageTableEntry *pt = (PageTableEntry *)getIdentAddressOfPPN(pd[mapping.pdi].pt.page_ppn);
  assert(pt[mapping.pti].present);
  pt[mapping.pti].present = 0;
  pt[mapping.pti].writeable = 0;
  PageManager::instance()->freePPN(pt[mapping.pti].page_ppn);
  asm volatile("movq %%cr3, %%rax; movq %%rax, %%cr3;" ::: "%rax");
}

PageMapLevel4Entry *ArchMemory::getRootOfKernelPagingStructure()
{
  return kernel_page_map_level_4;
}

void ArchMemory::flushTlb()
{
  asm volatile("movq %%cr3, %%rax; movq %%rax, %%cr3;" ::: "%rax", "memory");
}

void ArchMemory::setPid(int pid)
{
  pid_ = pid;
}

size_t ArchMemory::determineNeededPages(ArchMemory const &src)
{
  size_t total_pages_to_allocate = 0;

  PageMapLevel4Entry *src_pml4 = (PageMapLevel4Entry *)getIdentAddressOfPPN(src.page_map_level_4_);

  for (size_t pml4_i = 0; pml4_i < PAGE_MAP_LEVEL_4_ENTRIES / 2; pml4_i++)
  {
    if (src_pml4[pml4_i].present)
    {
      total_pages_to_allocate++;

      PageDirPointerTableEntry *srcPDPT = (PageDirPointerTableEntry *)getIdentAddressOfPPN(src_pml4[pml4_i].page_ppn);
      for (size_t PDPT_i = 0; PDPT_i < PAGE_DIR_POINTER_TABLE_ENTRIES; PDPT_i++)
      {
        if (srcPDPT[PDPT_i].pd.present)
        {
          total_pages_to_allocate++;

          PageDirEntry *srcPD = (PageDirEntry *)getIdentAddressOfPPN(srcPDPT[PDPT_i].pd.page_ppn);
          for (size_t PD_i = 0; PD_i < PAGE_DIR_ENTRIES; PD_i++)
          {
            if (srcPD[PD_i].pt.present)
            {
              total_pages_to_allocate++;
            }
          }
        }
      }
    }
  }

  return total_pages_to_allocate;
}

int ArchMemory::determineNeededPagesAllocPpn(size_t vpn)
{

  ArchMemoryMapping m = resolveMapping(vpn);

  int counter = 0;

  if (m.pdpt_ppn == 0)
  {
    counter++;
  }
  if (m.pd_ppn == 0)
  {
    counter++;
  }
  if (m.pt_ppn == 0)
  {
    counter++;
  }

  debug(LOADER, " counter :%d", counter);

  return counter;
}
