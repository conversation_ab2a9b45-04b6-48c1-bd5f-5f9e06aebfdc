/**
 * this linker script tells ld how to link and which symbols to add to the
 * kernel.x binary
 */
 
/* let the linker use its 'native' format  (ELF/COFF/PE) */
OUTPUT_FORMAT("elf32-i386")
/* no leading underscore for symbols handled in asm: */
ENTRY(entry)
LS_Phys = 0x100000; /* 1 meg */
/*LS_Virt = 0x100000; /* 1 meg */
/* use this line instead when paging works properly! */
LS_Virt = 0x80000000; /* 2 gig + 1 meg due to 4m pages for kernel area*/
SECTIONS
{
  .text LS_Virt : AT(LS_Phys)
  {
    PROVIDE(kernel_start_address = ABSOLUTE(.));

    LS_Code = .;

    text_start_address = .;
    *(.mboot)
    *(.text)
    *(.text.*)
    text_end_address = .;
    *(.gnu.linkonce.r.*)
    ro_data_start_address = .;
    *(.rodata*)
    ro_data_end_address = .;
    . = ALIGN(4096);

  }

  .data  ALIGN(4096) : AT(LS_Phys + (LS_Data - LS_Code))
  {
    LS_Data = .;
    data_start_address = .;
    *(.data)
    . = ALIGN(4096);
    *(.gdt_stuff)
    data_end_address = .;
    . = ALIGN(4096);
  }

  .bss : AT(LS_Phys + (LS_Bss - LS_Code))
  {
    LS_Bss = .;
    bss_start_address = .;
    *(.bss)
    . = ALIGN(4096);
    *(COMMON) /* common symbols, usually placed in .bss */
    bss_end_address = .;

  }

  .stab : AT(LS_Phys + (LS_Stab - LS_Code))
  {
    LS_Stab = .;
    stab_start_address_nr = .;
    *(.stab)
    stab_end_address_nr = .;
    . = ALIGN(4096);
  }
  

  .stabstr : AT(LS_Phys + (LS_Stabstr - LS_Code))
  {
    LS_Stabstr = .;
    stabstr_start_address_nr = .;
    *(.stabstr)
    stabstr_end_address_nr = .;
    . = ALIGN(4096);

    PROVIDE(kernel_end_address = .);
  }

}