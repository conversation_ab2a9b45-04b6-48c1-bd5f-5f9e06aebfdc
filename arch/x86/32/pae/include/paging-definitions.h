#pragma once

#include "types.h"


#define PAGE_DIRECTORY_POINTER_TABLE_ENTRIES 4
#define PAGE_DIRECTORY_ENTRIES 512
#define PAGE_TABLE_ENTRIES 512
#define PAGE_SIZE 4096
#define PAGE_INDEX_OFFSET_BITS 12

#define PHYSICAL_OFFSET     0xC0000000


// Constants for page fault handling
#define ERROR_MASK            0x00000007
#define PAGE_FAULT_USER       0x00000004
#define PAGE_FAULT_WRITEABLE  0x00000002
#define PAGE_FAULT_PRESENT    0x00000001


typedef struct
{
  size_t present                    :1;
  size_t reserved_1                 :2;  // must be 0
  size_t write_through              :1;
  size_t cache_disabled             :1;
  size_t reserved_2                 :4;  // must be 0
  size_t ignored_3                  :1;
  size_t ignored_2                  :1;
  size_t ignored_1                  :1;
  size_t page_directory_ppn         :24; // MAXPHYADDR (36) - 12
  size_t reserved_3                 :28; // must be 0
} __attribute__((__packed__)) PageDirPointerTableEntry;

static_assert(sizeof(PageDirPointerTableEntry) == 8, "PageDirPointerTableEntry is not 64 bit");

struct PageDirPageTableEntry
{
  size_t present                   :1;
  size_t writeable                 :1;
  size_t user_access               :1;
  size_t write_through             :1;
  size_t cache_disabled            :1;
  size_t accessed                  :1;
  size_t ignored_5                 :1;
  size_t size                      :1;
  size_t ignored_4                 :1;
  size_t ignored_3                 :1;
  size_t ignored_2                 :1;
  size_t ignored_1                 :1;
  size_t page_table_ppn            :24; // MAXPHYADDR (36) - 12
  size_t reserved_2                :27; // must be 0
  size_t execution_disabled        :1;
} __attribute__((__packed__));

static_assert(sizeof(PageDirPageTableEntry) == 8, "PageDirPageTableEntry is not 64 bit");

struct PageDirPageEntry
{
  size_t present                   :1;
  size_t writeable                 :1;
  size_t user_access               :1;
  size_t write_through             :1;
  size_t cache_disabled            :1;
  size_t accessed                  :1;
  size_t dirty                     :1;
  size_t size                      :1;
  size_t global_page               :1;
  size_t ignored_3                 :1;
  size_t ignored_2                 :1;
  size_t ignored_1                 :1;

  size_t pat                       :1;
  size_t reserved                  :8;
  size_t page_ppn                  :15; // MAXPHYADDR (36) - 21
  size_t reserved_2                :27; // must be 0
  size_t execution_disabled        :1;
} __attribute__((__packed__));

static_assert(sizeof(PageDirPageEntry) == 8, "PageDirPageEntry is not 64 bit");

typedef union
{
  struct PageDirPageTableEntry pt;
  struct PageDirPageEntry page;
} __attribute__((__packed__)) PageDirEntry;

typedef struct
{
  size_t present                   :1;
  size_t writeable                 :1;
  size_t user_access               :1;
  size_t write_through             :1;
  size_t cache_disabled            :1;
  size_t accessed                  :1;
  size_t dirty                     :1;
  size_t pat                       :1;
  size_t global_page               :1;
  size_t ignored_3                 :1;
  size_t ignored_2                 :1;
  size_t ignored_1                 :1;
  size_t page_ppn                  :24; // MAXPHYADDR (36) - 12
  size_t reserved_2                :27; // must be 0
  size_t execution_disabled        :1;
} __attribute__((__packed__)) PageTableEntry;

static_assert(sizeof(PageTableEntry) == 8, "PageTableEntry is not 64 bit");
