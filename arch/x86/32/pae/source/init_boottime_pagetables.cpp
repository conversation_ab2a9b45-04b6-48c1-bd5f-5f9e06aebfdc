#include "types.h"
#include "paging-definitions.h"
#include "offsets.h"
#include "multiboot.h"
#include "ArchCommon.h"
#include "assert.h"
#include "kprintf.h"
#include "ArchMemory.h"

extern void* kernel_end_address;

extern "C" void initialiseBootTimePaging()
{
  uint32 i;

  PageDirPointerTableEntry *pdpt_start = (PageDirPointerTableEntry*) VIRTUAL_TO_PHYSICAL_BOOT(
      (pointer )kernel_page_directory_pointer_table);
  PageDirEntry *pde_start = (PageDirEntry*) VIRTUAL_TO_PHYSICAL_BOOT((pointer )kernel_page_directory);
  for (i = 0; i < 4; ++i)
  {
    pdpt_start[i].page_directory_ppn = ((uint32) pde_start) / PAGE_SIZE + i;
    pdpt_start[i].present = 1;
  }

  PageTableEntry *pte_start = (PageTableEntry*) VIRTUAL_TO_PHYSICAL_BOOT((pointer )kernel_page_tables);

  uint32 kernel_last_page = VIRTUAL_TO_PHYSICAL_BOOT((pointer)&kernel_end_address) / PAGE_SIZE;

  // we do not have to clear the pde since its in the bss
  for (i = 0; i < 10; ++i)
  {
    pde_start[i].page.present = 1;
    pde_start[i].page.writeable = 1;
    pde_start[i].page.size = 1;
    pde_start[i].page.page_ppn = i;
  }

  for (i = 0; i < 8; ++i)
  {
    pde_start[i + 1024].pt.present = 1;
    pde_start[i + 1024].pt.writeable = 1;
    pde_start[i + 1024].pt.page_table_ppn = ((pointer) &pte_start[PAGE_TABLE_ENTRIES * i]) / PAGE_SIZE;
  }

  // ok, we currently only fill in mappings for the first 4 megs (aka one page table)
  // we do not have to zero out the other page tables since they're already empty
  // thanks to the bss clearance.

  // update, from now on, all pages up to the last page containing only rodata
  // will be write protected.

  extern uint32 ro_data_end_address;

  uint32 last_ro_data_page = VIRTUAL_TO_PHYSICAL_BOOT((pointer)&ro_data_end_address) / PAGE_SIZE;

  // ppns are 1mb = 256 pages after vpns...
  for (i = 0; i < last_ro_data_page - 256; ++i)
  {
    pte_start[i].present = 1;
    pte_start[i].writeable = 0;
    pte_start[i].page_ppn = i + 256;
  }
  for (; i < kernel_last_page - 256; ++i)
  {
    pte_start[i].present = 1;
    pte_start[i].writeable = 1;
    pte_start[i].page_ppn = i + 256;
  }

  if (ArchCommon::haveVESAConsole(0))
  {
    for (i = 0; i < 8; ++i)
    {
      pde_start[1528 + i].page.present = 1;
      pde_start[1528 + i].page.writeable = 1;
      pde_start[1528 + i].page.size = 1;
      pde_start[1528 + i].page.cache_disabled = 1;
      pde_start[1528 + i].page.write_through = 1;
      pde_start[1528 + i].page.page_ppn = (ArchCommon::getVESAConsoleLFBPtr(0) / (1024 * 1024 * 2)) + i;
    }
  }

  // identity mapping
  for (i = 0; i < PAGE_DIRECTORY_ENTRIES; ++i)
  {
    pde_start[i + PAGE_DIRECTORY_ENTRIES * 3].page.present = 1;
    pde_start[i + PAGE_DIRECTORY_ENTRIES * 3].page.writeable = 1;
    pde_start[i + PAGE_DIRECTORY_ENTRIES * 3].page.size = 1;
    pde_start[i + PAGE_DIRECTORY_ENTRIES * 3].page.page_ppn = i;
  }
}

extern "C" void removeBootTimeIdentMapping()
{
  uint32 i;

  kernel_page_directory_pointer_table[0].present = 0;
  kernel_page_directory_pointer_table[1].present = 0;
  for (i = 0; i < 10; ++i)
  {
    kernel_page_directory[i].page.present = 0;
  }
}
