set(KERNEL_BINARY kernel.x)

set(ARCH_X86_32_PAE_KERNEL_CFLAGS -m32 -O0 -gstabs2 -Wall -Wextra -Werror -Wno-error=format -nostdinc -nostdlib -nostartfiles -nodefaultlibs -fno-builtin -fno-exceptions -fno-stack-protector -mno-mmx -mno-sse2 -mno-sse3 ${NOPICFLAG})

set(KERNEL_CMAKE_CXX_FLAGS ${CMAKE_CXX_FLAGS} -std=gnu++11 -Wno-nonnull-compare -nostdinc++ -fno-rtti ${ARCH_X86_32_PAE_KERNEL_CFLAGS})
set(KERNEL_CMAKE_C_FLAGS   ${CMAKE_C_FLAGS}   -std=gnu11 ${ARCH_X86_32_PAE_KERNEL_CFLAGS})


set(ARCH_LD_ARGUMENTS -m32 -Wl,--build-id=none -Wl,-z,max-page-size=0x1000 -Wl,-melf_i386 -nostdinc -nostdlib -nodefaultlibs)
set(KERNEL_LD_ARGUMENT ${ARCH_LD_ARGUMENTS} -mcmodel=kernel ${NOPIEFLAG})
set(ARCH_APPEND_LD_ARGUMENTS )


MACRO(ARCH2OBJ ARCHOBJ_LIBNAME LIBRARY_NAME)
ENDMACRO(ARCH2OBJ)

set(KERNEL_IMAGE_OBJCOPY )

# kvm: Run kvm in non debugging mode
add_custom_target(kvm
  COMMAND qemu-system-i386 -m 8M -cpu kvm32 -drive file=${HDD_IMAGE},index=0,media=disk -debugcon stdio -no-reboot
  COMMENT "Executing `qemu-system-i386 -m 8M -cpu kvm32 -drive file=${HDD_IMAGE},index=0,media=disk -debugcon stdio -no-reboot`"
  WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
  COMMAND reset -I
  )

# qemu: Run qemu in non debugging mode
add_custom_target(qemu
	COMMAND	qemu-system-i386 -m 8M -cpu qemu32 -drive file=${HDD_IMAGE},index=0,media=disk -debugcon stdio -no-reboot
	COMMENT "Executing `qemu-system-i386 -m 8M -cpu qemu32 -drive file=${HDD_IMAGE},index=0,media=disk -debugcon stdio -no-reboot`"
	WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
	COMMAND reset -I
	)

# qemugdb: Run qemu in non debugging mode
add_custom_target(qemugdb
	COMMAND	qemu-system-i386 -s -S -m 8M -drive file=${HDD_IMAGE},index=0,media=disk -debugcon stdio -no-reboot
	COMMENT "Executing `gdb qemu-system-i386 -s -S -m 8M -drive file=${HDD_IMAGE},index=0,media=disk -debugcon stdio -no-reboot on localhost:1234`"
	WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
	COMMAND reset -I
	)

# qemutacos: Run qemu in pipe monitor mode for tacos
add_custom_target(qemutacos
  COMMAND qemu-system-i386 -hda ${HDD_IMAGE} -m 8M -snapshot -monitor pipe:qemu -nographic -debugcon stdio
  COMMENT "Executing `qemu-system-i386 -hda ${HDD_IMAGE} -m 8M -snapshot -monitor pipe:qemu -nographic -debugcon stdio -no-reboot`"
  WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
  )


