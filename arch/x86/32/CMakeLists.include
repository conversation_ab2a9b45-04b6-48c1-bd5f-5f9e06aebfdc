set(KERNEL_BINARY kernel.x)

set(ARCH_X86_32_KERNEL_CFLAGS -m32 -O0 -gstabs2 -Wall -Wextra -Werror -Wno-error=format -Wno-nonnull-compare -nostdinc -nostdlib -nostartfiles -nodefaultlibs -fno-builtin -fno-exceptions -fno-stack-protector -ffreestanding -mno-red-zone -mno-mmx -mno-sse2 -mno-sse3 -mno-3dnow ${NOPICFLAG})

set(KERNEL_CMAKE_CXX_FLAGS ${CMAKE_CXX_FLAGS} -std=gnu++17 -nostdinc++ -fno-rtti ${ARCH_X86_32_KERNEL_CFLAGS})
set(KERNEL_CMAKE_C_FLAGS   ${CMAKE_C_FLAGS}   -std=gnu11 ${ARCH_X86_32_KERNEL_CFLAGS})

set(ARCH_LD_ARGUMENTS -m32 -Wl,--build-id=none -Wl,-z,max-page-size=0x1000 -Wl,-melf_i386 -nostdinc -nostdlib -nodefaultlibs)
set(KERNEL_LD_ARGUMENT ${ARCH_LD_ARGUMENTS} -mcmodel=kernel ${NOPIEFLAG})
set(ARCH_APPEND_LD_ARGUMENTS )

MACRO(ARCH2OBJ ARCHOBJ_LIBNAME LIBRARY_NAME)
ENDMACRO(ARCH2OBJ)


set(KERNEL_IMAGE_OBJCOPY COMMAND ${OBJCOPY_EXECUTABLE} ${PROJECT_BINARY_DIR}/kernel.x --strip-unneeded ${PROJECT_BINARY_DIR}/kernel.x)
if ("${DEBUG}" STREQUAL "1")
  set(KERNEL_IMAGE_OBJCOPY )
endif()

set(AVAILABLE_MEMORY 8M)

set(QEMU_BIN qemu-system-i386)
set(QEMU_FLAGS_COMMON -m ${AVAILABLE_MEMORY} -drive file=${HDD_IMAGE},index=0,media=disk -debugcon stdio -no-reboot)
string(REPLACE ";" " " QEMU_FLAGS_COMMON_STR "${QEMU_FLAGS_COMMON}")

add_custom_target(kvm
  COMMAND ${QEMU_BIN} ${QEMU_FLAGS_COMMON} -cpu kvm32
  COMMENT "Executing `${QEMU_BIN} ${QEMU_FLAGS_COMMON_STR} -cpu kvm32`"
  WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
  COMMAND reset -I
  )

# qemu: Run qemu in non debugging mode
add_custom_target(qemu
	COMMAND ${QEMU_BIN} ${QEMU_FLAGS_COMMON} -cpu qemu32
	COMMENT "Executing `${QEMU_BIN} ${QEMU_FLAGS_COMMON_STR} -cpu qemu32`"
	WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
	COMMAND reset -I
	)

# qemugdb: Run qemu in debugging mode
add_custom_target(qemugdb
	COMMAND	${QEMU_BIN} ${QEMU_FLAGS_COMMON} -no-kvm -s -S
	COMMENT "Executing `gdb ${QEMU_BIN} ${QEMU_FLAGS_COMMON_STR} -no-kvm -s -S on localhost:1234`"
	WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
	COMMAND reset -I
	)

# qemutacos: Run qemu in pipe monitor mode for tacos
add_custom_target(qemutacos
  COMMAND ${QEMU_BIN} -hda ${HDD_IMAGE} -m ${AVAILABLE_MEMORY} -snapshot -monitor pipe:qemu -nographic -debugcon /dev/stdout
  COMMENT "Executing `qemu-system-i386 -hda ${HDD_IMAGE} -m 8M -snapshot -monitor pipe:qemu -nographic -debugcon /dev/stdout`"
  WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
  )

# net: Run qemu with network support, make sure to run after target "net-init"
add_custom_target(net
  COMMAND sudo ${QEMU_BIN} ${QEMU_FLAGS_COMMON} -netdev bridge,id=hn0 -device rtl8139,netdev=hn0,id=nic1 -cpu qemu32
  COMMENT "Executing `sudo ${QEMU_BIN} ${QEMU_FLAGS_COMMON_STR} -netdev bridge,id=hn0 -device rtl8139,netdev=hn0,id=nic1 -cpu qemu32`"
  WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
  COMMAND reset -I
  )

# net-init: Initializes br0 for target "net"
add_custom_target(net-init
  COMMAND sudo ${PROJECT_SOURCE_DIR}/utils/netinit.sh
  COMMENT "Executing `sudo utils/netinit.sh`"
  WORKING_DIRECTORY ${PROJECT_BINARY_DIR}
  COMMAND reset -I
  )
