#include "types.h"
#include "debug.h"
#include "debug_bochs.h"
#include "multiboot.h"
#include "offsets.h"
#include "ArchCommon.h"
#include "ArchMemory.h"
#include "paging-definitions.h"
#include "kstring.h"

#define PRINT(X) do { if (A_BOOT & OUTPUT_ENABLED) { writeLine2Bochs(VIRTUAL_TO_PHYSICAL_BOOT(X)); } } while (0)

#define MULTIBOOT_PAGE_ALIGN (1<<0)
#define MULTIBOOT_MEMORY_INFO (1<<1)
#define MULTIBOOT_WANT_VESA (1<<2)
#define MULTIBOOT_HEADER_MAGIC (0x1BADB002)
#define MULTIBOOT_HEADER_FLAGS (MULTIBOOT_PAGE_ALIGN | MULTIBOOT_MEMORY_INFO | MULTIBOOT_WANT_VESA)
#define MULTIBOOT_CHECKSUM (-(MULTIBOOT_HEADER_MAGIC + MULTIBOOT_HEADER_FLAGS))

static const struct {
  uint32 magic = MULTIBOOT_HEADER_MAGIC;
  uint32 flags = MULTIBOOT_HEADER_FLAGS;
  uint32 checksum = MULTIBOOT_CHECKSUM;
  uint32 mode = 0;
  uint32 widht = 800;
  uint32 height = 600;
  uint32 depth = 32;
} mboot __attribute__ ((section (".mboot")));

extern multiboot_info_t* multi_boot_structure_pointer;
extern uint8 bss_start_address;
extern uint8 bss_end_address;
extern uint8 boot_stack[];

extern "C" void parseMultibootHeader();
extern "C" void initialiseBootTimePaging();
extern "C" void startup();

extern "C" void entry()
{
  asm("mov %%ebx,%0": "=m"(*((multiboot_info_t**)VIRTUAL_TO_PHYSICAL_BOOT((pointer)&multi_boot_structure_pointer))));
  PRINT("Booting...\n");

  PRINT("Clearing Framebuffer...\n");
  memset((void*)(ArchCommon::getFBPtr(0)), 0, 80 * 25 * 2);

  PRINT("Clearing BSS...\n");
  memset((void*)VIRTUAL_TO_PHYSICAL_BOOT((pointer)&bss_start_address), 0, (uint32)&bss_end_address - (uint32)&bss_start_address);

  asm("push $2\n"
      "popf\n");

  PRINT("Parsing Multiboot Header...\n");
  parseMultibootHeader();

  PRINT("Initializing Kernel Paging Structures...\n");
  initialiseBootTimePaging();

  if (PAGE_DIRECTORY_ENTRIES == 512)
  {
    PRINT("Enable EFER.NXE...\n");
    asm("mov $0xC0000080,%ecx\n"
        "rdmsr\n"
        "or $0x800,%eax\n"
        "wrmsr\n");
  }

  PRINT("Setting CR3 Register...\n");
  asm("mov %[pd],%%cr3" : : [pd]"r"(VIRTUAL_TO_PHYSICAL_BOOT((pointer)ArchMemory::getRootOfKernelPagingStructure())));

  PRINT("Enable Page Size Extensions...\n");
  uint32 cr4;
  asm("mov %%cr4,%[v]\n" : [v]"=r"(cr4));
  cr4 |= 0x10;
  if (PAGE_DIRECTORY_ENTRIES == 512)
    cr4 |= 0x20;
  asm("mov %[v],%%cr4\n" : : [v]"r"(cr4));

  PRINT("Enable Paging...\n");
  asm("mov %cr0,%eax\n"
      "or $0x80010001,%eax\n"
      "mov %eax,%cr0\n");

  PRINT("Switch to our own stack...\n");
  asm("mov %[v],%%esp\n"
      "mov %%esp,%%ebp\n" : : [v]"i"(boot_stack + 0x4000));

  PRINT("Calling startup()...\n");
  asm("jmp *%%eax" : : "a"(startup));

  PRINT("Returned from startup()? This should never happen.\n");
  asm("hlt");
}
