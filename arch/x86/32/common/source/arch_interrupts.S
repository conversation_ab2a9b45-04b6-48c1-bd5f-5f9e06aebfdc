# ok, this is our main interrupt handling stuff

.code32
.text

.equ KERNEL_DS, 0x10

.macro pushAll
  pushal
  push %ds
  push %es
  movw $KERNEL_DS, %ax
  movw %ax,%es
  movw $KERNEL_DS, %ax
  movw %ax,%ds
.endm

.macro popAll
  pop %es
  pop %ds
  popal
.endm

.extern arch_saveThreadRegisters
.extern arch_saveThreadRegistersForPageFault

.macro irqhandler num
.global arch_irqHandler_\num
.extern irqHandler_\num
.stabs "arch_irqHandler_\num()",36,0,0,arch_irqHandler_\num
arch_irqHandler_\num:
  pushall
  pushl %ebp
  movl %esp,%ebp
  pushl $0
  call arch_saveThreadRegisters
  leave
  call irqHandler_\num
  popall
  iretl
.endm

dummyhandlerscratchvariable:
  .long 0
  .long 0

.extern dummyHandler
.global arch_dummyHandler
arch_dummyHandler:
.rept 128
        call arch_dummyHandlerMiddle
.endr
.extern dummyHandlerMiddle
.global arch_dummyHandlerMiddle
arch_dummyHandlerMiddle:
        pushall
        pushl %ebp
        movl %esp,%ebp
        pushl $0
        call arch_saveThreadRegisters
        leave
        pushl %ebp
        movl %esp,%ebp
        pushl 44(%esp)
        call arch_computeDummyHandler
        mov %eax,%ebx
        leave
        pushl %ebp
        movl %esp,%ebp
        subl $16,%esp
        movl $1, 12(%esp)
        movl 64(%esp),%eax
        movl %eax, 8(%esp)
        movl 60(%esp),%eax
        movl %eax, 4(%esp)
        movl %ebx, 0(%esp)
        call errorHandler
        leave
        popall
        addl $4,%esp
        iretl
        hlt

.extern errorHandler
.macro errorhandler num
.global arch_errorHandler_\num
arch_errorHandler_\num:
        pushall
        pushl %ebp
        movl %esp,%ebp
        pushl $0
        call arch_saveThreadRegisters
        leave
        pushl %ebp
        movl %esp,%ebp
        subl $16,%esp
        movl $0, 12(%esp)
        movl 64(%esp),%eax
        movl %eax, 8(%esp)
        movl 60(%esp),%eax
        movl %eax, 4(%esp)
        movl $\num,0(%esp)
        call errorHandler
        leave
        popall
        iretl
        hlt
.endm

.macro errorhandlerWithCode num
.global arch_errorHandler_\num
arch_errorHandler_\num:
        pushall
        pushl %ebp
        movl %esp,%ebp
        pushl $1
        call arch_saveThreadRegisters
        leave
        pushl %ebp
        movl %esp,%ebp
        subl $16,%esp
        movl $0, 12(%esp)
        movl 68(%esp),%eax
        movl %eax, 8(%esp)
        movl 64(%esp),%eax
        movl %eax, 4(%esp)
        movl $\num,0(%esp)
        call errorHandler
        leave
        popall
        addl $4,%esp
        iretl
        hlt
.endm


.extern pageFaultHandler
.global arch_pageFaultHandler
.stabs "arch_pageFaultHandler()",36,0,0,arch_pageFaultHandler
arch_pageFaultHandler:
  #we are already on a new stack because a privliedge switch happened
  pushall
  pushl %ebp
  movl %esp,%ebp
  pushl $1
  call arch_saveThreadRegisters
  leave
  pushl %ebp
  movl %esp,%ebp
  subl $8,%esp
  movl 52(%esp),%eax        # error cd
  movl %eax, 4(%esp)
  movl %cr2, %eax           # page fault address
  movl %eax, 0(%esp)
  call pageFaultHandler
  leave
  popall
  addl $4,%esp
  iretl
  hlt


.irp num,0,1,3,4,6,9,11,14,15,65
irqhandler \num
.endr

.irp num,8,10,11,12,13,14,17
errorhandlerWithCode \num
.endr

.irp num,0,4,5,6,7,9,16,18,19
errorhandler \num
.endr

.global arch_syscallHandler
.extern syscallHandler
.stabs "arch_syscallHandler",36,0,0,arch_syscallHandler
arch_syscallHandler:
  pushall
  pushl %ebp
  movl %esp,%ebp
  pushl $0
  call arch_saveThreadRegisters
  leave
  call syscallHandler
  hlt
