#include "ArchThreads.h"
#include "ArchMemory.h"
#include "Loader.h"
#include "kprintf.h"
#include "paging-definitions.h"
#include "offsets.h"
#include "Thread.h"
#include "kstring.h"



void ArchThreads::initialise()
{
  currentThreadRegisters = new ArchThreadRegisters{};
}

void ArchThreads::setAddressSpace(Thread *thread, ArchMemory& arch_memory)
{
  thread->kernel_registers_->cr3 = arch_memory.getValueForCR3();
  if (thread->user_registers_)
    thread->user_registers_->cr3 = arch_memory.getValueForCR3();

  if(thread == currentThread)
  {
          asm volatile("movl %[new_cr3], %%cr3\n"
                       ::[new_cr3]"r"(arch_memory.getValueForCR3()));
  }
}

void ArchThreads::createBaseThreadRegisters(ArchThreadRegisters *&info, void* start_function, void* stack)
{
  info = new ArchThreadRegisters{};
  pointer root_of_kernel_paging_structure = VIRTUAL_TO_PHYSICAL_BOOT(((pointer)ArchMemory::getRootOfKernelPagingStructure()));

  info->eflags  = 0x200;
  info->cr3     = root_of_kernel_paging_structure;
  info->esp     = (size_t)stack;
  info->ebp     = (size_t)stack;
  info->eip     = (size_t)start_function;

  /* fpu (=fninit) */
  info->fpu[0] = 0xFFFF037F;
  info->fpu[1] = 0xFFFF0000;
  info->fpu[2] = 0xFFFFFFFF;
  info->fpu[3] = 0x00000000;
  info->fpu[4] = 0x00000000;
  info->fpu[5] = 0x00000000;
  info->fpu[6] = 0xFFFF0000;
}

void ArchThreads::createKernelRegisters(ArchThreadRegisters *&info, void* start_function, void* kernel_stack)
{
  createBaseThreadRegisters(info, start_function, kernel_stack);

  info->cs      = KERNEL_CS;
  info->ds      = KERNEL_DS;
  info->es      = KERNEL_DS;
  info->ss      = KERNEL_SS;
}

void ArchThreads::createUserRegisters(ArchThreadRegisters *&info, void* start_function, void* user_stack, void* kernel_stack)
{
  createBaseThreadRegisters(info, start_function, user_stack);

  info->cs      = USER_CS;
  info->ds      = USER_DS;
  info->es      = USER_DS;
  info->ss      = USER_SS;
  info->esp0    = (size_t)kernel_stack;
}

void ArchThreads::changeInstructionPointer(ArchThreadRegisters *info, void* function)
{
  info->eip = (size_t)function;
}

void ArchThreads::yield()
{
  __asm__ __volatile__("int $65");
}

uint32 ArchThreads::testSetLock(uint32 &lock, uint32 new_value)
{
  return __sync_lock_test_and_set(&lock,new_value);
}

uint32 ArchThreads::atomic_add(uint32 &value, int32 increment)
{
  return __sync_fetch_and_add(&value,increment);
}

int32 ArchThreads::atomic_add(int32 &value, int32 increment)
{
  return __sync_fetch_and_add(&value,increment);
}

uint64 ArchThreads::atomic_add(uint64 &value, int64 increment)
{
  return __sync_fetch_and_add(&value,increment);
}

int64 ArchThreads::atomic_add(int64 &value, int64 increment)
{
  return __sync_fetch_and_add(&value,increment);
}


void ArchThreads::atomic_set(uint32& target, uint32 value)
{
  __atomic_store_n(&(target), value, __ATOMIC_SEQ_CST);
}

void ArchThreads::atomic_set(int32& target, int32 value)
{
  __atomic_store_n(&(target), value, __ATOMIC_SEQ_CST);
}

void ArchThreads::atomic_set(uint64& target, uint64 value)
{
  __atomic_store_n(&(target), value, __ATOMIC_SEQ_CST);
}

void ArchThreads::atomic_set(int64& target, int64 value)
{
  __atomic_store_n(&(target), value, __ATOMIC_SEQ_CST);
}


void ArchThreads::printThreadRegisters(Thread *thread, bool verbose)
{
  printThreadRegisters(thread,0,verbose);
  printThreadRegisters(thread,1,verbose);
}

void ArchThreads::printThreadRegisters(Thread *thread, uint32 userspace_registers, bool verbose)
{
  ArchThreadRegisters *info = userspace_registers?thread->user_registers_:thread->kernel_registers_;
  if (!info)
  {
    kprintfd("%sThread: %18p, has no %s registers. %s\n",userspace_registers?"  User":"Kernel",thread,userspace_registers?"User":"Kernel",userspace_registers?"":"This should never(!) occur. How did you do that?");
  }
  else if (verbose)
  {
    kprintfd("\t\t%sThread: %10p, info: %10p\n"\
             "\t\t\t eax: %10x  ebx: %10x  ecx: %10x  edx: %10x\n"\
             "\t\t\t esp: %10x  ebp: %10x  esp0 %10x  eip: %10x\n"\
             "\t\t\teflg: %10x  cr3: %10x\n",
             userspace_registers?"  User":"Kernel",thread,info,info->eax,info->ebx,info->ecx,info->edx,info->esp,info->ebp,info->esp0,info->eip,info->eflags,info->cr3);
  }
  else
  {
    kprintfd("\t%sThread %10p: info %10p eax %10x ebp %10x esp %10x esp0 %10x eip %10x cr3 %10x\n",
             userspace_registers?"  User":"Kernel",thread,info,info->eax,info->ebp,info->esp,info->esp0,info->eip,info->cr3);
  }
}

extern "C" void threadStartHack();

void ArchThreads::debugCheckNewThread(Thread* thread)
{
  assert(currentThread);
  ArchThreads::printThreadRegisters(currentThread,false);
  ArchThreads::printThreadRegisters(thread,false);
  assert(thread->kernel_registers_ != 0 && thread->kernel_registers_ != currentThread->kernel_registers_ && "all threads need to have their own register sets");
  assert(thread->kernel_registers_->esp0 == 0 && "kernel register set needs no backup of kernel esp");
  assert(thread->kernel_registers_->esp == thread->kernel_registers_->ebp && "new kernel stack must be empty");
  assert(thread->kernel_registers_->esp != currentThread->kernel_registers_->esp && thread->kernel_registers_->ebp != currentThread->kernel_registers_->ebp && "all threads need their own stack");
  assert(thread->kernel_registers_->cr3 < 0x80000000 && "cr3 contains the physical page dir address");
  if (thread->user_registers_ == 0)
    return;
  assert(thread->kernel_registers_->eip == 0 && "user threads should not start execution in kernel mode");
  assert(thread->switch_to_userspace_ == 1 && "new user threads must start in userspace");
  assert(thread->kernel_registers_->esp == thread->user_registers_->esp0 && "esp0 should point to kernel stack");
  assert(thread->kernel_registers_->cr3 == thread->user_registers_->cr3 && "user and kernel part of a thread need to have the same page dir");
  assert(thread->kernel_registers_->cr3 == thread->loader_->arch_memory_.getValueForCR3() && "thread and loader need to have the same page dir");
  assert(thread->user_registers_->eip != 0 && "user eip needs to be valid... execution will start there");
  if (currentThread->user_registers_ == 0)
    return;
  assert(currentThread->user_registers_->esp0 != thread->user_registers_->esp0 && "no 2 threads may have the same esp0 value");
}
