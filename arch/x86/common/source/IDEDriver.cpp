#include "IDEDriver.h"

#include "BDManager.h"
#include "BDVirtualDevice.h"
#include "ATADriver.h"
#include "ports.h"
#include "kstring.h"
#include "ArchInterrupts.h"
#include "kprintf.h"

uint32 IDEDriver::doDeviceDetection()
{
  uint32 jiffies = 0;
  uint16 base_port = 0x1F0;
  uint16 base_regport = 0x3F6;
  uint8 cs = 0;
  uint8 sc;
  uint8 sn;
  uint8 devCtrl;

  uint8 ata_irqs[4] =
  {
  14, 15, 11, 9
  };

  // setup register values
  devCtrl = 0x00; // please use interrupts

  // assume there are no devices
  debug(IDE_DRIVER, "doDetection:%d\n", cs);

  for (cs = 0; cs < 4; cs++)
  {
    char name[5];
    name[0] = 'i';
    name[1] = 'd';
    name[2] = 'e';
    name[3] = cs + 'a';
    name[4] = '\0';

    debug(IDE_DRIVER, "doDetection:Detecting IDE DEV: %s\n", name);

    if (cs > 1)
    {
      base_port = 0x170;
      base_regport = 0x376;
    }

    outportbp(base_regport, devCtrl); // init the device with interupts

    uint8 value = (cs % 2 == 0 ? 0xA0 : 0xB0);
    uint16 bpp6 = base_port + 6;
    uint16 bpp2 = base_port + 2;
    uint16 bpp3 = base_port + 3;

    outportb(bpp6, value);
    outportb(0x80, 0x00);

    outportb(bpp2, 0x55);
    outportb(0x80, 0x00);
    outportb(bpp3, 0xAA);
    outportb(0x80, 0x00);
    outportb(bpp2, 0xAA);
    outportb(0x80, 0x00);
    outportb(bpp3, 0x55);
    outportb(0x80, 0x00);
    outportb(bpp2, 0x55);
    outportb(0x80, 0x00);
    outportb(bpp3, 0xAA);
    outportb(0x80, 0x00);

    sc = inportb(bpp2);
    outportb(0x80, 0x00);
    sn = inportb(bpp3);
    outportb(0x80, 0x00);

    if ((sc == 0x55) && (sn == 0xAA))
    {
      outportbp(base_regport, devCtrl | 0x04); // RESET
      outportbp(base_regport, devCtrl);

      jiffies = 0;
      while (!(inportbp(base_port + 7) & 0x58) && jiffies++ < IO_TIMEOUT)
        ArchInterrupts::yieldIfIFSet();

      if (jiffies >= IO_TIMEOUT)
        debug(IDE_DRIVER, "doDetection: Still busy after reset!\n");
      else
      {
        outportbp(base_port + 6, (cs % 2 == 0 ? 0xA0 : 0xB0));

        jiffies = 0;
        while(inportbp(base_port + 7) & 0x80 && jiffies++ < IO_TIMEOUT);

        uint8 c1 = inportbp(base_port + 2);
        uint8 c2 = inportbp(base_port + 3);

        debug(IDE_DRIVER, "c1 = %x, c2 = %x\n", c1, c2);
        if (c1 != 0x01 && c2 != 0x01)
          debug(IDE_DRIVER, "doDetection: Not found after reset ! \n");
        else
        {
          uint8 c3 = inportbp(base_port + 7);
          uint8 c4 = inportbp(base_port + 4);
          uint8 c5 = inportbp(base_port + 5);

          if (((c4 == 0x14) && (c5 == 0xEB)) || ((c4 == 0x69) && (c5 == 0x96)))
          {
            debug(IDE_DRIVER, "doDetection: Found ATAPI ! \n");
            debug(IDE_DRIVER, "doDetection: port: %4X, drive: %d \n", base_port, cs % 2);

            debug(IDE_DRIVER, "doDetection: CDROM not supported \n");

            // CDROM hook goes here
            //
            // char *name = "ATAX0";
            // name[3] = cs + '0';
            // drv = new CROMDriver ( base_port, cs % 2 );
            // BDVirtualDevice *bdv = new
            // BDVirtualDevice( drv, 0, drv->getNumSectors(),
            // drv->getSectorSize(), name, true);
            // BDManager::getInstance()->addDevice( bdv );

          }
          else
          {
            if (c3 != 0)
            {
              if ((c4 == 0x00) && (c5 == 0x00))
              {
                debug(IDE_DRIVER, "doDetection: Found PATA ! \n");
                debug(IDE_DRIVER, "doDetection: port: %4X, drive: %d \n", base_port, cs % 2);

                ATADriver *drv = new ATADriver(base_port, cs % 2, ata_irqs[cs]);
                BDVirtualDevice *bdv = new BDVirtualDevice(drv, 0, drv->getNumSectors(), drv->getSectorSize(), name,
                                                           true);

                BDManager::getInstance()->addVirtualDevice(bdv);
                processMBR(drv, 0, drv->SPT, name);
              }
              else if ((c4 == 0x3C) && (c5 == 0xC3))
              {
                debug(IDE_DRIVER, "doDetection: Found SATA device! \n");
                debug(IDE_DRIVER, "doDetection: port: %4X, drive: %d \n", base_port, cs % 2);

                // SATA hook
                // drv = new SATADriver ( base_port, cs % 2 );

                debug(IDE_DRIVER, "doDetection: Running SATA device as PATA in compatibility mode! \n");

                ATADriver *drv = new ATADriver(base_port, cs % 2, ata_irqs[cs]);

                BDVirtualDevice *bdv = new BDVirtualDevice(drv, 0, drv->getNumSectors(), drv->getSectorSize(), name,
                                                           true);

                BDManager::getInstance()->addVirtualDevice(bdv);

                processMBR(drv, 0, drv->SPT, name);
              }
            }
            else
            {
              debug(IDE_DRIVER, "doDetection: Unknown harddisk!\n");
            }
          }
        }
      }
    }
    else
    {
      debug(IDE_DRIVER, "doDetection: Not found!\n");
    }

  }

  // TODO : verify if the device is ATA and not ATAPI or SATA
  return 0;
}

int32 IDEDriver::processMBR(BDDriver* drv, uint32 sector, uint32 SPT, const char *name)
{
  uint32 offset = 0, numsec = 0;
  uint16 buff[256]; // read buffer
  debug(IDE_DRIVER, "processMBR:reading MBR\n");

  static uint32 part_num = 0;
//   char part_num_str[2];
//   char part_name[10];

  uint32 read_res = ((ATADriver*)drv)->rawReadSector(sector, 1, (void *) buff);

  if (read_res != 0)
  {
    debug(IDE_DRIVER, "processMBR: drv returned BD_ERROR\n");
    return -1;
  }

  MBR *mbr = (MBR *) buff;

  if (mbr->signature == 0xAA55)
  {
    debug(IDE_DRIVER, "processMBR: | Valid PC MBR | \n");
    FP * fp = (FP *) mbr->parts;
    uint32 i;
    for (i = 0; i < 4; i++, fp++)
    {
      switch (fp->systid)
      {
        case 0x00:
          // do nothing
          break;
        case 0x05: // DOS extended partition
        case 0x0F: // Windows extended partition
        case 0x85: // linux extended partition
          debug(IDE_DRIVER, "ext. part. at: %d \n", fp->relsect);
          if (processMBR(drv, sector + fp->relsect, SPT, name) == -1)
            processMBR(drv, sector + fp->relsect - SPT, SPT, name);
          break;
        default:
          // offset = fp->relsect - SPT;
          offset = fp->relsect;
          numsec = fp->numsect;

          char part_name[6];
          strncpy(part_name, name, 4);
          part_name[4] = part_num + '0';
          part_name[5] = 0;
          part_num++;
          BDVirtualDevice *bdv = new BDVirtualDevice(drv, offset, numsec, drv->getSectorSize(), part_name, true);

          // set Partition Type (FileSystem identifier)
          bdv->setPartitionType(fp->systid);

          BDManager::getInstance()->addVirtualDevice(bdv);
          break;
      }
    }
  }
  else
  {
    debug(IDE_DRIVER, "processMBR: | Invalid PC MBR %d | \n", mbr->signature);
    return -1;
  }

  debug(IDE_DRIVER, "processMBR:, done with partitions \n");
  return 0;
}
