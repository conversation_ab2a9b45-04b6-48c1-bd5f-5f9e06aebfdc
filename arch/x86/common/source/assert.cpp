#include "assert.h"
#include "kprintf.h"
#include "panic.h"
#include "debug_bochs.h"
#include "Thread.h"
#include "ArchInterrupts.h"

extern Thread* currentThread;

__attribute__((noreturn)) void pre_new_sweb_assert(const char* condition, uint32 line, const char* file)
{
  system_state = KPANIC;
  char const *error_string = "KERNEL PANIC: Assertion Failed in File:  on Line:      ";
  char line_string[5];
  ArchInterrupts::disableInterrupts();
  writeChar2Bochs('\n');
  writeLine2Bochs(condition);
  writeChar2Bochs('\n');
  writeLine2Bochs(error_string);
  writeChar2Bochs('\n');
  writeLine2Bochs(file);
  writeChar2Bochs('\n');
  if (currentThread != 0)
    currentThread->printBacktrace(false);
  uint8 * fb = (uint8*)0xC00B8000;
  uint32 s=0;
  uint32 i=0;
  for (s=0; s<40; ++s)
  {
    fb[i++] = error_string[s];
    fb[i++] = 0x9f;
  }

  while (file && *file)
  {
    fb[i++] = *file++;
    fb[i++] = 0x9f;
  }

  for (s=40; s<54; ++s)
  {
    fb[i++] = error_string[s];
    fb[i++] = 0x9f;
  }

  i-=4;
  for (s=0; s < (sizeof(line_string) - 1); ++s)
  {
    line_string[s]=' ';
  }
  line_string[s]='\0';
  while (line>0)
  {
    fb[i++] = (uint8) ( 0x30 + (line%10) );
    fb[i] = 0x9f;
    line_string[--s] = ( 0x30 + (line%10) );
    i-=3;
    line /= 10;
  }
  writeLine2Bochs(line_string);
  writeChar2Bochs('\n');
  while(1);
  unreachable();
}


void sweb_assert(const char *condition, uint32 line, const char* file)
{
  ArchInterrupts::disableInterrupts();
  static bool in_assert = false;
  system_state = KPANIC;
  kprintfd("KERNEL PANIC: Assertion %s failed in File:Line %s:%d\n",condition, file, line);
  if (in_assert) {
    kprintfd("PANIC LOOP: How did we get here?\n");
    while(1);
    unreachable();
  }
  in_assert = true;
  if (currentThread != 0)
    currentThread->printBacktrace(false);
  while(1);
  unreachable();
}
