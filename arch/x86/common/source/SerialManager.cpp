#include "ArchSerialInfo.h"
#include "SerialManager.h"
#include "kstring.h"

#include "debug_bochs.h"
#include "kprintf.h"
#include "8259.h"

SerialManager * SerialManager::instance_ = 0;

SerialManager::SerialManager() : num_ports( 0 )
{
}

SerialManager::~SerialManager()
{
}

uint32 SerialManager::get_num_ports()
{
  return num_ports;
}

uint32 SerialManager::do_detection( uint32 is_paging_set_up )
{
  uint16 * bios_sp_table;

  if (is_paging_set_up)
    bios_sp_table = (uint16 *) 0xC0000400;
  else
    bios_sp_table = (uint16 *) 0x00000400;
  uint32 i = 0;

  for( ; i < SC::MAX_ARCH_PORTS; i++, bios_sp_table++ )
  {
    if( *bios_sp_table != 0x00 )
    {
      uint8 sp_name[] = { 's', 'p', (uint8) (num_ports + '1'), '\0' };
      ArchSerialInfo * archInfo = new ArchSerialInfo();
      archInfo->base_port = *bios_sp_table;
      archInfo->uart_type = SC::UART_OLD;
      // UART type detection still missing
      archInfo->irq_num = 4 - i%2;
      serial_ports[ num_ports ] = new SerialPort( (char*) sp_name, *archInfo );
      enableIRQ( archInfo->irq_num );
      num_ports++;
    }
  }
  return num_ports;
}

void SerialManager::service_irq( uint32 irq_num )
{
  for(size_t i = 0;i < num_ports; i++)
    if( serial_ports[ i ]->get_info().irq_num == irq_num )
      serial_ports[ i ]->irq_handler();
}
