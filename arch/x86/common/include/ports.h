#pragma once

#include "types.h"


/**
 * reads 1 byte from the selected I/O port
 * @param port the I/O port number which is read
 *
 */
static inline uint8 inportb(uint16 port)
{
   uint8 return_val;
   __asm__ __volatile__ (
   "inb %1,%0"
   : "=a"(return_val)
   : "d"(port));

   return return_val;
}

/**
 * reads 1 byte from the selected I/O port with slowdown
 * @param port the I/O port number which is read
 *
 */
static inline uint8 inportbp(uint16 port)
{
  uint8 _v;
  asm volatile ("inb %1, %0" : "=a" (_v) : "id" (port));
  asm volatile ("outb %al,$0x80" );
  return _v;
}

/**
 * reads 1 word from the selected I/O port
 * @param port the I/O port number which is read
 *
 */
static inline uint16 inportw(uint16 port)
{
  uint16 _res;
  asm volatile ("inw %1, %0" : "=a" (_res) : "id" (port));
  return _res;
}

/**
 * reads 1 word from the selected I/O port with slowdown
 * @param port the I/O port number which is read
 *
 */
__attribute__((unused))
static uint16 inportwp(uint16 port)
{
  uint16 _res;
  asm volatile ("inw %1, %0" : "=a" (_res) : "id" (port));
  asm volatile ("outb %al,$0x80");
  return _res;
}

/**
 * sends 1 byte of data to the specified I/O port
 * @param port the I/O port number to send data to
 * @param val data value sent to I/O port
 *
 */
static inline void outportb(uint16 port, uint8 val)
{
   __asm__ __volatile__ (
   "outb %b0, %w1"
   :
   : "a"(val), "d"(port));
}

/**
 * sends 1 byte of data to the specified I/O port with slowdown
 * @param port the I/O port number to send data to
 * @param val data value sent to I/O port
 */
static inline void outportbp(uint16 port, uint8 value)
{
  asm volatile ("outb %b0, %1" : : "a" (value), "id" (port));
  asm volatile ("outb %al,$0x80");
}

/**
 * sends 1 word of data to the specified I/O port
 * @param port the I/O port number to send data to
 * @param val data value sent to I/O port
 *
 */
static inline void outportw(uint16 port, uint16 value)
{
    asm volatile ("outw %w0, %1" : : "a" (value), "id" (port));
}

/**
 * sends 1 word of data to the specified I/O port with slowdown
 * @param port the I/O port number to send data to
 * @param val data value sent to I/O port
 */
static inline void outportwp(uint16 port, uint16 value)
{
  asm volatile ("outw %w0, %1" : : "a" (value), "id" (port));
  asm volatile ("outb %al,$0x80");
}

