#pragma once

struct vbe_controller
{
  uint8 signature[4];
  uint16 version;
  uint32 oem_string;
  uint32 capabilities;
  uint32 video_mode;
  uint16 total_memory;
  uint16 oem_software_rev;
  uint32 oem_vendor_name;
  uint32 oem_product_name;
  uint32 oem_product_rev;
  uint8 reserved[222];
  uint8 oem_data[256];
} __attribute__ ((packed));

struct vbe_mode
{
  uint16 mode_attributes;
  uint8 win_a_attributes;
  uint8 win_b_attributes;
  uint16 win_granularity;
  uint16 win_size;
  uint16 win_a_segment;
  uint16 win_b_segment;
  uint32 win_func;
  uint16 bytes_per_scanline;

  // >=1.2
  uint16 x_resolution;
  uint16 y_resolution;
  uint8 x_char_size;
  uint8 y_char_size;
  uint8 number_of_planes;
  uint8 bits_per_pixel;
  uint8 number_of_banks;
  uint8 memory_model;
  uint8 bank_size;
  uint8 number_of_image_pages;
  uint8 reserved0;

  // direct color
  uint8 red_mask_size;
  uint8 red_field_position;
  uint8 green_mask_size;
  uint8 green_field_position;
  uint8 blue_mask_size;
  uint8 blue_field_position;
  uint8 reserved_mask_size;
  uint8 reserved_field_position;
  uint8 direct_color_mode_info;

  // >=2.0
  uint32 phys_base;
  uint32 reserved1;
  uint16 reversed2;

  // >=3.0
  uint16 linear_bytes_per_scanline;
  uint8 banked_number_of_image_pages;
  uint8 linear_number_of_image_pages;
  uint8 linear_red_mask_size;
  uint8 linear_red_field_position;
  uint8 linear_green_mask_size;
  uint8 linear_green_field_position;
  uint8 linear_blue_mask_size;
  uint8 linear_blue_field_position;
  uint8 linear_reserved_mask_size;
  uint8 linear_reserved_field_position;
  uint32 max_pixel_clock;

  uint8 reserved3[189];
} __attribute__ ((packed));

typedef struct multiboot_header
{
   uint32 magic     : 32;
   uint32 flags     : 32;
   uint32 checksum    : 32;
   uint32 header_addr   : 32;
   uint32 load_addr   : 32;
   uint32 load_end_addr   : 32;
   uint32 bss_end_addr    : 32;
   uint32 entry_addr    : 32;
   uint32 mode_type   : 32;
   uint32 width     : 32;
   uint32 height    : 32;
   uint32 depth     : 32;
} __attribute__((__packed__)) multiboot_header_t;

typedef struct elf_section_header_table
{
  uint32 num;
  uint32 size;
  uint32 addr;
  uint32 shndx;
} __attribute__((__packed__)) elf_section_header_table_t;

typedef struct multiboot_info
{
  union
  {
    uint32 flags            : 32;
    struct
    {
      uint32 f_mem          : 1;  // 0
      uint32 f_boot_dev     : 1;  // 1
      uint32 f_cmdline      : 1;  // 2
      uint32 f_mods         : 1;  // 3
      uint32 f_aout_symtab  : 1;  // 4
      uint32 f_elf_shdr     : 1;  // 5
      uint32 f_mmap         : 1;  // 6
      uint32 f_drives       : 1;  // 7
      uint32 f_conf_tab     : 1;  // 8
      uint32 f_bootl_name   : 1;  // 9
      uint32 f_apm          : 1;  // 10
      uint32 f_vbe          : 1;  // 11
      uint32 f_fb           : 1;  // 12
      uint32 f_other        : 19; // 13-31
    } __attribute__((__packed__));
  };
  uint32 mem_lower          : 32;
  uint32 mem_upper          : 32;
  uint32 boot_device        : 32;
  uint32 cmdline            : 32;
  uint32 mods_count         : 32;
  uint32 mods_addr          : 32;
  elf_section_header_table_t elf_sec;
  uint32 mmap_length        : 32;
  uint32 mmap_addr          : 32;
  uint32 drives_length      : 32;
  uint32 drives_addr        : 32;
  uint32 config_table       : 32;
  uint32 boot_loader_name   : 32;
  uint32 apm_table          : 32;
  uint32 vbe_control_info   : 32;
  uint32 vbe_mode_info      : 32;
  uint32 vbe_mode           : 32;
  uint32 vbe_interface_seg  : 32;
  uint32 vbe_interface_off  : 32;
  uint32 vbe_interface_len  : 32;
} __attribute__((__packed__)) multiboot_info_t;

typedef struct module
{
  uint32 mod_start;
  uint32 mod_end;
  uint32 string;
  uint32 reserved;
}__attribute__((__packed__)) module_t;

#define MAX_MEMORY_MAPS 10
#define MAX_MODULE_MAPS 10

typedef struct memory_map
{
  uint32 size;
  uint32 base_addr_low;
  uint32 base_addr_high;
  uint32 length_low;
  uint32 length_high;
  uint32 type;
} __attribute__((__packed__)) memory_map_t;


struct multiboot_remainder
{
  uint32 memory_size;
  uint32 vesa_x_res;
  uint32 vesa_y_res;
  uint32 vesa_bits_per_pixel;
  uint32 have_vesa_console;
  uint32 vesa_lfb_pointer;
  uint32 num_module_maps;

  struct memory_maps
  {
    uint32 used;
    uint64 start_address;
    uint64 end_address;
    uint32 type;
  } __attribute__((__packed__)) memory_maps[MAX_MEMORY_MAPS];

  struct module_maps
  {
    uint32 used;
    uint32 start_address;
    uint32 end_address;
    uint8 name[256];
  } __attribute__((__packed__)) module_maps[MAX_MODULE_MAPS];

}__attribute__((__packed__));

struct mb_offsets {
    uint64 phys;
    uint64 entry;
} __attribute__((__packed__));

