#pragma once

#include "types.h"
#include "paging-definitions.h"

class Console;

class ArchCommon
{
  public:
    static pointer getKernelEndAddress();
    static pointer getFreeKernelMemoryStart();
    static pointer getFreeKernelMemoryEnd();

    /**
     * @param is_paging_set_up inform function that paging is not yet up (=false) and
     * that it should behave accordingly
     */
    static size_t haveVESAConsole(size_t is_paging_set_up = 1);
    static size_t getVESAConsoleHeight();
    static size_t getVESAConsoleWidth();
    static size_t getVESAConsoleBitsPerPixel();

    /**
     * @return a Pointer to the location of the VESA Memory Region
     */
    static pointer getVESAConsoleLFBPtr(size_t is_paging_set_up = 1);

    /**
     * @return a Pointer to the location of the FrameBuffer
     */
    static pointer getFBPtr(size_t is_paging_set_up = 1);

    /**
     * @return number of Useable Memory Regions
     */
    static size_t getNumUseableMemoryRegions();

    /**
     * Reads the Grub MultiBoot-Info and
     * returns the start- and end-address of the Useable Memory Region x
     *
     * @param number of the region-information to parse
     * @param &start_address of Useable Memory Region
     * @param &end_address of Useable Memory Region
     * @param &type of Useable Memory Region
     * @return 1 if region >= number_of_regions, 0 otherwise
     */
    static size_t getUsableMemoryRegion(size_t region, pointer &start_address, pointer &end_address, size_t &type);

    /**
     * @return size_t returns the number of modules loaded by grub
     */
    static size_t getNumModules(size_t is_paging_set_up = 1);

    /**
     * Parses the Grub MultiBoot Info with regard to modules
     *
     * @param num the number of grub-loaded module which this is about
     * @return size_t returns memory start address of module "num"
     */
    static size_t getModuleStartAddress(size_t num, size_t is_paging_set_up = 1);

    /**
     * Parses the Grub MultiBoot Info with regard to modules
     *
     * @param num the number of grub-loaded module which this is about
     * @return size_t returns memory end address of module "num"
     */
    static size_t getModuleEndAddress(size_t num, size_t is_paging_set_up = 1);

    /**
     * Generates the according console depending on the architecture
     * @param count the number of consoles to create
     * @return a pointer to the Console object
     */
    static Console* createConsole(size_t count);

    /**
     * Generates the Debug info depending on the architecture
     */
    static void initDebug();

    /**
     * let the CPU idle, f.e. with the halt statement
     */
    static void idle();

    /**
     * draw a heartbeat character
     */
    static void drawHeartBeat();

    /**
    * draw some infos/statistics
    */
    static void drawStat();
};

