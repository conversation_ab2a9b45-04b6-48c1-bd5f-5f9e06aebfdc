#pragma once

#define IO_TIMEOUT (600000)
#define IRQ0_TIMER_FREQUENCY 0

#include "types.h"

class ArchInterrupts
{
public:
  static void initialise();

  static void enableTimer();
  static void setTimerFrequency(uint32 freq);
  static void disableTimer();

  static void enableKBD();
  static void disableKBD();

  /**
   * Signals EOI to the Interrupt-Controller, so the Controller
   * can resume sending us Interrupts
   *
   * @param number of Interrupt that finished, so we know which Interrupt-Controller
   * to signal
   *
   */
  static void EndOfInterrupt(uint16 number);

  static void enableInterrupts();
  static bool disableInterrupts();

  /**
   * on x86: tests if the IF Flag in EFLAGS is set, aka if the Interrupts are enabled
   *
   * @return bool true if Interrupts are enabled, false otherwise
   */
  static bool testIFSet();

  /**
   * yields if the IF Flag is set, else does hlt
   */
  static void yieldIfIFSet();
};

