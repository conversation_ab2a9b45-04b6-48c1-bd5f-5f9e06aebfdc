/**
 * ElfFormat.h
 */
#pragma once

#include "types.h"
#include "kprintf.h"

class Elf
{
  public:
    static const uint32 EI_NIDENT = 16;

// Elf file types

    static const uint32 ET_NONE = 0;
    static const uint32 ET_REL = 1;
    static const uint32 ET_EXEC = 2;
    static const uint32 ET_DYN = 3;
    static const uint32 ET_CORE = 4;
    static const uint32 ET_LOPROC = 0xff00;
    static const uint32 ET_HIPROC = 0xffff;

// Elf machine types

    static const uint32 EM_NONE = 0;
    static const uint32 EM_M32 = 1;
    static const uint32 EM_SPARC = 2;
    static const uint32 EM_386 = 3;
    static const uint32 EM_68K = 4;
    static const uint32 EM_88K = 5;
    static const uint32 EM_860 = 7;
    static const uint32 EM_MIPS = 8;
    static const uint32 EM_AMD64 = 62;
    static const uint32 EM_AARCH64 = 183;

// Elf version

    static const uint32 EV_NONE = 0;
    static const uint32 EV_CURRENT = 1;

// ELF Magic numbers
// An ELF file should have MAG0,MAG1,MAG2,MAG3
// set to 0x7f,'E','L','F' respectively
//

    static const uint32 EI_MAG0 = 0;
    static const uint32 EI_MAG1 = 1;
    static const uint32 EI_MAG2 = 2;
    static const uint32 EI_MAG3 = 3;
    static const uint32 EI_CLASS = 4;
    static const uint32 EI_DATA = 5;
    static const uint32 EI_VERSION = 6;
    static const uint32 EI_OSABI = 7;
    static const uint32 EI_ABIVERSION = 8;
    static const uint32 EI_PAD = 9;

// ELF CLASSES

    static const uint32 ELFCLASSNONE = 0;
    static const uint32 ELFCLASS32 = 1;
    static const uint32 ELFCLASS64 = 2;

// ELF DATAS

    static const uint32 ELFDATANONE = 0;
    static const uint32 ELFDATA2LSB = 1;
    static const uint32 ELFDATA2MSB = 2;

    typedef uint64 Elf64_Addr;
    typedef uint16 Elf64_Half;
    typedef uint64 Elf64_Off;
    typedef int32 Elf64_Sword;
    typedef uint32 Elf64_Word;
    typedef int64 Elf64_Sxword;
    typedef uint64 Elf64_Xword;

// PHDR SECTION TYPES
    static const Elf64_Word PT_NULL      = 0;
    static const Elf64_Word PT_LOAD      = 1;
    static const Elf64_Word PT_DYNAMIC   = 2;
    static const Elf64_Word PT_INTERP    = 3;
    static const Elf64_Word PT_NOTE      = 4;
    static const Elf64_Word PT_SHLIB     = 5;
    static const Elf64_Word PT_PHDR      = 6;
    static const Elf64_Word PT_LOPROC    = 7;
    static const Elf64_Word PT_HIPROC    = 8;
    static const Elf64_Word PT_GNU_STACK = 9;

    struct sELF64_Ehdr
    {
        uint8 e_ident[EI_NIDENT];
        Elf64_Half e_type;
        Elf64_Half e_machine;
        Elf64_Word e_version;
        Elf64_Addr e_entry;
        Elf64_Off e_phoff;
        Elf64_Off e_shoff;
        Elf64_Word e_flags;
        Elf64_Half e_ehsize;
        Elf64_Half e_phentsize;
        Elf64_Half e_phnum;
        Elf64_Half e_shentsize;
        Elf64_Half e_shnum;
        Elf64_Half e_shstrndx;
    } __attribute__((__packed__));

    struct sELF64_Shdr
    {
        Elf64_Word sh_name;
        Elf64_Word sh_type;
        Elf64_Xword sh_flags;
        Elf64_Addr sh_addr;
        Elf64_Off sh_offset;
        Elf64_Xword sh_size;
        Elf64_Word sh_link;
        Elf64_Word sh_info;
        Elf64_Xword sh_addralign;
        Elf64_Xword sh_entsize;
    } __attribute__((__packed__)) ;

    struct sELF64_Phdr
    {
        Elf64_Word p_type;
        Elf64_Word p_flags;
        Elf64_Off p_offset;
        Elf64_Addr p_vaddr;
        Elf64_Addr p_paddr;
        Elf64_Xword p_filesz;
        Elf64_Xword p_memsz;
        Elf64_Xword p_align;
    } __attribute__((__packed__));

    typedef struct sELF64_Phdr Phdr;
    typedef struct sELF64_Shdr Shdr;
    typedef struct sELF64_Ehdr Ehdr;

    static bool headerCorrect(Ehdr* hdr)
    {
      if (hdr->e_ident[Elf::EI_MAG0]  == 0x7f
          &&  hdr->e_ident[Elf::EI_MAG1]  == 'E'
          &&  hdr->e_ident[Elf::EI_MAG2]  == 'L'
          &&  hdr->e_ident[Elf::EI_MAG3]  == 'F'
          &&  hdr->e_ident[Elf::EI_CLASS] == Elf::ELFCLASS64
          &&  hdr->e_ident[Elf::EI_DATA]  == Elf::ELFDATA2LSB
          &&  hdr->e_type            == Elf::ET_EXEC
          &&  ((hdr->e_machine         == Elf::EM_AMD64)
          ||   (hdr->e_machine         == Elf::EM_AARCH64))
          &&  hdr->e_version         == Elf::EV_CURRENT)
      {
        return true;
      }
      return false;
    }


    static void printElfHeader ( Ehdr &hdr )
    {
      kprintfd("hdr addr: %p\n", &hdr);
#define foobar(f,x) do { kprintfd("hdr " #x ": " #f "\n",hdr. x); } while(0)
      foobar("%x", e_type);
      foobar("%x", e_machine);
      foobar("%x", e_version);
      foobar("%zx", e_entry);
      foobar("%zx", e_phoff);
      foobar("%zx", e_shoff);
      foobar("%x", e_flags);
      foobar("%x", e_ehsize);
      foobar("%x", e_phentsize);
      foobar("%x", e_phnum);
      foobar("%x", e_shentsize);
      foobar("%x", e_shnum);
      foobar("%x", e_shstrndx);
#undef foobar
    }

};
