language: cpp
compiler: gcc
matrix:
  include:
        
    - os: linux
      dist: xenial
      sudo: required
      addons:
        apt:
            update: true
            sources:
                - sourceline: 'ppa:ubuntu-toolchain-r/test'
            packages:
                - cmake
                - gcc-arm-none-eabi
                - binutils-arm-none-eabi
                - gcc-aarch64-linux-gnu
                - g++-aarch64-linux-gnu
                - binutils-aarch64-linux-gnu
                - u-boot-tools
                - gcc-8
                - g++-8
                - qemu-system-x86
                - qemu-system-arm
                - qemu-system-aarch64
                - qemu-utils
      env:
        - CCOMPILER=gcc-8
        - CXXCOMPILER=g++-8

    - os: linux
      dist: xenial
      sudo: required
      addons:
        apt:
            update: true
            sources:
                - sourceline: 'ppa:ubuntu-toolchain-r/test'
            packages:
                - cmake
                - gcc-arm-none-eabi
                - binutils-arm-none-eabi
                - gcc-aarch64-linux-gnu
                - g++-aarch64-linux-gnu
                - binutils-aarch64-linux-gnu
                - u-boot-tools
                - gcc-9
                - g++-9
                - qemu-system-x86
                - qemu-system-arm
                - qemu-system-aarch64
                - qemu-utils
      env:
        - CCOMPILER=gcc-9
        - CXXCOMPILER=g++-9

    - os: linux
      dist: bionic
      sudo: required
      addons:
        apt:
            update: true
            sources:
                - sourceline: 'ppa:ubuntu-toolchain-r/test'
            packages:
                - cmake
                - gcc-arm-none-eabi
                - binutils-arm-none-eabi
                - gcc-aarch64-linux-gnu
                - g++-aarch64-linux-gnu
                - binutils-aarch64-linux-gnu
                - u-boot-tools
                - gcc-10
                - g++-10
                - qemu-system-x86
                - qemu-system-arm
                - qemu-system-aarch64
                - qemu-utils
      env:
        - CCOMPILER=gcc-10
        - CXXCOMPILER=g++-10

    - os: linux
      dist: bionic
      sudo: required
      addons:
        apt:
            update: true
            packages:
                - cmake
                - gcc-arm-none-eabi
                - binutils-arm-none-eabi
                - gcc-aarch64-linux-gnu
                - g++-aarch64-linux-gnu
                - binutils-aarch64-linux-gnu
                - u-boot-tools
                - gcc
                - g++
                - qemu-system-x86
                - qemu-system-arm
                - qemu-system-aarch64
                - qemu-utils
      env:
        - CCOMPILER=gcc
        - CXXCOMPILER=g++

        
install:
  - if [[ -n "$CCOMPILER" ]]; then export CC=$CCOMPILER; fi
  - if [[ -n "$CXXCOMPILER" ]]; then export CXX=$CXXCOMPILER; fi
      
script:
  - cd ..
  - mkdir build
  - cd build
  - cmake ../sweb
  - cp ../sweb/utils/travis_test.sh .
  - timeout 120 ./travis_test.sh x86_64
  - cp ../sweb/utils/travis_test.sh .
  - timeout 120 ./travis_test.sh x86_32
  - cp ../sweb/utils/travis_test.sh .
  - timeout 120 ./travis_test.sh x86_32_pae
  - cp ../sweb/utils/travis_test.sh .
  - timeout 120 ./travis_test.sh arm_rpi2
  - cp ../sweb/utils/travis_test.sh .
  - timeout 120 ./travis_test.sh arm_icp
  - cp ../sweb/utils/travis_test.sh .
  - timeout 120 ./travis_test.sh armv8_rpi3
