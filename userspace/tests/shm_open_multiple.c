#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"
#include "fcntl.h"

int main()
{
  char string[6]= "test\0";
  char string2[6]= "test1\0";
  char string3[6]= "test2\0";
  int fd1= shm_open(string,O_CREAT|O_RDWR,0);
  int fd2 = shm_open("test1\0",O_CREAT|O_RDWR,0);
  int fd3 = shm_open("test2\0",O_CREAT|O_RDWR,0);
 
  printf("%d %d %d\n",fd1,fd2,fd3);
  fd1= shm_open(string,O_CREAT|O_RDWR,0);
  fd2 = shm_open(string2,O_CREAT|O_RDWR,0);
  fd3 = shm_open(string3,O_CREAT|O_RDWR,0);
   printf("%d %d %d",fd1,fd2,fd3);
  return 0;

} 