#include <stdio.h>
#include <string.h>
#include "pthread.h"
#include "fcntl.h"
#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"



int main() {
    const char* filename = "/usr/testfile.txt";
    const size_t size = 4096;

    // Create and truncate file
    int fd = open(filename, O_RDWR|O_CREAT); 

    pid_t pid = fork();
    

    if (pid == 0) {
        // Child process: independently mmap the file
        int cfd = open(filename, O_RDWR);
        

        char* c_map = mmap(NULL, size, PROT_READ | PROT_WRITE, MAP_SHARED, cfd, 0);
        

        close(cfd);
        printf("Child writing to mmap...\n");
        memcpy(c_map, "Child was here!",16);
       
        munmap(c_map, size);
        exit(0);
    } else {
        waitpid(pid,NULL,NULL); // wait for child

        // Parent: mmap same file after child modified it
        int pfd = open(filename, O_RDWR);
      

        char* p_map = mmap(NULL, size, PROT_READ | PROT_WRITE, MAP_SHARED, pfd, 0);
      

        close(pfd);
        printf("Parent sees mmap contents: '%s'\n", p_map);
        munmap(p_map, size);
    }

    return 0;
}