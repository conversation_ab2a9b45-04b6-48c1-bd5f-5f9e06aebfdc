#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"
#include <string.h>
#include "fcntl.h"


char* string= "Hello from shared Memory object 1";
char* string2= "Hello from shared Memory object 2";
char* string3= "Hello from shared anon Memory object";
char* string4= "Hello from shared Memory object 3";




int main()
{
  int fd= shm_open("test",O_CREAT|O_RDWR,0);
   int fd2= shm_open("test3",O_CREAT|O_RDWR,0);
    char* adress= mmap(0,4096,PROT_READ|PROT_WRITE,MAP_SHARED,fd,0);
    char* adress_anon= mmap(0,4096*3,PROT_READ|PROT_WRITE,MAP_SHARED|MAP_ANONYMOUS,-1,0);
    
    char* adress3= mmap(0,4096*4,PROT_READ|PROT_WRITE,MAP_SHARED,fd2,0);
    printf("%p",adress3);
   
    memcpy(adress3,string4,34);
    memcpy(adress3+4096*2,string4,34);
    printf("parent adress:%p",adress);

     char* adress_anon2= mmap(0,4096*3,PROT_READ|PROT_WRITE,MAP_SHARED|MAP_ANONYMOUS,-1,0);
        memcpy(adress_anon2,string3,37);
        memcpy(adress_anon2+4098,string3,37);
       

    size_t pid=fork();
    if(pid==0)
    { 
        int fd= shm_open("test",O_CREAT|O_RDWR,0);
        char* adress1= mmap(0,4096,PROT_READ|PROT_WRITE,MAP_SHARED,fd,0);
        int fd2= shm_open("test2",O_CREAT|O_RDWR,0);
        char* adress2= mmap(0,4096*2,PROT_READ|PROT_WRITE,MAP_SHARED,fd2,0);
        int fd3= shm_open("test3",O_CREAT|O_RDWR,0);
        char* adress3= mmap(0,4096*4,PROT_READ,MAP_SHARED,fd3,0);
         munmap(adress_anon2,4096*3);
        memcpy(adress_anon,string3,37);
        memcpy(adress2,string2,34);
        memcpy(adress_anon+4097,string3,37);
       

        *adress1 ='a';
        *(adress1+1) ='a';
        *(adress1+2) ='\0';

           printf("%s\n",adress3);
           printf("%s\n",adress3+4096*2);
          printf("%s\n",adress_anon2);
          printf("%s\n",adress_anon2+4098);
          printf("child adress:%p\n",adress1);
          munmap(adress1,4096);
          munmap(adress2,4096*2);
          getchar();
    }
    else
    {
        waitpid(pid,NULL,NULL);
         
        int fd2= shm_open("test2",O_CREAT|O_RDWR,0);
        char* adress2= mmap(0,4096*2,PROT_READ,MAP_SHARED,fd2,0);
        printf("%s\n",adress);
        printf("%s\n",adress2);
         printf("%s\n",adress_anon);
          printf("%s\n",adress_anon+4097);
        

        munmap(adress,4096);
        munmap(adress2,4096*2);
        printf("%d", shm_unlink("test2"));
        printf("%d", shm_unlink("test3"));
       printf("%d", shm_unlink("test"));
         


    }


 



  return 0;
}
