#include <stdio.h>
#include <stdlib.h>
#include "pthread.h"

#define LOCAL_ARRAY_SIZE (10*4096)

void *say_hello()
{
    printf("Hello world\n");
      char local_array[4096*10];

    // Touch memory to ensure page faults
    for (size_t i = 4095; i < LOCAL_ARRAY_SIZE; ++i) {
        local_array[i] = 'A' ;  // Use different char per thread
    }

    // Print the address and first/last bytes for verification
    printf("[Thread ] array at %p, first='%c', last='%c'\n",
            (void*)local_array, local_array[4095], local_array[LOCAL_ARRAY_SIZE - 1]);
    return 0;
}

#define GLOBAL_ARRAY_SIZE (4096 * 3000)

char array[GLOBAL_ARRAY_SIZE];


int main()
{
pthread_t tid;

    for(size_t i =4095; i < 4096*3000; i++)
     {
        array[i]='a';       
     }


    for (int i = 0; i < 700; i++)
    {

        pthread_create(&tid, NULL, (void *(*)(void *))say_hello, NULL);
    for(size_t i =4095; i < 4096*100; i++)
    {
        array[i]='a';       
     }
    
        pthread_join(tid,NULL);
    }
    return 0;
}
