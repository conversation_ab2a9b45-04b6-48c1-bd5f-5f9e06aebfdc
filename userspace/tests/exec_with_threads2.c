#include <unistd.h>
#include <stdio.h>
#include<pthread.h>

pthread_t tid;
 pthread_t tid2;
 pthread_t tid3;
 pthread_t tid4;
 pthread_t tid5;
void*ret_val=0;
char* array[]={"Hello","World",NULL};
void* say_hello() {
  
  //getchar();
 
  return (void*) 777;
}


void* say_hello2() {
  printf("Hello world\n");
  pthread_join(tid,&ret_val );
  execv("/usr/program.sweb",array); 
 //printf("%d",  pthread_join(tid,&ret_val ));
  
 return (void*) 110;
}


void* say_hello3() {
  printf("Hello world\n");
   execv("/usr/program.sweb",array); 
  pthread_join(tid2,&ret_val );
   
   
 return  (void*) 777;
}

void* say_hello4() {
  printf("Hello world from thread 4\n");
  pthread_join(tid4,&ret_val );
   
 return (void*) 777;
}
void say_hello5() {
  printf("Hello world\n");
} 

int main() {

    while (1)
    {
        
     pthread_create(&tid, NULL, (void *(*)(void*))say_hello, NULL);
   pthread_create(&tid2, NULL, (void *(*)(void*))say_hello2, NULL);
   pthread_create(&tid3, NULL, (void *(*)(void*))say_hello3, NULL);
   pthread_create(&tid4, NULL, (void *(*)(void*))say_hello4, NULL);
  pthread_create(&tid4, NULL, (void *(*)(void*))say_hello4, NULL);
  pthread_create(&tid5, NULL, (void *(*)(void*))say_hello5, NULL);

  pthread_join(tid5,NULL);
    }


 
 
 
 //getchar(); 


  return 0;
}