/*
---
category: fast
tags: growing_stacks
description: "checks if the guard page on every thread stack works"
expect_exit_codes: [9999]
*/
#include "string.h"
#include "stdio.h"
#include "pthread.h"
#define ARRAY_SIZE 4096 * 15

void* allocatePages()
{
    char array[ARRAY_SIZE] = {0};
    return 0;
}

int main()
{
    printf("This test should crash!\n");
    pthread_t tid;
    pthread_create(&tid, NULL, allocatePages, NULL);

    pthread_join(tid, NULL);
    

    return 0;
}