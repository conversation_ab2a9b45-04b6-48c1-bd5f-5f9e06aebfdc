#include <stdio.h>
#include "pthread.h"
#include "stdlib.h"
#include "nonstd.h"
#include "string.h"
#include "wait.h"

char testpage[4096*3];

int main()
{
    for(size_t i =4095; i < 4096*3; i++)
    {
        testpage[i] = 'a';
    }

     pid_t pid = fork();
        if (pid == 0) {
           printf("%p",&testpage[0]);
          swap_page_out((size_t)&testpage[4095]/4096);
        }
        else
        {
            waitpid(pid,NULL,NULL);
           size_t t= testpage[4095];
        }

       
    
}