#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "wait.h"
#include <stdio.h>
#include "pthread.h"
#include "stdlib.h"


#define ONE_MB (1024 * 1024)
#define ALLOC_MB 1500  // Try increasing to 3000+ on systems with more RAM
#define STRIDE 4096    // Touch every 4KB page


char mem[1000*4096];


void allocate_and_touch_memory(int id) {
    size_t size = 4096*1000;
    char *mem = malloc(size);


    // Touch each page to force it to be backed by physical memory
    for (size_t i = 0; i < size; i += STRIDE) {
        mem[i] = (char)id;
    }

    printf("Process %d touched %zu MB\n", id, (size_t)ALLOC_MB);

    //sleep(10); // Keep memory resident for a while

    free(mem);
}

int main() {

    const int num_processes = 5;
    pid_t pids[num_processes];

    for (int i = 0; i < num_processes; i++) {
        pid_t pid = fork();
        if (pid == 0) {
            allocate_and_touch_memory(i + 1);
           exit(0);
            
        } else if (pid > 0) {
            pids[i] = pid;
        } 
    }

    // Wait a bit then kill one child to simulate process death
    
    
    printf("Killing process %ld to simulate cleanup\n", pids[0]);
    

    // Wait for all child processes
    for (int i = 0; i < num_processes; i++) {
        int status;
        waitpid(pids[i], &status, 0);
    }

    printf("All done.\n");
    return 0;
}