#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>
#include "wait.h"


int main() {
    int local_var = 777;

    int pid = fork();
    if (pid == 0) {
        local_var = 888;  // Writing to stack after fork
        printf("Child stack var = %d\n", local_var);
        _exit(0);
    } else {
        waitpid(pid, 0, 0);
        printf("Parent stack var = %d\n", local_var);
    }

    return 0;
}