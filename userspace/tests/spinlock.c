#include <stdio.h>
#include "pthread.h"

pthread_spinlock_t lock;
size_t count = 0;

void say_hello() {
    for(int i = 0;i < 100000;i++)
    {
        pthread_spin_lock(&lock);
        count++;
        if(count % 1000 == 0) printf("Count: %ld\n",count);
        pthread_spin_unlock(&lock);
    }

}

int main() {
    size_t tid;
    pthread_spin_init(&lock,0);
    for(size_t i = 0;i < 5;i++)
    {
        pthread_create(&tid, NULL, (void *(*)(void*))say_hello, NULL);

    }
    pthread_join(tid,0);
    printf("Counter: %ld\n",count);
    return 0;
}

