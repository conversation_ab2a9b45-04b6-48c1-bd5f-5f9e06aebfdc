#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>

char* array[]={"Hello","World",NULL};
size_t tid;
pthread_t tid;
 pthread_t tid2;
 pthread_t tid3;
 pthread_t tid4;
 pthread_t tid5;

 void* ret_val=0;


void* say_hello() {
  printf("Hello world\n");
  pthread_cancel(tid2);
  pthread_detach(tid2);
  return (void*) 777;
}


void* say_hello2() {
  printf("Hello world\n");
  //pthread_join(tid,&ret_val );
   printf("Thread2 join result:%d\n", pthread_join(tid3, &ret_val));
   printf("%p\n", ret_val );
 //printf("%d",  pthread_join(tid,&ret_val ));
  
 return (void*) 110;
}


void say_hello3() {
  printf("Hello world\n");
  //pthread_join(tid2,&ret_val );
    pthread_create(&tid, NULL, (void *(*)(void*))say_hello2, NULL);
   printf("Thread3 join result:%d\n", pthread_join(tid2, &ret_val));
    printf("Thread3 join result:%d\n", pthread_join(tid4, &ret_val));

     printf("%d", execv("ff",array));
   printf("%p\n", ret_val );
 //printf("%d",  pthread_join(tid2,&ret_val ));
}

void* say_hello4() {
  printf("Hello world from thread 4\n");
  //pthread_join(tid4,&ret_val );
    pthread_create(&tid, NULL, (void *(*)(void*))say_hello3, NULL);
    size_t pid =fork();
    if(pid==0)
    {

    printf("%d", execv("/usr/program.sweb",array));
    }
    else
    {
         printf("%d", execv("/usr/program.sweb",array));
    }
   printf("Thread4 join result:%d\n", pthread_join(tid3, &ret_val));
 return (void*) 777;
}
void say_hello5() {
  printf("Hello world\n");
   sleep(5);
   printf("Thread5 join result:%d\n", pthread_join(tid4,&ret_val ));
   printf("%p\n", ret_val );
 //printf("%d",  pthread_join(tid2,&ret_val ));
}



int main() {
   
      
     for(size_t i=0; i< 1000; i++)
     {
  
        pthread_create(&tid, NULL, (void *(*)(void*))say_hello, NULL);
        pthread_create(&tid2, NULL, (void *(*)(void*))say_hello2, NULL);
        pthread_create(&tid3, NULL, (void *(*)(void*))say_hello3, NULL);
        pthread_create(&tid4, NULL, (void *(*)(void*))say_hello4, NULL);
        pthread_create(&tid5, NULL, (void *(*)(void*))say_hello5, NULL);
     }
   
 
  
}
