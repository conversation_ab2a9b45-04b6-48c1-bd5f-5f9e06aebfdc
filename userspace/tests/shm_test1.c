#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"
#include "fcntl.h"

int main()
{
  int fd= shm_open("test",O_CREAT,0);
  printf("%d",fd);
    char* adress= mmap(0,4096,PROT_READ|PROT_WRITE,MAP_SHARED,fd,0);
    printf("parent adress:%p",adress);
    size_t pid=fork();
    if(pid==0)
    { 
        int fd= shm_open("test",O_CREAT,0);
        printf("%d",fd);
        char* adress1= mmap(0,4096,PROT_READ|PROT_WRITE,MAP_SHARED,fd,0);
        int fd2= shm_open("test2",O_CREAT,0);
        char* adress2= mmap(0,4096*2,PROT_READ|PROT_WRITE,MAP_SHARED,fd2,0);
         *adress2 ='h';
        *(adress2+1) ='s';
         *(adress2+2) ='\0';
          *(adress2+4097) ='\0';
        *adress1 ='a';
        *(adress1+1) ='a';
         *(adress1+2) ='\0';
          printf("%s\n",adress1);
          printf("child adress:%p\n",adress1);
          //munmap(adress1,4096);
          //munmap(adress2,4096*2);
          getchar();
    }
    else
    {
        waitpid(pid,NULL,NULL);
        //printf("%s\n",adress);  
        int fd2= shm_open("test2",O_CREAT,0);
        char* adress2= mmap(0,4096*2,PROT_READ,MAP_SHARED,fd2,0);
       
        printf("%s\n",adress2);

        munmap(adress,4096);
        munmap(adress2,4096*2);
        printf("%d", shm_unlink("test"));
        printf("%d", shm_unlink("test2"));


    }

}

