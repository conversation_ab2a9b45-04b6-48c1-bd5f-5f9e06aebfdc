#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>
#include "wait.h"


#define PAGE_SIZE 4096
#define PAGES     4


int main() {
    
    char buffer[PAGE_SIZE * PAGES]; 

    for (int i = 0; i < PAGES; i++) {
        buffer[i * PAGE_SIZE] = 'A' + i;
    }

    int pid = fork();
    if (pid == 0) {
       
        buffer[PAGE_SIZE]     = 'X';
        buffer[PAGE_SIZE * 3] = 'Y'; 

        printf("Child:\n");
        for (int i = 0; i < PAGES; i++) {
            printf("  Page %d: %c\n", i, buffer[i * PAGE_SIZE]);
        }

        _exit(0);
    } else {
        waitpid(pid, 0, 0);

        printf("Parent:\n");
        for (int i = 0; i < PAGES; i++) {
            printf("  Page %d: %c\n", i, buffer[i * PAGE_SIZE]);
        }
    }

    return 0;
}