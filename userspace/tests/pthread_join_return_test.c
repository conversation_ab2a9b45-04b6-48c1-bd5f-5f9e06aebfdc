#include <stdio.h>
#include "pthread.h"

void say_hello() {
    printf("Hello world\n");
    pthread_exit((size_t*)6);
    printf("Do not display\n");
}

int main() {
  size_t tid,tid2;
  void* retv_1;
  void* retv_2;
  tid2 = 4;

  pthread_create(&tid, NULL, (void *(*)(void*))say_hello, NULL);
  size_t value =pthread_join(tid,&retv_1);

  printf("retvalue: %p \n",retv_1);
  printf("join_return: %ld \n",value);

  size_t value2 =pthread_join(tid2,&retv_2);

  printf("retvalue: %p \n",retv_2);
  printf("join_return: %ld \n",value2);
  return 0;
}

