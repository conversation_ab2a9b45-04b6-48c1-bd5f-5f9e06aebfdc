#include <stdio.h>
#include <string.h>
#include "pthread.h"
#include "fcntl.h"
#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"

int main() {
   
    const char *filename = "/usr/testfile.txt";
    char string[5]="Hello";
    char string2[11]="ffffffffff";
    int fd = open(filename, O_RDWR|O_CREAT); 
    printf("%d\n",fd);
    lseek(fd,3000,SEEK_SET);
    write(fd,string,5);
    lseek(fd,4097,SEEK_SET);
    write(fd,string,5);
    lseek(fd,7096,SEEK_SET);
     write(fd,string,5);

  
    char *mapped = mmap(NULL, 4096*2, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 3000);
   
    printf("Original content: %s\n", mapped);
    printf("Original content: %s\n", mapped+1097);
    printf("Original content: %s\n", mapped+4096);

    
    mapped[0] = 'A'; 
    printf("Modified content: %s\n", mapped);

    
    //close(fd);

    munmap(mapped, 4096); 
      

    return 0;
}
