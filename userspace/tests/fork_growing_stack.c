/*
---
category: fast
tags: fork, growing_stacks
description: "Checks if 19 pages can be created for a big array and and read from in the child process after fork"
expect_exit_codes: 
*/
#include "string.h"
#include "stdio.h"
#include "unistd.h"
#include "wait.h"

#define PAGE_SIZE 4096
#define PAGES 14

int main()
{
    size_t test_success = 0;
    size_t pid = fork();

    if (pid == 0)
    {

        char array[PAGE_SIZE * PAGES] = {0};

        for (size_t i = 0; i < PAGES; i++)
        {

            printf("Page:%ld\n", i);
            printf("%d\n", array[i * PAGE_SIZE]);
            if (array[i*PAGE_SIZE] != 0)
            {
                test_success = -1;
            }
        }
    }
    else
    {

        waitpid(pid, 0, 0);
    }


    if (test_success == -1)
    {
        return -1;
    }

    return 0;
}