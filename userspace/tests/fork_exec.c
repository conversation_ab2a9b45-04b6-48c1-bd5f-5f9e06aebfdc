#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>

char* array[]={"Hello","World",NULL};
size_t tid;
void* say_hello() {
  printf("Hello world\n");
  return (void*) 777;
}


int main() {
    pid_t pid;
    pid = fork();
   
    if (pid == 0) {
        
        pthread_create(&tid,NULL,say_hello,NULL);
        printf("%d", execv("/usr/program.sweb",array));
        printf("Das ist der Child-Prozess\n");
    
}
else
{
 printf("%d", execv("/usr/program.sweb",array));
}

getchar();
  
}