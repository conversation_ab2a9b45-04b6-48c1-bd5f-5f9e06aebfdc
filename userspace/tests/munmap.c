#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"

int main()
{
   char* adress= mmap((void*)0x400,4096*4,PROT_READ|PROT_WRITE,MAP_ANONYMOUS|MAP_PRIVATE,-1,300);
    printf("parent adress:%p\n",adress);
    *adress ='a';
    *(adress+1) ='a';
    *(adress+2) ='\0';
    *(adress+4098) ='a';
    *(adress+4096*2+1) ='a';
    //*(adress+4096*3+1) ='a';
    printf("%s\n",adress);
    printf("%c\n", *(adress+4098));
    printf("%c\n", *(adress+4096*2+1));



     munmap(adress+4096*3,4096);
     *(adress+4096*3) ='b';
     munmap(adress+4096*2,4096);
     munmap(adress+4096,4096);
     munmap(adress,4096);
     *adress ='b';
   
   

 



  return 0;
}