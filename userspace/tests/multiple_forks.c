#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>
#include "wait.h"

 pid_t pid[50];

int main() {
    for (int i = 0; i < 50; ++i) {
        pid[i] = fork();
        // if (pid == 0) {
        //     _exit(i);
        // }
    }

    for (int i = 0; i < 50; ++i) {
        int status;
        waitpid(pid[i],NULL,NULL);
    }

    printf("Fork bomb complete.\n");
    return 0;
}