#include <stdio.h>
#include<pthread.h>
#include<string.h>

void* return_value;

void say_hello() {
  int counter = 0;
  while(1)
  {

  printf("Hello world %d\n",counter);
  counter += counter;
  }

  return;
}

int main(){
    size_t tid ,tid1, tid2;

    pthread_create(&tid, NULL, (void *(*)(void*))say_hello, NULL);
    pthread_cancel(tid);
    pthread_join(tid,&return_value);
    printf(" return value:%p", return_value);
   
    return 0;
}