#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>
#include "wait.h"


#define NUM_JOINABLE 400
#define NUM_DETACHED 400

void* joinable_worker(void* arg) {
    long id = (long)arg;
    //printf("[Joinable Thread %ld] Started\n", id);
    for (int i = 0; i < 10; i++) {
        //printf("[Joinable Thread %ld] Running...\n", id);
      
    }
    //printf("[Joinable Thread %ld] Finished\n", id);
    return NULL;
}

void* detached_worker(void* arg) {
    long id = (long)arg;
    //printf("[Detached Thread %ld] Started\n", id);
    for (int i = 0; i < 10; i++) {
        //printf("[Detached Thread %ld] Running...\n", id);
       
    }
    //printf("[Detached Thread %ld] Finished\n", id);
    return NULL;
}

void* cancelable_worker(void* arg) {
    printf("[Cancelable Thread] Started\n");
    for (int i = 0; i < 10; i++) {
        printf("[Cancelable Thread] Running...\n");
    
    }
    printf("[Cancelable Thread] Finished\n");
    return NULL;
}

void try_exec(const char* path) {
    char* const args[] = { (char*)path, NULL };
    printf("[Child] Executing: %s\n", path);
   printf("%d", execv(path, args));
   
}

int main() {
    pthread_t joinable_threads[NUM_JOINABLE];
    pthread_t detached_threads[NUM_DETACHED];
    pthread_t cancelable_thread;

    printf("[Main] Creating joinable threads...\n");
    for (long i = 0; i < NUM_JOINABLE; i++) {
        pthread_create(&joinable_threads[i], NULL, joinable_worker, (void*)i);
    }

    printf("[Main] Creating detached threads...\n");
    for (long i = 0; i < NUM_DETACHED; i++) {
        pthread_attr_t attr;
       
    
        pthread_create(&detached_threads[i], &attr, detached_worker, (void*)i);
        pthread_detach(detached_threads[i]);
       
    }

    printf("[Main] Creating cancelable thread...\n");
    pthread_create(&cancelable_thread, NULL, cancelable_worker, NULL);

    // Let threads start and run a bit

    printf("[Main] Canceling the cancelable thread...\n");
    pthread_cancel(cancelable_thread);
    pthread_join(cancelable_thread, NULL);
    printf("[Main] Canceled thread has exited.\n");

    pid_t pid = fork();
    if (pid < 0) {
       
    } else if (pid == 0) {
        // CHILD PROCESS
        printf("[Child] Forked. Now executing.\n");
         // Let threads in child run a bit
        try_exec("usr/exec_many_threads.sweb"); // Valid exec
    } else {
        // PARENT PROCESS
        printf("[Parent] Waiting for child to finish...\n");
        int status;
        waitpid(pid, &status, 0);
        printf("[Parent] Child exited with status: %d\n", status);

        printf("[Parent] Joining remaining threads...\n");
        for (int i = 0; i < NUM_JOINABLE; i++) {
            pthread_join(joinable_threads[i], NULL);
        }

        printf("[Parent] Done joining threads. Exiting.\n");
    }

    return 0;
}