#include <unistd.h>
#include <stdio.h>
#include<pthread.h>
#include "wait.h"
#include "string.h"



void run_test(const char *name, void (*test_func)()) {
    printf("Running test: %s... ", name);

    pid_t pid = fork();
    if (pid == -1) {
        printf("fork failed");
        _exit(1);
    }

    if (pid == 0) {
        // Child process runs the test
        test_func();
        _exit(0); // Should never reach here if exec works
    } else {
        int status;
        waitpid(pid, &status, 0);
     
            int code = status;
            if (code == 0 && strcmp(name, "Valid exec (/bin/ls)") == 0) {
                printf("[PASS - exec succeeded]\n");
            }
            else if (code == 0) {
                printf("[FAIL - exec did happen]\n");
            } else {
                printf("[PASS - exec failed as expected]\n");
            }
       
    
}
}

// --- Test Cases ---

// Test 1: Valid execve
void test_exec_valid_ls() {
    char *argv[] = { "ls", NULL };
    execv("/usr/program.sweb", argv);
    printf("excec failed");
    _exit(-1); // return errno as _exit code
}

// Test 2: Non-existent binary
void test_exec_invalid_path() {
    char *argv[] = { "fake", NULL };
    execv("/no/such/file", argv);
    printf("execve failed");
    _exit(-1);
}

// Test 3: NULL argv
void test_exec_null_argv() {
    execv("/usr/program.sweb",NULL);
    printf("execve failed");
    _exit(-1);
}

// Test 4: Malformed argv (no NULL terminator)
void test_exec_unterminated_argv() {
    char *argv[]={(char*)0x5,NULL};
   
    // Missing argv[1] = NULL;
    printf("argv pointer:%p\n",argv[2]);
    printf("argv pointer2:%p\n",argv[3]);
    execv("/usr/program.sweb", argv);
    printf("execve failed");
    _exit(-1);
}

// Test 5: Invalid argv pointer
void test_exec_invalid_argv_ptr() {
    char **bad_argv = (char **)0xdeadbeef;
    execv("/usr/program.sweb", bad_argv);
    printf("execve failed");
    _exit(-1);
}

// Test 6: argv[0] is NULL
void test_exec_null_argv0() {
    char *argv[] = { NULL, NULL };
    execv("/usr/program.sweb", argv);
    printf("execve failed");
    _exit(-1);
}

// Test 7: NULL path
void test_exec_null_path() {
    char *argv[] = { "ls", NULL };
    execv(NULL, argv);
    printf("execve failed");
    _exit(-1);
}

int main() {
   

   
    run_test("Valid exec (/bin/ls)", test_exec_valid_ls);
    run_test("Non-existent path", test_exec_invalid_path);
    run_test("NULL argv", test_exec_null_argv);
    run_test("Unterminated argv", test_exec_unterminated_argv);
    run_test("Invalid argv pointer", test_exec_invalid_argv_ptr);
    run_test("NULL argv[0]", test_exec_null_argv0);
    run_test("NULL path", test_exec_null_path);

    return 0;
}