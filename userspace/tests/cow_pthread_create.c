#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>
#include "wait.h"


#define PAGE_SIZE 4096
static char shared_data[PAGE_SIZE * 2]; // 2 pages

void* thread_func(void* arg) {
    // Thread writes to page 1
    shared_data[PAGE_SIZE] = 'T';  // Page 1
    return NULL;
}

int main() {
    // Init memory
    shared_data[0] = 'A';               // Page 0
    shared_data[PAGE_SIZE] = 'B';       // Page 1

    pthread_t thread;
    pthread_create(&thread, NULL, thread_func, NULL);
    pthread_join(thread, NULL);

    int pid = fork();
    if (pid == 0) {
        shared_data[0] = 'C';           // Child modifies page 0
        printf("Child sees:\n");
        printf("  Page 0: %c\n", shared_data[0]);
        printf("  Page 1: %c\n", shared_data[PAGE_SIZE]);
        _exit(0);
    } else {
        waitpid(pid, 0, 0);
        printf("Parent sees:\n");
        printf("  Page 0: %c\n", shared_data[0]);
        printf("  Page 1: %c\n", shared_data[PAGE_SIZE]);
    }

    return 0;
}