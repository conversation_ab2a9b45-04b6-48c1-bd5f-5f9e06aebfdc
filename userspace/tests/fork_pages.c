#include "stdio.h"
#include "pthread.h"
#include "unistd.h"
#include "string.h"

#define PAGESIZE 4096 *5

int main()
{   
    printf("test\n");
    char array[PAGESIZE];
    memset(array, 'A', PAGESIZE);

    printf("beginning of array: %c\n", array[0]);
    printf("end of array: %c\n", array[PAGESIZE - 1]);

    printf("Forking\n");
    sleep(1);

    size_t pid = fork();
    if (pid == 0)
    {
        printf("CHILD: beginning of array: %c\n"
               "end of array: %c\n",
               array[0], array[PAGESIZE - 1]);

        printf("CHILD: changing array now!\n");
        memset(array, 'B', PAGESIZE);

        printf("CHILD: beginning of array: %c\n"
               "end of array: %c\n",
               array[0], array[PAGESIZE - 1]);
    }
    if (pid > 0)
    {
        //sleep(1);
        printf("Press ENTER to start parent execution!\n Array in Parent should not have changed\n");
        //getchar();
        printf("PARENT: beginning of array: %c\n"
               "end of array: %c\n",
               array[0], array[PAGESIZE - 1]);
    }
    if (pid < 0)
    {
        printf("error\n");
    }
    return 0;
}