#include <stdio.h>
#include "pthread.h"
#include "stdlib.h"
#include "string.h"
#include "unistd.h"

#define PAGE_SIZE 4096
#define NUM_PAGES 300

// Each page is 4KB, total is 40KB
char global_array[NUM_PAGES * PAGE_SIZE];

int main() {
    
    reserve_pages(450);

    
    const int num_processes = 3;
    const int page_size = 4096;
    const int num_pages = 20;
    char* data = (char*)malloc(page_size);
    memset(data, 0xAB, page_size); // Fill with identical data

    for (int i = 0; i < num_processes; ++i)
    {
        int pid = fork();
        if (pid == 0)
        {
            for (int j = 0; j < num_pages; ++j)
            {
                char* mem = (char*)malloc(page_size);
                memcpy(mem, data, page_size); // Copy same content
                printf("Child %d wrote to page %d\n", i, j);
            }
           
           
        }
    }

   getchar();
    

    return 0;
}
