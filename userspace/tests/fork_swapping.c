#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include "wait.h"

#define PAGE_SIZE 4096
#define NUM_PAGES 400 
#define NUM_CHILDREN 5     

int main() {
    printf("Allocating %d pages to induce swapping...\n", NUM_PAGES);
    char **pages = (char **)malloc(NUM_PAGES * sizeof(char *));
   

    for (int i = 0; i < NUM_PAGES; ++i) {
        pages[i] = (char *)malloc(PAGE_SIZE);
       
        memset(pages[i], i % 256, PAGE_SIZE);  
    }

    printf("Forking %d children...\n", NUM_CHILDREN);
    pid_t child_pids[NUM_CHILDREN];

    for (int c = 0; c < NUM_CHILDREN; ++c) {
        pid_t pid = fork();

         if (pid == 0) {
            // Child process
            printf("[Child %d] Started. Reading memory...\n", c);
            int sum = 0;
            for (int i = 0; i < NUM_PAGES; i += 100) {
                sum += pages[i][0];  // Trigger swap-in if needed
            }
            printf("[Child %d] Done reading. Sum: %d\n", c, sum);

            // Optional: write to a page to trigger COW
            pages[c * 100][0] = 42;  // Unique index for each child
            printf("[Child %d] Wrote to a page.\n", c);
            _exit(0);
        } else {
            // Parent process
            child_pids[c] = pid;
        }
    }

    // Parent waits for all children using waitpid
    for (int i = 0; i < NUM_CHILDREN; ++i) {
        int status;
        waitpid(child_pids[i], &status, 0); 
        
      
    }

    printf("[Parent] All children finished.\n");

    // Clean up
    for (int i = 0; i < NUM_PAGES; ++i) {
        free(pages[i]);
    }
    free(pages);
    return 0;
}