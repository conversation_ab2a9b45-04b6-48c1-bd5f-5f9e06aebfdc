
#include <stdio.h>
#include<pthread.h>
#include<string.h>
#include "assert.h"


#define NUM_THREADS 1500

pthread_t threads[NUM_THREADS];
int thread_args[NUM_THREADS]; // arguments stay valid throughout

void* thread_func(void* arg) {
    int thread_num = *(int*)arg;

    switch (thread_num % 4) {
        case 0:
            printf("[Thread %d] Returning normally\n", thread_num);
            return (void*)(long)(thread_num + 100);
        case 1:
            printf("[Thread %d] Exiting via pthread_exit\n", thread_num);
            pthread_exit((void*)(long)(thread_num + 200));
        case 2:
            printf("[Thread %d] Sleeping to allow cancel\n", thread_num);
            //sleep(3);
            printf("[Thread %d] Woke up after sleep\n", thread_num);
            return (void*)(long)(thread_num + 300);
        case 3:
            printf("[Thread %d] Simulating long computation...\n", thread_num);
            for (volatile int i = 0; i < 100000000; ++i); 
            printf("[Thread %d] Finished computation\n", thread_num);
            return (void*)(long)(thread_num + 400);
        default:
            return NULL;
    }
}

void test_extended_pthread_behavior() {
    int result;

    // Create threads using static array for arguments
    for (int i = 0; i < NUM_THREADS; ++i) {
        thread_args[i] = i;
        result = pthread_create(&threads[i], NULL, thread_func, &thread_args[i]);
        assert(result == 0);
    }

    // Cancel sleeping threads
    sleep(1);
    for (int i = 0; i < NUM_THREADS; ++i) {
        if (i % 4 == 2) {
            printf("[Main] Cancelling thread %d\n", i);
            result = pthread_cancel(threads[i]);
            //assert(result == 0);
        }
    }

    // Join all threads
    for (int i = 0; i < NUM_THREADS; ++i) {
        void* retval;
        result = pthread_join(threads[i], &retval);
        assert(result == 0);

        if (retval == (void*) -1) {
            printf("[Main] Thread %d was canceled\n", i);
        } else {
            printf("[Main] Thread %d returned value: %ld\n", i, (long)retval);
        }
    }

    printf("All threads joined successfully.\n");
}



int main() {
    printf("Starting pthread test...\n");
    test_extended_pthread_behavior();
    return 0;
}