#include <stdio.h>
#include "pthread.h"
#include "stdlib.h"
#include "nonstd.h"
#include "string.h"

int main()
{
    int* testpage = malloc(4096);
    if (!testpage)
    {
        printf("malloc error");
        return -1;
    }
    memset(testpage, 'A', 4096); //for later swapin
    size_t vpn = (size_t)testpage / 4096;
    printf("vpn: %zu\n", vpn);

    size_t swapped_out_page = swap_page_out(vpn);
    printf("swapped out page: %d\n", (int)swapped_out_page);

    size_t swapped_in_page = swap_page_in(vpn);
    printf("swapped in page: %d\n", (int)swapped_in_page);

    printf("First char in page: %c should be A\n", testpage[0]);

    swapped_out_page = swap_page_out(vpn);
    printf("swapped out page: %d\n", (int)swapped_out_page);

    swapped_in_page = swap_page_in(vpn);
    printf("swapped in page: %d\n", (int)swapped_in_page);

    printf("First char in page: %c should be A\n", testpage[0]);

    free(testpage);
    return(0);
}