/*
---
category: fast
tags: pthread
description: "Create a single thread, then exit"
expect_exit_codes:
*/

#include <stdio.h>
#include <pthread.h>
#include <string.h>

char array[4096*10];


void say_hello()
{
  printf("Hello world\n");

  return;
}
void say_hello2()
{
  printf("Hello world\n");

  return;
}

int main()
{
  void *ret_val;
  size_t tid, tid1, tid2;
  pthread_create(&tid, NULL, (void *(*)(void *))say_hello, NULL);
   pthread_create(&tid, NULL, (void *(*)(void *))say_hello2, NULL);

    pthread_join(tid,NULL);

  array[0]= 'a';
  printf("Array acess");
  array[4097]= 'a';
    printf("Array acess");



  return 0;
}