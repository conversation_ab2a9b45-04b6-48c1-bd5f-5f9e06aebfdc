#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>
#include "wait.h"

char* array[]={"Hello","World",NULL};
size_t tid;
pthread_t tid;
 pthread_t tid2;
 pthread_t tid3;
 pthread_t tid4;
 pthread_t tid5;
 size_t i[4]={0};

 void* ret_val[250];




void* say_hello() {
  printf("Hello world\n");
 
    
  return (void*) 777;
}




void* say_hello2() {
  printf("Hello world\n");
  //pthread_join(tid,&ret_val );
   printf("Thread2 join result:%d\n", pthread_join(tid3, NULL));
   
  
     pid_t pid;
 //printf("%d",  pthread_join(tid,&ret_val ));
 pid = fork();
 if (pid == 0) {
    
     printf("Fork from Thread 2");
     execv("/usr/program.sweb",array);
    }
    else
    {
      waitpid(pid,NULL,NULL);  
    }
  
 return (void*) 110;
}


void say_hello3() {
      pid_t pid;
  printf("Hello world\n");
  //pthread_join(tid2,&ret_val );
  
   //printf("Thread3 join result:%d\n", pthread_join(tid2, NULL));
  
   
 //printf("%d",  pthread_join(tid2,&ret_val ));
 pid = fork();
 if (pid == 0) {
    
      printf("Fork from Thread 3");
       execv("/usr/cow_simple.sweb",array);
      _exit(0);
    }
    else
    {
        waitpid(pid,NULL,NULL);
    }
}

void* say_hello4() {
      pid_t pid;
  printf("Hello world from thread 4\n");
  //pthread_join(tid4,&ret_val );
   
   printf("Thread4 join result:%d\n", pthread_join(tid3, NULL));
   
   pid = fork();
   if (pid == 0) {
     printf("Fork from Thread 4");
       execv("/usr/pthread_join.sweb",array);
      _exit(0);

    }
    else
    {
        waitpid(pid,NULL,NULL);
    }
 return (void*) 777;
}
void say_hello5() {
      pid_t pid;
  printf("Hello world\n");
  
   printf("Thread5 join result:%d\n", pthread_join(tid4,NULL ));
 
     pid = fork();
   
    if (pid == 0) {
    printf("Fork from Thread 5");
     execv("/usr/exec_with_threads.sweb",array);
     _exit(0);

    }
    else
    {
      waitpid(pid,NULL,NULL);
    }

 
 //printf("%d",  pthread_join(tid2,&ret_val ));
}





int main() {
    pid_t pid;

     for(size_t i=0; i< 50; i++)
     {
  
        pthread_create(&tid, NULL, (void *(*)(void*))say_hello, NULL);
        pthread_create(&tid2, NULL, (void *(*)(void*))say_hello2, NULL);
        pthread_create(&tid3, NULL, (void *(*)(void*))say_hello3, NULL);
        pthread_create(&tid4, NULL, (void *(*)(void*))say_hello4, NULL);
        pthread_create(&tid5, NULL, (void *(*)(void*))say_hello5, NULL);
     }



   pid = fork();
   
    if (pid == 0) {
        
        //printf("%d", execv("/usr/combined_thread_test2.sweb",array));
        _exit(0);
        }
else
{

 waitpid(pid,NULL,NULL);
}



return 0;
}