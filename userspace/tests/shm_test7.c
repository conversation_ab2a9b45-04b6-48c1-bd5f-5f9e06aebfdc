#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"
#include <string.h>
#include "fcntl.h"



int main() {
    const char* shm_name = "/concurrent_read_shm";
    const size_t shm_size = 4096*6;

    
    int shm_fd = shm_open(shm_name, O_CREAT|O_RDWR,0);
   

    pid_t pid = fork();
    
  

    if (pid == 0) {  
        
        char* shared_mem = (char*) mmap(NULL, shm_size, PROT_READ | PROT_WRITE, MAP_SHARED, shm_fd, 0);
      

       
        const char* message1 = "First message.";
       memcpy(shared_mem, message1, 16);
        sleep(1);  
        const char* message2 = "Second message.";
       memcpy(shared_mem+4096, message2, 16);
        sleep(1);  
        const char* message3 = "Third message.";
       memcpy(shared_mem+4096*5, message3, 16);
        sleep(1);  

       
        munmap(shared_mem, shm_size);
        close(shm_fd);
        return 0;
    }
    else
    {

     waitpid(pid,NULL,NULL);

   
        char* shared_mem = (char*) mmap(NULL, shm_size, PROT_READ, MAP_SHARED, shm_fd, 0);
        char* shared_mem2 = (char*) mmap(NULL, shm_size, PROT_READ, MAP_SHARED, shm_fd, 0);
   
        printf("Consumer read: %s\n", shared_mem);

        printf("Consumer read: %s\n", shared_mem+4096);
        printf("Consumer read: %s\n", shared_mem+4096*5);
        printf("Consumer read: %s\n", shared_mem2);

        printf("Consumer read: %s\n", shared_mem2+4096);
        printf("Consumer read: %s\n", shared_mem2+4096*5);

        munmap(shared_mem, shm_size);
        close(shm_fd);
        shm_unlink(shm_name); 
    }

    
   
    

    return 0;
}
