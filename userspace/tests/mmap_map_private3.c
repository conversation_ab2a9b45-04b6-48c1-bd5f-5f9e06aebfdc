#include <stdio.h>
#include <string.h>
#include "pthread.h"
#include "fcntl.h"
#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"




int main() {
   
    const char *filename = "/usr/testfile.txt";
    const char *filename2 = "/usr/test1.txt";
    const char *filename3 = "/usr/test2.txt";
    char* string ="Private mmap_mapping 1";
    char* string2="Private mmap_mapping 2";
     char* string3="Private mmap_mapping 3";
    int fd = open(filename, O_RDWR|O_CREAT); 
    int fd2 = open(filename2, O_RDWR|O_CREAT);
    int fd3 = open(filename3, O_RDWR|O_CREAT);
    printf("%d\n",fd);
   
   write(fd,string,23);
   for(size_t i =0; i< 5000; i++)
   {
   write(fd2,string2,23);
   write(fd3,string3,23);
   }

  

  
    char *mapped = mmap((void*)0x400, 4096*3, PROT_READ | PROT_WRITE, MAP_PRIVATE, fd, 300);
     char *mapped2 = mmap(NULL, 4096*4, PROT_READ | PROT_WRITE, MAP_PRIVATE, fd2, 0);
      char *mapped3 = mmap(NULL, 4096*5, PROT_READ | PROT_WRITE, MAP_PRIVATE, fd3, 0);
   
    printf("Original content: %s\n", mapped);
     printf("Original content: %s\n", mapped2+4096);
      printf("Original content: %s\n", mapped3+4096*3);

    
    mapped[0] = 'A'; 
    printf("Modified content: %s\n", mapped);

    
    //close(fd);

    munmap(mapped, 4096); 
      

    return 0;
}
