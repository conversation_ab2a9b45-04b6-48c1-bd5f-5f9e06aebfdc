#include <stdio.h>
#include "pthread.h"

pthread_mutex_t lock;
size_t count = 0;

void say_hello(void* arg) {
    for(int i = 0;i < 500;i++)
    {
        pthread_mutex_lock(&lock);
        count++;
        printf("Count%ld: %ld\n",(size_t)arg,count);
        pthread_mutex_unlock(&lock);
    }

}

int main() {
    pthread_mutex_init(&lock,0);
    size_t tid[5];
    for(size_t i = 1;i<5;i++) {
        pthread_create(&tid[i], NULL, (void *(*)(void *)) say_hello, (void*)i);
    }
    for(size_t i = 1;i<5;i++) {
        pthread_join(tid[i],0);
    }


    printf("Counter: %ld\n",count);
    return 0;
}
