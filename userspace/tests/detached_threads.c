#include <stdio.h>
#include <stdlib.h>
#include "pthread.h"

pthread_t tid[300];


void say_hello() {
     size_t tid;
     pthread_create(&tid, NULL, (void *(*)(void*))say_hello, NULL);
     pthread_detach(tid);
    
}




int main(void)
{
     size_t tid;
     for(size_t i=0 ; i< 2000;i++)
     {
     pthread_create(&tid, NULL, (void *(*)(void*))say_hello, NULL);
     pthread_detach(tid);
     }   
    
    return 0;
}
