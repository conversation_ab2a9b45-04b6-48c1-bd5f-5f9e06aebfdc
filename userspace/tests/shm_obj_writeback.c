#include <stdio.h>
#include <string.h>
#include "pthread.h"
#include "fcntl.h"
#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"
#include "fcntl.h"

int main() {
   
 
  
    int fd =shm_open("test",O_CREAT|O_RDWR,0);
    

  
    char *mapped = mmap(NULL, 4096*2, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);

    size_t pid = fork();
    if(pid == 0)
    {
          int fd =shm_open("test",O_CREAT|O_RDWR,0);
        
       
       char *mapped = mmap(NULL, 4096*2, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0); 
         mapped[0] = 'A';
         mapped[1] = 'A';  
         printf("Modified content: %s\n", mapped); 
         munmap(mapped,4096*2);
         exit(0);
    }
    

      waitpid(pid,NULL,NULL);
   
    char buffer[6];
    lseek(fd,0,SEEK_SET);
    read(fd,buffer,6);
    printf("Contents of buffer:%s",buffer);

    
    //close(fd);

    munmap(mapped, 4096); 
      

    return 0;
}
