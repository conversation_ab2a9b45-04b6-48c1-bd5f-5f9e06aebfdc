#include <stdio.h>
#include <unistd.h>
#include "wait.h"



int main() {
    pid_t pid1 = fork();
    if (pid1 == 0) {
        pid_t pid2 = fork();
        if (pid2 == 0) {
            pid_t pid3 = fork();
            if (pid3 == 0) {
                printf("Third generation child\n");
                _exit(3);
            }
            waitpid(pid3,NULL,NULL);
            _exit(2);
        }
        waitpid(pid2,NULL,NULL);
        _exit(1);
    }

    waitpid(pid1,NULL,NULL);
    printf("Main done.\n");
}