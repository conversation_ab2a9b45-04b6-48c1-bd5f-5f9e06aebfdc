
#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>
#include "wait.h"


#define PAGE_SIZE 4096
#define PAGES     12


int main() {
    char data[PAGE_SIZE * PAGES];
    // <PERSON><PERSON> fills all pages with 'P'
    for (int i = 0; i < PAGES; i++) {
        data[i * PAGE_SIZE] = 'P';
    }

    int pid = fork();
    if (pid == 0) {
        // <PERSON> writes unique letters to all pages
        for (int i = 0; i < PAGES; i++) {
            data[i * PAGE_SIZE] = 'A' + i;
        }

        printf("Child sees:\n");
        for (int i = 0; i < PAGES; i++) {
            printf("  Page %d: %c\n", i, data[i * PAGE_SIZE]);
        }

        _exit(0);
    } else {
        waitpid(pid, 0, 0);

        printf("Pa<PERSON> sees:\n");
        for (int i = 0; i < PAGES; i++) {
            printf("  Page %d: %c\n", i, data[i * PAGE_SIZE]);
        }
    }

    return 0;
}