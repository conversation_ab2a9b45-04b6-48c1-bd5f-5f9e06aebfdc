#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <string.h>
#include "nonstd.h"  // for reserve_pages if needed
#include "wait.h"
#define NUM_THREADS 200
#define LOCAL_ARRAY_SIZE (4096 * 10)


char array[4096*1000];


void* thread_func(void* arg) {
    int tid = *(int*)arg;

    // Allocate a large local array on the stack
    char local_array[LOCAL_ARRAY_SIZE];

    // Touch memory to ensure page faults
    for (size_t i = 4095; i < LOCAL_ARRAY_SIZE; ++i) {
        local_array[i] = 'A' + (tid % 26);  // Use different char per thread
    }

    // Print the address and first/last bytes for verification
    printf("[Thread %d] array at %p, first='%c', last='%c'\n",
           tid, (void*)local_array, local_array[4095], local_array[LOCAL_ARRAY_SIZE - 1]);
       
      sleep(3);

      for (size_t i = 4095; i < LOCAL_ARRAY_SIZE; ++i) {
         if(local_array[i] != ('A' + (tid % 26)))
          {     
                printf("<PERSON>wapin failed");
                exit(-1);
          }  // Use different char per thread
    }

    memset((char*)array,4, 4096*1000);

    return NULL;
}

int main() {
    pthread_t threads[NUM_THREADS];
    int tids[NUM_THREADS];

    
   
    


    printf("[Main] Launching %d threads with large local arrays...\n", NUM_THREADS);

  for (int i = 0; i < NUM_THREADS; ++i) {
        tids[i] = i;
        pthread_create(&threads[i], NULL, thread_func, &tids[i]);
    }

     
   
    
    for (int i = 0; i < NUM_THREADS; ++i) {

        pthread_join(threads[i], NULL);
    }


  

    return 0;
}
