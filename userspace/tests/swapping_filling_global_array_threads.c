#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <string.h>

#define NUM_THREADS 50
#define LOCAL_ARRAY_SIZE (4096 * 10)
#define GLOBAL_ARRAY_SIZE (4096 * 30000)

char array[GLOBAL_ARRAY_SIZE];

void* thread_func(void* arg) {
    int tid = *(int*)arg;

  
     char local_array[LOCAL_ARRAY_SIZE];

   
    for (size_t i = 0; i < LOCAL_ARRAY_SIZE; ++i) {
        local_array[i] = 'A' + (tid % 26);
    }

   
    size_t chunk_size = GLOBAL_ARRAY_SIZE / NUM_THREADS;
    size_t start = tid * chunk_size;
    size_t end = start + chunk_size;

    if (end > GLOBAL_ARRAY_SIZE) end = GLOBAL_ARRAY_SIZE;

    for (size_t i = start; i < end; ++i) {
        array[i] = 'a' + (tid % 26);
    }

    printf("[Thread %d] local_array at %p, first='%c', last='%c', wrote global[%zu:%zu)\n",
           tid, (void*)local_array, local_array[0], local_array[LOCAL_ARRAY_SIZE - 1], start, end);

    return NULL;
}

void* thread_func2() {


   /**  int*  f = NULL;
    *f = 3;**/

    return NULL;
  
}



int main() {
    pthread_t threads[NUM_THREADS];
    int tids[NUM_THREADS];

    printf("[Main] Launching %d threads with local and global memory writes...\n", NUM_THREADS);

    for (int i = 0; i < NUM_THREADS; ++i) {
        tids[i] = i;
        pthread_create(&threads[i], NULL, thread_func, &tids[i]);
    }
   /**  for (int i = 0; i < NUM_THREADS; ++i) {
        tids[i] = i;
        pthread_create(&threads[i], NULL, thread_func2, &tids[i]);
    }**/


    for (int i = 0; i < NUM_THREADS; ++i) {
        pthread_join(threads[i], NULL);
    }

    printf("[Main] All threads finished. Verifying global memory...\n");

    // Optional: verify some of the global memory content
    for (int i = 0; i < NUM_THREADS; ++i) {
        size_t offset = (GLOBAL_ARRAY_SIZE / NUM_THREADS) * i;
        printf("Global[%zu] = %c\n", offset, array[offset]);
    }

    return 0;
}

