#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"
#include "fcntl.h"

#define SHM_NAME "/my_shared_mem"
#define SIZE 4096*990




int main() {
    // Create shared memory object
    int shm_fd = shm_open(SHM_NAME, O_CREAT | O_RDWR, 0666);
   

    // Set size of shared memory object
  

    // Map shared memory into address space
    char *shared_mem = mmap(NULL, SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, shm_fd, 0);

    for(size_t i =0; i < SIZE; i+=4096)
    {
        shared_mem[i]= (char)'a' + i;
    }
  

     // Initialize shared memory

    pid_t pid = fork();
    if (pid == 0) {
        // Child process
        printf("Child sees shared_mem[0] = %c\n", shared_mem[0]);
        if(shared_mem[0] != 'a')
        {
            exit(-1);
        }
        shared_mem[0] = 'f';
        printf("Child sets shared_mem[0] = f\n");
        _exit(0);
    } else {
        waitpid(pid,NULL,NULL); // Wait for child to finish
        printf("Parent sees shared_mem[0] = %c\n", shared_mem[0]);
    }

   
    // Cleanup
    munmap(shared_mem, SIZE);
    close(shm_fd);
    shm_unlink(SHM_NAME);  // Remove the shared memory object

    return 0;
}
