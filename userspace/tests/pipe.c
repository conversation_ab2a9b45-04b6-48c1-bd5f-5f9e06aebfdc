#include "unistd.h"
#include "stdio.h"
#include "pthread.h"
#include <string.h>
#include "wait.h"

char* buffer[5];

int main(){
   
    int fds[2];
   
     pipe(fds);
     pid_t pid = fork();

     if(pid ==0)
     {
         write(fds[1], "Hello",5);
     }
    
    else
    {
        waitpid(pid,NULL,NULL);
      read(fds[0],buffer,5);
       printf("%s\n", (char*)buffer);
    }
    


  

    return 0;
}