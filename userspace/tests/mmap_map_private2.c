#include <stdio.h>
#include <string.h>
#include "pthread.h"
#include "fcntl.h"
#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"

int main() {
   
    const char *filename = "/usr/testfile.txt";
    char string[5]="Hello";
    char string2[11]="ffffffffff";
    int fd = open(filename, O_RDWR|O_CREAT); 
    printf("%d\n",fd);
    for(size_t i=0; i< 4000;i++)
    {
    if(i <700)
    {
     write(fd,string,5);
    }
    else
    {
    write(fd,string2,11);
    }
    }

  

  
    char *mapped = mmap(NULL, 4096*2, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
   
    printf("Original content: %s\n", mapped+1000);

    
    mapped[0] = 'A'; 
    printf("Modified content: %c\n", mapped[4099]);

    
    //close(fd);

    munmap(mapped, 4096); 
      

    return 0;
}
