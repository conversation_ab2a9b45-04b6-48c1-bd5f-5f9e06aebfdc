#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"


int main()
{
    
    char* adress= mmap(0,4096*3,PROT_READ|PROT_WRITE,MAP_ANONYMOUS|MAP_SHARED,-1,0);
    char* adress2= mmap(0,4096*3,PROT_READ|PROT_WRITE,MAP_ANONYMOUS|MAP_SHARED,-1,0);
    char* adress3= mmap(0,4096*2,PROT_READ|PROT_WRITE,MAP_ANONYMOUS|MAP_SHARED,-1,0);
     
       *adress2='f';
       *(adress2+1)='a';
       *(adress2+2)='\0';
     
     size_t pid =fork();
     if(pid==0)
     {
       *adress='h';
       *(adress+1)='s';
       *(adress+2)='\0';
       *(adress+4096*2+1)='g';
        *(adress3)='a';
          *(adress3+1)='\0';
           *(adress3+4096)='f';
           *(adress3+4097)='\0';
       printf("%s",adress2);
       getchar();
     }
     else
     {
      waitpid(pid,NULL,NULL);
       printf("%c",adress[0]);
         printf("%s",adress2);
        printf("%s",adress3);
       getchar();
      munmap(adress,4096*3);
      munmap(adress2,4096*3);
       munmap(adress3,4096*2);

     }
   

 



  return 0;
}
