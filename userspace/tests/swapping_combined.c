#include <stdio.h>
#include "string.h"
#include "pthread.h"
#include "stdlib.h"

char array[4096*3000];

int main() {
    

static char array2[4096*3000];
char* array3 = malloc(4096*3000);



for(size_t i =4095 ; i< 4096*3000; i+=4096)
{
      memset(&array[i], 0xAA, 2);
      memset(&array2[i], 0xAA, 2);
}

for(size_t i =4095 ; i< 4096*3000; i+=4096)
{
     memset(&array3[i], 0xAA, 2);
}



return 0;


}