#include <stdio.h>
#include "pthread.h"

pthread_mutex_t lock;
size_t condition = 0;
pthread_cond_t cond;

void thread1() {
    pthread_mutex_lock(&lock);
    condition = 1;
    //printf("Waiting on condition...\n");
    pthread_cond_wait(&cond,&lock);
    //printf("Woke up\n");

}

void thread2() {
    while(condition == 0);
    pthread_mutex_lock(&lock);
    //printf("Signaling...\n");
    pthread_cond_signal(&cond);
    //printf("Signaled\n");
    pthread_mutex_unlock(&lock);

}

int main() {
    pthread_mutex_init(&lock,0);
    pthread_cond_init(&cond,0);
    size_t tid1,tid2;

    pthread_create(&tid1, NULL, (void *(*)(void *)) thread1,0);
    pthread_create(&tid2, NULL, (void *(*)(void *)) thread2,0);

    pthread_join(tid1,0);
    pthread_join(tid2,0);

    printf("Done\n");
    return 0;
}
