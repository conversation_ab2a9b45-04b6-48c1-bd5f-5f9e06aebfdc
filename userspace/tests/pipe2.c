#include <stdio.h>
#include <unistd.h>
#include <string.h>
#include "wait.h"

#define BUFFER_SIZE 100

int main() {
    int pipe_fd[2];
    pid_t pid;
    char write_msg[] = "Hello from parent!";
    char read_msg[BUFFER_SIZE];

    // Create the pipe
    pipe(pipe_fd);
       
    

    // Fork the process
    pid = fork();

   

    if (pid > 0) {
        // Parent process
        close(pipe_fd[0]); // Close unused read end
        write(pipe_fd[1], write_msg, strlen(write_msg) + 1);
        close(pipe_fd[1]); // Close write end after writing

        waitpid(pid,NULL,NULL); // Wait for child
    } else {
        // Child process
        close(pipe_fd[1]); // Close unused write end
        read(pipe_fd[0], read_msg, BUFFER_SIZE);
        printf("Child received: %s\n", read_msg);
        close(pipe_fd[0]);
    }

    return 0;
}