#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"


int main()
{
    
    char* adress= mmap(0,4096*3000,PROT_READ|PROT_WRITE,MAP_ANONYMOUS|MAP_SHARED,-1,0);


    for(size_t i =0; i < 4096*3000; i+=4096)
    {
        adress[i]= (char)'a' + i;
    }
     
     size_t pid =fork();
     if(pid==0)
     {
       *adress='h';
       *(adress+1)='s';
       *(adress+2)='\0';
       printf("%c",adress[1]);

     }
     else
     {
      waitpid(pid,NULL,NULL);
       printf("%c",adress[0]);
       munmap(adress,4096*3);

     }
   

 



  return 0;
}
