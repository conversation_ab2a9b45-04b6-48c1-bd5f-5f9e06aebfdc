#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <wait.h>


pid_t global_pid;

void* wait_in_thread(void* arg) {
    int status;
    int ret = waitpid(global_pid, &status, 0);
    if (ret > 0) {
        printf("Thread: Reaped child %d\n", ret);
    } else {
       
    }
    return NULL;
}

int main() {
    global_pid = fork();
    if (global_pid == 0) {
        _exit(77);
    }

    pthread_t t;
    pthread_create(&t, NULL, wait_in_thread, NULL);

    sleep(1); 

    
    int status;
    int ret = waitpid(global_pid, &status, 0);
    if (ret == -1 ) {
        printf("Main: Already reaped.\n");
    } else {
        printf("Main: Unexpected wait result.\n");
    }

    pthread_join(t, NULL);
}

