#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"
#include <string.h>
#include "fcntl.h"



int main() {
    const char* shm_name = "/concurrent_read_shm";
    const size_t shm_size = 256;

    
    int shm_fd = shm_open(shm_name, O_CREAT|O_RDWR,0);
   

    pid_t pid = fork();
    
  

    if (pid == 0) {  
        
        char* shared_mem = (char*) mmap(NULL, shm_size, PROT_READ | PROT_WRITE, MAP_SHARED, shm_fd, 0);
      

       
        const char* message1 = "First message.";
       memcpy(shared_mem, message1, shm_size);
        sleep(1);  
        const char* message2 = "Second message.";
       memcpy(shared_mem, message2, shm_size);
        sleep(1);  
        const char* message3 = "Third message.";
       memcpy(shared_mem, message3, shm_size);
        sleep(1);  

       
        munmap(shared_mem, shm_size);
        close(shm_fd);
        return 0;
    }

     waitpid(pid,NULL,NULL);

   
    char* shared_mem = (char*) mmap(NULL, shm_size, PROT_READ, MAP_SHARED, shm_fd, 0);
   
    printf("Consumer read: %s\n", shared_mem);

    
    munmap(shared_mem, shm_size);
    close(shm_fd);
    shm_unlink(shm_name);  

    return 0;
}
