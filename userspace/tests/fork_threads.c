#include <stdio.h>
#include <string.h>
#include "pthread.h"
#include "fcntl.h"
#include "unistd.h"

void* Write()
{
    printf("maoin\n");
    return 0;
}


int main(){

    fork();

    

    

    size_t tid1, tid2, tid3, tid4;
    pthread_create(&tid1,NULL,Write,NULL);
    pthread_create(&tid2,NULL,Write,NULL);
    pthread_create(&tid3,NULL,Write,NULL);
    pthread_create(&tid4,NULL,Write,NULL);


    pthread_join(tid1,NULL);
    pthread_join(tid2,NULL);
    pthread_join(tid3,NULL);
    pthread_join(tid4,NULL);
    return 0;
}