/*
---
category: slow
tags: cow, swapping, threads, hierarchy
description: "Komplexer Test für Swapping von Copy-on-Write (COW) Seiten mit Prozesshierarchie und Threads"
expect_exit_codes: 0
*/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include "wait.h"
#include "pthread.h"

#define PAGE_SIZE 4096
#define SHARED_PAGES 300
#define SWAP_TRIGGER_PAGES 800
#define NUM_CHILDREN 3
#define NUM_THREADS 2

// Gemeinsam genutzte Arrays für verschiedene Testszenarien
char shared_read_only[SHARED_PAGES * PAGE_SIZE];     // Nur-Lese-Seiten (never-dirty)
char shared_cow_pages[SHARED_PAGES * PAGE_SIZE];     // COW-Seiten
char shared_modified[SHARED_PAGES * PAGE_SIZE];      // Modifizierte Seiten (dirty)
char swap_trigger[SWAP_TRIGGER_PAGES * PAGE_SIZE];   // Zum Erzwingen von Swapping

// Thread-Daten
typedef struct {
    int thread_id;
    int process_id;
    int start_page;
    int end_page;
    int* error_count;
} ThreadData;

pthread_mutex_t error_mutex = PTHREAD_MUTEX_INITIALIZER;

// Thread-Funktion für Verifizierung
void* verify_thread(void* arg) {
    ThreadData* data = (ThreadData*)arg;
    int local_errors = 0;
    
    printf("[P%d-T%d] Verifying pages %d to %d\n", 
           data->process_id, data->thread_id, data->start_page, data->end_page);
    
    // Überprüfe read-only Seiten (sollten unverändert sein)
    for (int i = data->start_page; i < data->end_page; i++) {
        char expected = (char)(i % 256);
        if (shared_read_only[i * PAGE_SIZE] != expected) {
            printf("[P%d-T%d] ERROR: Read-only page %d has wrong value: %d vs %d\n", 
                   data->process_id, data->thread_id, i, 
                   (int)shared_read_only[i * PAGE_SIZE], (int)expected);
            local_errors++;
        }
    }
    
    // Überprüfe COW-Seiten (abhängig vom Prozess-ID)
    for (int i = data->start_page; i < data->end_page; i++) {
        char expected;
        if (data->process_id == 0) {
            // Elternprozess: Original-Werte
            expected = (char)(i % 256);
        } else if (data->process_id % 2 == 1) {
            // Ungerade Prozess-IDs: Modifizieren jede zweite Seite
            expected = (i % 2 == 0) ? (char)((i % 256) + data->process_id) : (char)(i % 256);
        } else {
            // Gerade Prozess-IDs: Modifizieren jede dritte Seite
            expected = (i % 3 == 0) ? (char)((i % 256) + data->process_id) : (char)(i % 256);
        }
        
        if (shared_cow_pages[i * PAGE_SIZE] != expected) {
            printf("[P%d-T%d] ERROR: COW page %d has wrong value: %d vs %d\n", 
                   data->process_id, data->thread_id, i, 
                   (int)shared_cow_pages[i * PAGE_SIZE], (int)expected);
            local_errors++;
        }
    }
    
    // Überprüfe modifizierte Seiten (abhängig vom Prozess-ID)
    for (int i = data->start_page; i < data->end_page; i++) {
        char expected = (char)((i % 256) + data->process_id);
        if (shared_modified[i * PAGE_SIZE] != expected) {
            printf("[P%d-T%d] ERROR: Modified page %d has wrong value: %d vs %d\n", 
                   data->process_id, data->thread_id, i, 
                   (int)shared_modified[i * PAGE_SIZE], (int)expected);
            local_errors++;
        }
    }
    
    pthread_mutex_lock(&error_mutex);
    *data->error_count += local_errors;
    pthread_mutex_unlock(&error_mutex);
    
    printf("[P%d-T%d] Verification complete with %d errors\n", 
           data->process_id, data->thread_id, local_errors);
    
    return NULL;
}

// Prozess-Funktion
int process_function(int process_id, int parent_id) {
    printf("[P%d] Process started (parent: P%d)\n", process_id, parent_id);
    
    // Modifiziere COW-Seiten basierend auf Prozess-ID
    if (process_id > 0) {
        printf("[P%d] Modifying COW pages\n", process_id);
        if (process_id % 2 == 1) {
            // Ungerade Prozess-IDs: Modifizieren jede zweite Seite
            for (int i = 0; i < SHARED_PAGES; i += 2) {
                shared_cow_pages[i * PAGE_SIZE] += process_id;
                if (i % 50 == 0) {
                    printf("[P%d] Modified COW page %d\n", process_id, i);
                }
            }
        } else {
            // Gerade Prozess-IDs: Modifizieren jede dritte Seite
            for (int i = 0; i < SHARED_PAGES; i += 3) {
                shared_cow_pages[i * PAGE_SIZE] += process_id;
                if (i % 50 == 0) {
                    printf("[P%d] Modified COW page %d\n", process_id, i);
                }
            }
        }
        
        // Alle Prozesse modifizieren ihre eigenen Kopien von shared_modified
        for (int i = 0; i < SHARED_PAGES; i++) {
            shared_modified[i * PAGE_SIZE] += process_id;
            if (i % 50 == 0) {
                printf("[P%d] Modified page %d\n", process_id, i);
            }
        }
    }
    
    // Erzeuge Kindprozesse, wenn wir noch nicht die maximale Tiefe erreicht haben
    if (process_id < NUM_CHILDREN) {
        pid_t child_pid = fork();
        
        if (child_pid < 0) {
            printf("[P%d] Fork failed!\n", process_id);
            return 1;
        } else if (child_pid == 0) {
            // Kind-Prozess
            return process_function(process_id + 1, process_id);
        } else {
            printf("[P%d] Created child process P%d (PID: %d)\n", 
                   process_id, process_id + 1, child_pid);
        }
    }
    
    // Erzwinge Swapping in Blatt-Prozessen
    if (process_id == NUM_CHILDREN) {
        printf("[P%d] Triggering swapping...\n", process_id);
        for (int i = 0; i < SWAP_TRIGGER_PAGES; i++) {
            swap_trigger[i * PAGE_SIZE] = (char)(i % 256);
            if (i % 100 == 0) {
                printf("[P%d] Allocated page %d to trigger swapping\n", process_id, i);
            }
        }
    }
    
    // Verifiziere mit Threads
    printf("[P%d] Starting verification threads\n", process_id);
    pthread_t threads[NUM_THREADS];
    ThreadData thread_data[NUM_THREADS];
    int error_count = 0;
    
    int pages_per_thread = SHARED_PAGES / NUM_THREADS;
    for (int t = 0; t < NUM_THREADS; t++) {
        thread_data[t].thread_id = t;
        thread_data[t].process_id = process_id;
        thread_data[t].start_page = t * pages_per_thread;
        thread_data[t].end_page = (t == NUM_THREADS - 1) ? 
                                  SHARED_PAGES : (t + 1) * pages_per_thread;
        thread_data[t].error_count = &error_count;
        
        pthread_create(&threads[t], NULL, verify_thread, &thread_data[t]);
    }
    
    // Warte auf alle Threads
    for (int t = 0; t < NUM_THREADS; t++) {
        pthread_join(threads[t], NULL);
    }
    
    // Warte auf Kindprozess, falls vorhanden
    if (process_id < NUM_CHILDREN) {
        int status;
        pid_t child_pid = wait(&status);
        
        if (WIFEXITED(status)) {
            printf("[P%d] Child P%d exited with status %d\n", 
                   process_id, process_id + 1, WEXITSTATUS(status));
            if (WEXITSTATUS(status) != 0) {
                printf("[P%d] Child reported errors!\n", process_id);
                error_count++;
            }
        } else {
            printf("[P%d] Child P%d did not exit normally\n", process_id, process_id + 1);
            error_count++;
        }
    }
    
    printf("[P%d] Process completed with %d errors\n", process_id, error_count);
    return (error_count > 0) ? 1 : 0;
}

int main() {
    printf("Complex COW Swapping Test: Initializing shared arrays...\n");
    
    // Initialisiere die gemeinsam genutzten Arrays
    for (int i = 0; i < SHARED_PAGES; i++) {
        shared_read_only[i * PAGE_SIZE] = (char)(i % 256);
        shared_cow_pages[i * PAGE_SIZE] = (char)(i % 256);
        shared_modified[i * PAGE_SIZE] = (char)(i % 256);
        
        if (i % 100 == 0) {
            printf("Initialized page %d\n", i);
        }
    }
    
    // Starte den Hauptprozess (P0)
    int result = process_function(0, -1);
    
    if (result == 0) {
        printf("Complex COW Swapping Test completed successfully!\n");
    } else {
        printf("Complex COW Swapping Test failed with errors!\n");
    }
    
    return result;
}