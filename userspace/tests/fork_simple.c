
#include <stdio.h>
#include "pthread.h"
#include <string.h>


void say_hello() {
  printf("Hello world\n");
}
int main() {
    pid_t pid;
    getchar();
    pid = fork();
    if (pid < 0) {
        printf("error");
    } else if (pid == 0) {
        printf("Das ist der Child-Prozess\n");
        printf("child pid: %ld\n",pid );
        size_t tid;
        pthread_create(&tid, NULL, (void *(*)(void*))say_hello, NULL);
        getchar();
    
    } else {
        printf("das ist der Parent-Prozess. PID des Child: %ld\n", pid);  
    
    }
    return 0;
}