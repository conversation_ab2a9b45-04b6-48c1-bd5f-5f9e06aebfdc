#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"
#include "fcntl.h"


#define SHM_NAME "/my_shared_mem"
#define SIZE 4096

int main() {
    // Parent creates shared memory
    int shm_fd = shm_open(SHM_NAME, O_CREAT | O_RDWR, 0666);
   

    // Set the size of the shared memory
    

    // Map it into parent memory
    int *shared_mem = mmap(NULL, SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, shm_fd, 0);
  

    shared_mem[0] = 123;  // Initialize

    pid_t pid = fork();
     if (pid == 0) {
        // === Child process ===

        // Child reopens the same shared memory without closing anything
        int shm_fd_child = shm_open(SHM_NAME, O_RDWR, 0666);
        printf("%d\n",shm_fd_child);
       

        int *child_mem = mmap(NULL, SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, shm_fd_child, 0);

       

        printf("Child sees shared_mem[0] = %d\n", child_mem[0]);
        child_mem[0] = 888;
        printf("Child sets shared_mem[0] = 888\n");

        // Clean up
        
    } else {
        // === Parent process ===
        waitpid(pid,NULL,NULL); // Wait for child

        printf("Parent sees shared_mem[0] = %d\n", shared_mem[0]);

        // Clean up
        munmap(shared_mem, SIZE);
        close(shm_fd);
        shm_unlink(SHM_NAME);
    }

    return 0;
}