#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"
#include <string.h>
#include "fcntl.h"


int main() {
    

    const char* shm_name = "/test_shm";
    int shm_fd = shm_open(shm_name,O_CREAT|O_RDWR,0);
   
    const size_t shm_size = 256;
   

    
    pid_t pid = fork();
   

    if (pid == 0) { 
       
        char* shared_mem = (char*) mmap(NULL, shm_size, PROT_READ, MAP_SHARED, shm_fd, 0);
        
 
        sleep(2);  

      
        printf("Consumer read: %s\n", shared_mem);

      
        munmap(shared_mem, shm_size);
        close(shm_fd);
        shm_unlink(shm_name);

    } else {  

        char* shared_mem = (char*) mmap(NULL, shm_size, PROT_READ | PROT_WRITE, MAP_SHARED, shm_fd, 0);


        const char* message = "Hello from Shared Memory!";
        
        memcpy(shared_mem, message, shm_size);
        

        printf("Producer wrote: %s\n", message);

       
        munmap(shared_mem, shm_size);
        close(shm_fd);

       
        waitpid(pid,NULL,NULL);  
    }

    return 0;
}
