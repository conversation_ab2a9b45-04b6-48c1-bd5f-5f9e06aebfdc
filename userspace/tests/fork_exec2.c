#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>
#include "wait.h"

char* array[]={"Hello","World",NULL};
size_t tid;
void* say_hello() {
  printf("Hello world\n");
  return (void*) 777;
}


int main() {
    pid_t pid;
    pid = fork();
   
    if (pid == 0) {
        
        printf("%d", execv("/usr/combined_thread_test.sweb",array));
        }
else
{

 return 0;
}

//waitpid(pid,NULL,NULL);

  
}