/*
---
category: fast
tags: fds
description: "Checks if fds are rightfully isolated after fork"
expect_exit_codes: 
*/

#include <stdio.h>
#include <string.h>
#include "pthread.h"
#include "fcntl.h"
#include "unistd.h"

int main()
{
    size_t test_sucess = 0;

    const char *text_parent = "parent_data";
    int fd1 = open("/usr/testfile1.txt", O_RDWR | O_CREAT);
    if (fd1 < 0)
    {
        printf("error: creating file\n");
        return -1;
    }
    int fd2 = open("/usr/testfile2.txt", O_RDWR | O_CREAT);
    if (fd2 < 0)
    {
        printf("error: creating file\n");
        return -1;
    }
    printf("Parent: fd1=%d, fd2=%d \n", fd1, fd2);

    write(fd1, text_parent, sizeof(text_parent));

    pid_t pid = fork();
    if (pid < 0)
    {
        printf("error: fork");
        close(fd1);
        close(fd2);
    }
    else if (pid == 0)
    { // child
        printf("Child inhereted fd1=%d, fd2=%d\n", fd1, fd2);

        const char *text_child = "child_data";
        int write_result = write(fd1, text_child, sizeof(text_child));
        printf("Child: write result= %d \n", write_result);

        printf("Child: closing fd1\n");
        close(fd1);

        int fd3 = open("/usr/testfile3.txt", O_RDWR | O_CREAT);
        printf("Child: new fd3=%d\n", fd3);


        int fd1_neu = open("/usr/testfile1.txt",  O_RDWR | O_CREAT);
        printf("Child reopened fd1 as fd1_neu=%d\n", fd1_neu);
    }
    else // parent
    {
        sleep(2);
        char* buffer[20];
        const char* text_parent2 = "parent2";
        int read_result = read(fd1,buffer,sizeof(buffer));
        printf("Parent: read result = %d sollte 0 sein\n", read_result);

        int write_result = write(fd1, text_parent2, sizeof("parent2"));
        printf("Parent: write result = %d sollte nicht -1 sein\n", write_result);

        close(fd1);
        close(fd2);
        if (read_result != 0 || write_result == -1)
        {
            test_sucess = -1;
        }
    }

    if (test_sucess == -1)
    {
        return -1;
    }
    return 0;
}