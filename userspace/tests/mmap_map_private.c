#include <stdio.h>
#include <string.h>
#include "pthread.h"
#include "fcntl.h"
#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"

int main() {
   
    const char *filename = "/usr/testfile.txt";
    char string[5]="Hello";
    char string2[11]="ffffffffff";
    int fd = open(filename, O_RDWR|O_CREAT); 
    
    
    printf("%ld", write(fd,string2, 20));
    //printf("%ld", write(fd,string,4096));
    
    

  
    //char *mapped = mmap(NULL, 4096*2, PROT_READ | PROT_WRITE, MAP_PRIVATE, fd, 0);
   
    //printf("Original content: %s\n", (mapped+4096));

    
   
      

    return 0;
}
