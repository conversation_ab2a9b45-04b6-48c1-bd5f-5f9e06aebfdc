#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <wait.h>


int main() {
    pid_t pid;
    int status=3;
    pid = fork();
   
    printf("pid: %ld\n",pid );

    if (pid == 0) {
         int status=3;
        printf("Das ist der Child-Prozess\n");
        getchar();
       
       
        return -1;
        
    }
    else
    {  
       waitpid(pid,&status,0);
       printf("Exit code:%d\n",status);
    }
    
     //printf("%ld\n", waitpid(1,NULL,0));
   
   
   


    return 0;
}