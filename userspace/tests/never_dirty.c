#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <string.h>
#include "nonstd.h"
#include "wait.h"
#define PAGE_SIZE 4096
#define ARRAY_SIZE 1500
char read_only_array[PAGE_SIZE * ARRAY_SIZE];

int main()
{
    printf("start reading big array! should trigger swapping\n");

    for (int i = 0; i < ARRAY_SIZE; i++)
    {
        char val = read_only_array[i * PAGE_SIZE];
        if (i % 100 == 0)
        {
            printf("Read page %d\n", i);
        }
    }

    printf("accessing pages so that they have to be loaded in\n");
    for (int i = 0; i < ARRAY_SIZE; i++)
    {
        char val = read_only_array[i * PAGE_SIZE];
        if (i % 100 == 0)
        {
            printf("Read page a second time %d\n", i);
        }
    }

    pid_t pid = fork();
    if (pid < 0)
    {
        printf("Error\n");
        return -1;
    }
    else if (pid == 0) // child
    {
        printf("CHILD accessing pages so that they have to be loaded in\n");
        for (int i = 0; i < ARRAY_SIZE; i++)
        {
            char val = read_only_array[i * PAGE_SIZE];
            if (i % 100 == 0)
            {
                printf("Read page a second time %d\n", i);
            }
        }
    }

    return 0;
}
