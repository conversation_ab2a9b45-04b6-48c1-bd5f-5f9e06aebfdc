#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>
#include "wait.h"


static int data = 555;

int main() {
    int pid = fork();
    if (pid == 0) {
        // Child process writes
        data = 999;  
        printf("Child wrote shared_data = %d\n", data);
        _exit(0);
    } else {
        waitpid(pid, 0, 0);
        printf("Parent read shared_data = %d\n", data);
    }

    return 0;
}