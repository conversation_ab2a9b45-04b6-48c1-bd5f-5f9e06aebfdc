#include <stdio.h>
#include "pthread.h"
#include "stdlib.h"
#include "nonstd.h"
#include "string.h"
#include "wait.h"

char array[4096*400];
char array2[4096*400];
char array3[4096*1000];

int main() {

 reserve_pages(700);

/**for(size_t i =4095 ; i< 4096*450; i+=4096)
{
     memset(&array[i], 0xAA, 2);
      memset(&array2[i], 0xAA, 2);
}**/

for(size_t i =4095 ; i< 4096*300; i+=4096)
{
     memset(&array2[i], 0xAA, 2);
}

for(size_t i =0 ; i< 4096*300; i+=4096)
{
     memset(&array2[i], 0xAA, 2);
}



return 0;


}