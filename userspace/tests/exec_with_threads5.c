#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>
#include "wait.h"





#define NUM_JOINABLE 400
#define NUM_DETACHED 400
#define NUM_CANCELABLE 300



void* joinable_worker(void* arg) {
    long id = (long)arg;
    for (int i = 0; i < 3; i++) {
        printf("[Joinable Thread %ld] Working... Iteration %d\n", id, i);
     
    }
    printf("[Joinable Thread %ld] Finished\n", id);
    return NULL;
}

void* detached_worker(void* arg) {
    long id = (long)arg;
    for (int i = 0; i < 3; i++) {
        printf("[Detached Thread %ld] Running... Iteration %d\n", id, i);
       
    }
    printf("[Detached Thread %ld] Finished\n", id);
    return NULL;
}

void* cancelable_worker(void* arg) {
    long id = (long)arg;
    printf("[Cancelable Thread %ld] Started\n", id);
    for (int i = 0; i < 5; i++) {
        printf("[Cancelable Thread %ld] Still alive at iteration %d\n", id, i);
        
    }
    printf("[Cancelable Thread %ld] Finished\n", id);
    return NULL;
}

void try_exec(const char* path) {
    char* const args[] = { (char*)path, "-l", NULL };
    printf("[Child] Executing: %s\n", path);
    execv(path, args);
    _exit(1);
}

int main() {
    

    pthread_t joinable_threads[NUM_JOINABLE];
    pthread_t cancelable_threads[NUM_CANCELABLE];
     pthread_t detached_threads[NUM_DETACHED];

    printf("Spawning %d joinable threads...\n", NUM_JOINABLE);
    for (long i = 0; i < NUM_JOINABLE; i++) {
        pthread_create(&joinable_threads[i], NULL, joinable_worker, (void*)i);
    }

    printf("Spawning %d detached threads...\n", NUM_DETACHED);
    for (long i = 0; i < NUM_DETACHED; i++) {
        
       
        pthread_create(&detached_threads[i], NULL, detached_worker, (void*)i);
        pthread_detach(detached_threads[i]);
        
    }

    printf("Spawning %d cancelable threads...\n", NUM_CANCELABLE);
    for (long i = 0; i < NUM_CANCELABLE; i++) {
        pthread_create(&cancelable_threads[i], NULL, cancelable_worker, (void*)i);
    }


    
    printf("Forking child to exec...\n");
    pid_t pid = fork();
    if (pid < 0) {
      
    } else if (pid == 0) {
       
        try_exec("usr/exec_with_threads.sweb");
    } else {
        int status;
        waitpid(pid, &status, 0);
        printf("[Parent] Child exited with status: %d\n", status);
    }
    
    printf("Canceling cancelable threads...\n");
    for (int i = 0; i < NUM_CANCELABLE; i++) {
        pthread_cancel(cancelable_threads[i]);
        pthread_join(cancelable_threads[i], NULL);
    }
    printf(" All cancelable threads joined after cancel.\n");


    printf("Joining all joinable threads...\n");
    for (int i = 0; i < NUM_JOINABLE; i++) {
        pthread_join(joinable_threads[i], NULL);
    }

   
    return 0;
}
