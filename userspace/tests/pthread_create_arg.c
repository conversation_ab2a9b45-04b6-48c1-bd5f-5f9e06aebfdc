#include <stdio.h>
#include "pthread.h"



void *say_hello()
{
    printf("hello\n");
    return 0;
}

void* print_arg(void *arg)
{
    size_t num = (size_t)arg;
    printf("Thread arg: %ld\n", num);
    return 0;
}

int main()
{
    size_t tid;
    size_t tid1;
    pthread_create(&tid, NULL, (void *(*)(void *))say_hello, NULL);
    size_t num = 55;

    pthread_create(&tid1, NULL,print_arg, (void*)num);

    return 0;
}