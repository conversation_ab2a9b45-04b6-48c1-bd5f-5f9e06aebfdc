#include <pthread.h>
#include <stdio.h>
#include <unistd.h>

void* thread_func(void* arg) {

      pthread_setcanceltype(PTHREAD_CANCEL_ASYNCHRONOUS,NULL);

    printf("Thread started\n");
    
    while (1)
    {
       
    }
    
        
    printf("Thread completed\n");
    return "Thread finished normally";
}

int main() {
    pthread_t thread;
    void* retval;

    pthread_create(&thread, NULL, thread_func, NULL);
       

    sleep(3); // Let the thread run for a while

    pthread_cancel(thread);
   
       

    pthread_join(thread, &retval);
       
    printf("%p",retval);
   

    return 0;
}