/*
---
category: slow
tags: pthreads
description: "creates lot of threads and they simulate diffrent exits"
expect_exit_codes: 
*/

#include <stdio.h>
#include<pthread.h>
#include<string.h>
#include "assert.h"

#define NUM_THREADS 1200

pthread_t threads[NUM_THREADS];


void* thread_behavior(void* arg) {
    //printf("pointer:%p\n",arg);
    int thread_num = *(int*)arg;

    // Simulate different behaviors
    if (thread_num % 3 == 0) {
        printf("[Thread %d] Exiting via pthread_exit\n", thread_num);
        pthread_exit((void*)(size_t)(thread_num + 100));
    } else if (thread_num % 3 == 1) {
        printf("[Thread %d] Doing work and returning normally\n", thread_num);
        //sleep(1);
        
        return (void*)(size_t)(thread_num + 200);
    } else {
        printf("[Thread %d] Will be canceled if not quick enough\n", thread_num);
        //sleep(3); // Long sleep to allow cancel
        return (void*)(size_t)(thread_num + 300);
    }

    // Uncomment to test what happens when a thread exits the whole process
    // if (thread_num == 5) {
    //     printf("[Thread %d] Calling exit(0), this ends the entire program!\n", thread_num);
    //     exit(0);
    // }
    return 0;
}

void test_combined_pthread_behavior() {
    int args[NUM_THREADS];
    int result;
       
    // Create threads
    for (int i = 0; i < NUM_THREADS; ++i) {
         
        args[i] = i;
        result = pthread_create(&threads[i], NULL, thread_behavior, &args[i]);
        assert(result == 0);
    }

    // Give threads some time to run
   
    // Cancel some threads
    for (int i = 0; i < NUM_THREADS; ++i) {
        if (i % 3 == 2) {
            printf("[Main] Cancelling Thread %d\n", i);
            result = pthread_cancel(threads[i]);
            //assert(result == 0);
        }
    }

    // Join all threads
    for (int i = 0; i < NUM_THREADS; ++i) {
        void* retval;
        result = pthread_join(threads[i], &retval);
        assert(result == 0);

        if (retval == (void*) -1) {
            printf("[Main] Thread %d was canceled\n", i);
        } else {
            printf("[Main] Thread %d returned value: %ld\n", i, (long)retval);
        }
    }

    printf("All threads handled successfully.\n");
}

int main() {
    
    test_combined_pthread_behavior();
    //getchar();
   
    return 0;
}