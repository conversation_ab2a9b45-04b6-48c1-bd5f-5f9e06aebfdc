#include <stdio.h>
#include "pthread.h"
#include "stdlib.h"
#include "string.h"

#define PAGE_SIZE 4096
#define NUM_PAGES 300

// Each page is 4KB, total is 40KB
char global_array[NUM_PAGES * PAGE_SIZE];

int main() {
    
    reserve_pages(450);

    printf("Starting test with %d pages...\n", NUM_PAGES);

    
    for (int i = 0; i < NUM_PAGES; ++i) {
        char *page = &global_array[i * PAGE_SIZE];
        memset(page, 42, PAGE_SIZE);
    }

   
   
    volatile int sum = 0;
    for (int i = 0; i < NUM_PAGES; ++i) {
       global_array[i * PAGE_SIZE]= 33;
    }
      

    printf("Accessed all pages. Dummy sum: %d\n", sum);
    printf("Sleeping to allow deduplication to run...\n");

    

    return 0;
}
