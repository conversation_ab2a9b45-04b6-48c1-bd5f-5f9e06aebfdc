#include <stdio.h>
#include <unistd.h>
#include "wait.h"

int main() {
    pid_t pid = fork();  // Create a child process

    if (pid == -1) {
        // Fork failed
        printf("Fork failed");
        return 1;
    }

    if (pid == 0) {
        // Child process: Sleep for 2 seconds
        printf("Child process (PID: %ld) is sleeping for 2 seconds...\n", pid);
        sleep(2);  // Simulate some work in the child process
        printf("Child process (PID: %ld) is done.\n", pid);

       
        return 6;
    } else {
        // Parent process: Wait for the child process to finish
        int status;
        printf("Parent process (PID: %ld) is waiting for child (PID: %ld)...\n", pid, pid);
        pid_t child_pid = waitpid(pid, &status, 0);  // Wait for the child to finish
        
        if (child_pid == -1) {
            printf("Waitpid failed");
            return 1;
        }
        printf("%d",WIFEXITED(status));
        // Check the exit status of the child process
        if (WIFEXITED(status)) 
        {
            printf("Child process (PID: %ld) exited normally with status %d.\n", child_pid, WEXITSTATUS(status));
        
        }   

    return 0;
    }
}