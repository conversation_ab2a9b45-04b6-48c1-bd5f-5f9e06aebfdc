#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"

int main()
{
   char* adress= mmap((void*)0x400,4096*3,PROT_READ|PROT_WRITE,MAP_ANONYMOUS|MAP_PRIVATE,-1,300);
    printf("parent adress:%p\n",adress);
    printf("%s\n",adress);

     adress[0]='A';
      adress[1]='B';
       adress[4097]='B';
      printf("%s\n",adress);
     munmap(adress,4096*3);
    
   

 



  return 0;
}
