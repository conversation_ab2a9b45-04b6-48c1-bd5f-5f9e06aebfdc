#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"
#include <string.h>
#include "fcntl.h"


int main() {
    const char* shm_name = "/multi_producer_shm";
    const size_t shm_size = 256;

    
    int shm_fd = shm_open(shm_name,O_CREAT|O_RDWR, 0);
    printf("%d",shm_fd);
  

 

 
    pid_t pid1 = fork();
    pid_t pid2 = fork();
    
  

    if (pid1 == 0) {  
       printf("%d",shm_fd);
        char* shared_mem = (char*) mmap(NULL, shm_size, PROT_READ | PROT_WRITE, MAP_SHARED, shm_fd, 0);
       

        
        const char* message = "Producer 1 wrote!";
        memcpy(shared_mem, message, shm_size);
        printf("Producer 1 wrote: %s\n", message);

       
        munmap(shared_mem, shm_size);
        //close(shm_fd);
        return 0;
    }

    if (pid2 == 0) {  
        printf("%d",shm_fd);
        char* shared_mem = (char*) mmap(NULL, shm_size, PROT_READ | PROT_WRITE, MAP_SHARED, shm_fd, 0);
        

       
        const char* message = "Producer 2 wrote!";
        memcpy(shared_mem, message, shm_size);
        printf("Producer 2 wrote: %s\n", message);

       
        munmap(shared_mem, shm_size);
        //close(shm_fd);
        return 0;
    }

   
    
    sleep(10);

    
    char* shared_mem = (char*) mmap(NULL, shm_size, PROT_READ, MAP_SHARED, shm_fd, 0);
    

  
    printf("Consumer read: %s\n", shared_mem);

 
    munmap(shared_mem, shm_size);
    close(shm_fd);
    shm_unlink(shm_name);  

    return 0;
}

