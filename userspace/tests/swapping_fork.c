#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>
#include "wait.h"
#include "stdlib.h"


#define NUM_PROCS 6
#define NUM_PAGES 1000
#define PAGE_SIZE 4096

 char* array[]={"Hello","World", "this", "should", "work", "hopeufffly", "again",NULL};
 pid_t pids[3];

int main() {
    
    char* big_mem = malloc(NUM_PAGES * PAGE_SIZE);
    if (!big_mem) {
        return 1;
    }

    // Fill memory to ensure it is actually allocated
    for (int i = 0; i < NUM_PAGES * PAGE_SIZE; i += PAGE_SIZE) {
        big_mem[i] = i % 256;
    }

    printf("[PARENT] Filled memory, forking processes now...\n");

    for (int i = 0; i < NUM_PROCS; ++i) {
        pids[i] = fork();
        if (pids[i] == 0) {
            if (pids[i] == 0) {
                // Regular child: accesses memory to trigger CoW
                printf("[CHILD %d] Accessing memory (should trigger CoW)...\n", i);
                for (int j = 0; j < NUM_PAGES * PAGE_SIZE; j += PAGE_SIZE * 8) {
                    big_mem[j] += 1;  // Write access
                }
                printf("[CHILD %d] Done, exiting...\n", i);
                exit(0);
            } else {
                // Child calls exec
                printf("[CHILD %d] Executing self again using exec()...\n", i);
                //printf("%d",execv("/usr/program.sweb",array)); 
                waitpid(pids[i],NULL,NULL);
                
            }
        }
    }

    // Parent waits
  

    printf("[PARENT] All children finished.\n");
    free(big_mem);
    return 0;
}
