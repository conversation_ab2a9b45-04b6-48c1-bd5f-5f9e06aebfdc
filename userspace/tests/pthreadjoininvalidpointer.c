#include <stdio.h>
#include "pthread.h"

pthread_t tid;
 pthread_t tid2;
 pthread_t tid3;
 pthread_t tid4;
 pthread_t tid5;

 void** ret_val=(void**) 0x10;

void* say_hello() {
  printf("Hello world\n");
  return (void*) 777;
}


void* say_hello2() {
  printf("Hello world\n");
  //pthread_join(tid,&ret_val );
   printf("Thread2 join result:%d\n", pthread_join(tid, ret_val));
   printf("%p\n", ret_val );
 
 return (void*) 110;
}



int main() {

printf("Test Start:-----------\n");

  
 pthread_create(&tid, NULL, (void *(*)(void*))say_hello, NULL);
 pthread_create(&tid2, NULL, (void *(*)(void*))say_hello2, NULL);

  printf("Test End:-----------\n");
  return 0;
}