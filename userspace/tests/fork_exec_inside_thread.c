#include <stdio.h>
#include <unistd.h>
#include "wait.h"
#include<pthread.h>


void* runner(void* arg) {
    pid_t pid = fork();
    if (pid == 0) {
        char *args[] = {"/usr/program.sweb", "fork+exec from thread", NULL};
        execv(args[0], args);
        _exit(1); // Shouldn't reach here
    }

    int status;
    waitpid(pid, &status, 0);
    printf("Thread done waiting.\n");
    return NULL;
}

int main() {
    pthread_t t;
    pthread_create(&t, NULL, runner, NULL);
    pthread_join(t, NULL);
    return 0;
}