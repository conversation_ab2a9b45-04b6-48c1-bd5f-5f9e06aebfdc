#include <stdio.h>
#include <string.h>
#include "pthread.h"
#include "fcntl.h"
#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"

int main() {
   
    const char *filename = "/usr/testfile.txt";
    char string[1]="a";
    char string2[2]="b";
    char string3[3]="c";
    int fd = shm_open(filename, O_RDWR|O_CREAT,0); 
    
    for(size_t i=0; i<4095; i++)
    {
         printf("%ld", write(fd,string, 1));
       

    }
      printf("%ld", write(fd,"\0", 1));
   
    for(size_t i=0; i<4095; i++)
    {
         printf("%ld", write(fd,string2, 1));
        

    }
    printf("%ld", write(fd,"\0", 1));

      for(size_t i=0; i<4095; i++)
    {
         printf("%ld", write(fd,string3, 1));
        

    }
    printf("%ld", write(fd,"\0", 1));
    //printf("%ld", write(fd,string,4096));
    
    

  
    char *mapped = mmap(NULL, 4096*2, PROT_READ | PROT_WRITE, MAP_PRIVATE, fd, 4096*2);
   
    printf("Original content: %s\n", (mapped));

    
   
      

    return 0;
}
