#include <stdio.h>
#include "pthread.h"
#include "stdlib.h"
#include "nonstd.h"
#include "string.h"
#include "wait.h"

#define PAGE_SIZE 4096
#define TOTAL_PAGES 1200
#define NUM_THREADS 100

char array[PAGE_SIZE * TOTAL_PAGES];
char array2[PAGE_SIZE * TOTAL_PAGES];

void* thread_func(void* arg) {
  

     for(size_t i=0 ; i<PAGE_SIZE * TOTAL_PAGES;i+=4096)
     {
        array[i] ='a';
     }

    return NULL;
}



int main() {
    // limit physical memory
    pthread_t threads[NUM_THREADS];
    int tids[NUM_THREADS];
    printf("Filling first half...\n");
    for (size_t i = 0; i < TOTAL_PAGES / 2; ++i) {
        array[i * PAGE_SIZE] = (char)i; // Write to first 500 pages
    }

    printf("Filling second half (should evict first)...\n");
    for (size_t i = TOTAL_PAGES / 2; i < TOTAL_PAGES; ++i) {
        array[i * PAGE_SIZE] = (char)i; // Write to second 500 pages
    }

    while (1)
    {
        /* code */
    
    
    for (int i = 0; i < NUM_THREADS; ++i) {
        tids[i] = i;
        pthread_create(&threads[i], NULL, thread_func, &tids[i]);
    }
    }

    for (int i = 0; i < NUM_THREADS; ++i) {
        pthread_join(threads[i], NULL);
    }

   


    printf("Swap-in test complete.\n");
    return 0;
}