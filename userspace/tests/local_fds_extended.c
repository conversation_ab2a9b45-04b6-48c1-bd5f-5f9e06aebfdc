#include <stdio.h>
#include <string.h>
#include "pthread.h"
#include "fcntl.h"
#include "unistd.h"
#include "wait.h"

void *readAndWrite(void *fd1)
{
    size_t fd = (size_t)fd1;

    int writer_result1 = write(fd, "A", sizeof(char[10]));
    printf("Parent: write result = %d sollte nicht -1 sein\n", writer_result1);

    char buffer[10];
    int read_result1 = read(fd, buffer, sizeof(buffer));
    printf("Parent: read result = %d sollte 0 sein\n", read_result1);
    return 0;
}

void* makestuff(size_t fd1, size_t fd2, size_t fd3, size_t fd4)
{
    size_t tid1, tid2, tid3, tid4;
    pthread_create(&tid1, NULL, readAndWrite, (void *)fd1);
    pthread_create(&tid2, NULL, readAndWrite, (void *)fd2);
    pthread_create(&tid3, NULL, readAndWrite, (void *)fd3);
    pthread_create(&tid4, NULL, readAndWrite, (void *)fd4);

    pthread_join(tid1, NULL);
    pthread_join(tid2, NULL);
    pthread_join(tid3, NULL);
    pthread_join(tid4, NULL);
    return 0;
}

int main()
{

    size_t fd1 = open("/usr/datafile.txt", O_CREAT | O_RDWR);
    if (fd1 < 0)
    {
        printf("Error opening file with fd: %ld", fd1);
    }
    size_t fd2 = open("/usr/datafile.txt", O_CREAT | O_RDWR);
    if (fd2 < 0)
    {
        printf("Error opening file with fd: %ld", fd2);
    }
    size_t fd3 = open("/usr/datafile.txt", O_CREAT | O_RDWR);
    if (fd3 < 0)
    {
        printf("Error opening file with fd: %ld", fd3);
    }
    size_t fd4 = open("/usr/datafile.txt", O_CREAT | O_RDWR);
    if (fd4 < 0)
    {
        printf("Error opening file with fd: %ld", fd4);
    }
    size_t pid;

    pid = fork();
    if (pid == 0)//child
    {
        makestuff(fd1, fd2, fd3, fd4);
        close(fd1);
        close(fd2);
        close(fd3);
        close(fd4);
    }
    else
    {
        waitpid(pid, NULL, NULL);
        makestuff(fd1, fd2, fd3, fd4);
        makestuff(fd1, fd2, fd3, fd4);
        makestuff(fd1, fd2, fd3, fd4);
        makestuff(fd1, fd2, fd3, fd4);
        makestuff(fd1, fd2, fd3, fd4);
        makestuff(fd1, fd2, fd3, fd4);
        makestuff(fd1, fd2, fd3, fd4);
        makestuff(fd1, fd2, fd3, fd4);
        makestuff(fd1, fd2, fd3, fd4);
        makestuff(fd1, fd2, fd3, fd4);
        makestuff(fd1, fd2, fd3, fd4);
        makestuff(fd1, fd2, fd3, fd4);
        makestuff(fd1, fd2, fd3, fd4);
        close(fd1);
        close(fd2);
        close(fd3);
        close(fd4);
    }
   

    
    return 0;
}