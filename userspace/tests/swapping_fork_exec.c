#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>
#include "wait.h"
#include "stdlib.h"


#define NUM_PROCS 1
#define NUM_PAGES 1000
#define PAGE_SIZE 4096

 char* array[]={"Hello","World", "this", "should", "work", "hopeufffly", "again",NULL};
 pid_t pids[5];

int main() {
    
    char* big_mem = malloc(NUM_PAGES * PAGE_SIZE);
    if (!big_mem) {
        return 1;
    }

    // Fill memory to ensure it is actually allocated
    for (int i = 0; i < NUM_PAGES * PAGE_SIZE; i += PAGE_SIZE) {
        big_mem[i] = i % 256;
    }

    printf("[PARENT] Filled memory, forking processes now...\n");

    for (int i = 0; i < NUM_PROCS; ++i) {
        pids[i] = fork();
           if (pids[i] == 0) {
            // In child process
            printf("[CHILD %d] Calling execv...\n", i);
            execv("/usr/program.sweb", array);

            // If execv fails
            
            exit(0);
        }
        else
        {
            waitpid(pids[i], NULL, 0);
        }
    }

   /**  for (int i = 0; i < NUM_PROCS; ++i) {
        waitpid(pids[i], NULL, 0);
    }**/
    // Parent waits
  

    printf("[PARENT] All children finished.\n");
    free(big_mem);
    return 0;
}
