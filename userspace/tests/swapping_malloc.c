#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "wait.h"
#include <stdio.h>
#include "pthread.h"
#include "stdlib.h"


#define ONE_MB (1024 * 1024)
#define ALLOC_MB 1500  // Try increasing to 3000+ on systems with more RAM
#define STRIDE 4096    // Touch every 4KB page





void allocate_and_touch_memory(int id) {
    size_t size = 4096*3000;
    char *mem = malloc(size);


    // Touch each page to force it to be backed by physical memory
    for (size_t i = 0; i < size; i += STRIDE) {
        mem[i] = (char)id;
    }

    printf("Process %d touched %zu MB\n", id, (size_t)ALLOC_MB);

    //sleep(10); // Keep memory resident for a while

    //free(mem);
}

int main() {

  
allocate_and_touch_memory( 1);
         
     

    
    
   
    

   

    printf("All done.\n");
    return 0;
}