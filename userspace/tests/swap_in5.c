#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define PAGE_SIZE 4096
#define TOTAL_PAGES 4000

char array[PAGE_SIZE * TOTAL_PAGES];
char array2[PAGE_SIZE * TOTAL_PAGES];
size_t checksum[TOTAL_PAGES];

size_t calculate_checksum(char *page) {
    size_t sum = 0;
    for (size_t i = 0; i < PAGE_SIZE; ++i) {
        sum += (unsigned char)page[i];
    }
    return sum;
}

int main() {
    printf("Filling first half and computing checksums...\n");
    for (size_t i = 0; i < TOTAL_PAGES / 2; ++i) {
        char *page = &array[i * PAGE_SIZE];
        memset(page, (char)i, PAGE_SIZE);  // Fill page
        checksum[i] = calculate_checksum(page);
    }

    printf("Filling second half (should evict first)...\n");
    for (size_t i = TOTAL_PAGES / 2; i < TOTAL_PAGES; ++i) {
        char *page = &array[i * PAGE_SIZE];
        memset(page, (char)i, PAGE_SIZE);  // Fill page
        checksum[i] = calculate_checksum(page);
    }

    printf("Verifying first half (should cause swap-ins)...\n");
    for (size_t i = 0; i < TOTAL_PAGES / 2; ++i) {
        char *page = &array[i * PAGE_SIZE];
        size_t current = calculate_checksum(page);
        if (current != checksum[i]) {
            printf("Checksum mismatch at page %zu! Expected %zu, got %zu\n",
                   i, checksum[i], current);
        }
    }

    printf("Verifying second half...\n");
    for (size_t i = TOTAL_PAGES / 2; i < TOTAL_PAGES; ++i) {
        char *page = &array[i * PAGE_SIZE];
        size_t current = calculate_checksum(page);
        if (current != checksum[i]) {
            printf("Checksum mismatch at page %zu! Expected %zu, got %zu\n",
                   i, checksum[i], current);
        }
    }

    printf("Causing further eviction by writing to second array...\n");
    for (size_t i = TOTAL_PAGES / 2; i < TOTAL_PAGES; ++i) {
        char *page = &array2[i * PAGE_SIZE];
        memset(page, (char)i, PAGE_SIZE);  // Trigger further page evictions
    }

    printf("Final verification of first half...\n");
    for (size_t i = 0; i < TOTAL_PAGES / 2; ++i) {
        char *page = &array[i * PAGE_SIZE];
        size_t current = calculate_checksum(page);
        if (current != checksum[i]) {
            printf("Final checksum mismatch at page %zu! Expected %zu, got %zu\n",
                   i, checksum[i], current);
        }
    }

    printf("Swap-in and eviction test complete.\n");
    return 0;
}