#include <stdio.h>
#include <string.h>
#include "pthread.h"
#include "fcntl.h"
#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"

int main() {
   
    const char *filename = "/usr/testfile.txt";
     const char *filename2 = "/usr/test.txt";
    char string[6]="Hello\0";
    int fd = open(filename, O_RDWR|O_CREAT); 
     int fd2 = open(filename2, O_RDWR|O_CREAT); 
    printf("%d\n",fd);
    write(fd,string,6);
  

    

  
    char *mapped = mmap(NULL, 4096*2, PROT_READ | PROT_WRITE, MAP_PRIVATE, fd, 0);
    char *mapped2 = mmap(NULL, 4096*2, PROT_READ | PROT_WRITE, MAP_SHARED, fd2, 0);
    size_t pid = fork();
    if(pid == 0)
    {
        int fd = open(filename, O_RDWR|O_CREAT); 
         int fd2 = open(filename2, O_RDWR|O_CREAT); 
        printf("%d",fd);
       char *mapped = mmap(NULL, 4096*2, PROT_READ | PROT_WRITE, MAP_PRIVATE, fd, 0); 
       char *mapped2 = mmap(NULL, 4096*2, PROT_READ | PROT_WRITE, MAP_SHARED, fd2, 0);
         mapped[0] = 'A';
         mapped[1] = 'A';  
         printf("Modified content: %s\n", mapped); 
         exit(0);
    }

      waitpid(pid,NULL,NULL);
   
    char buffer[6];
    lseek(fd,0,SEEK_SET);
    read(fd,buffer,6);
    printf("Contents of buffer:%s",buffer);

    
    //close(fd);

    munmap(mapped, 4096); 
      

    return 0;
}
