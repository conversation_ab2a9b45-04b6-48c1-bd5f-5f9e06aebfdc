#include <stdio.h>
#include <stdlib.h>
#include <string.h>


#define PAGE_SIZE 4096
#define TOTAL_PAGES 1011

char array[TOTAL_PAGES * PAGE_SIZE];
char array2[TOTAL_PAGES * PAGE_SIZE];

size_t calculate_checksum(char *page) {
    size_t sum = 0;
    for (size_t i = 0; i < PAGE_SIZE; ++i) {
        sum += (unsigned char)page[i];
    }
    return sum;
}

void test_linear_access() {
    printf("[Test] Linear access pattern\n");
    for (int i = 0; i < TOTAL_PAGES; ++i) array[i * PAGE_SIZE] = (char)(i % 256);
    for (int i = 0; i < TOTAL_PAGES; ++i) {
        if (array[i * PAGE_SIZE] != (char)(i % 256)) printf("Mismatch at %d\n", i);
    }
}

void test_repeated_access_small_set() {
    printf("[Test] Repeated access to small subset\n");
    for (int i = 0; i < TOTAL_PAGES; ++i) array[i * PAGE_SIZE] = (char)(i % 256);
    int sum = 0;
    for (int rep = 0; rep < 1000; ++rep) {
        for (int i = 0; i < 5; ++i) sum += array[i * PAGE_SIZE];
    }
    printf("Sum: %d\n", sum);
}

void test_alternating_pages() {
    printf("[Test] Alternating pages\n");
    for (int i = 0; i < TOTAL_PAGES; i += 2) array[i * PAGE_SIZE] = (char)(i % 256);
    for (int i = 1; i < TOTAL_PAGES; i += 2) array[i * PAGE_SIZE] = (char)(i % 256);
    for (int i = 0; i < TOTAL_PAGES; ++i) {
        if (array[i * PAGE_SIZE] != (char)(i % 256)) printf("Mismatch at %d\n", i);
    }
}

void test_write_then_read() {
    printf("[Test] Write then read\n");
    for (int i = 0; i < TOTAL_PAGES; ++i) array[i * PAGE_SIZE] = (char)(i % 256);
    for (int i = 0; i < TOTAL_PAGES; ++i) {
        if (array[i * PAGE_SIZE] != (char)(i % 256)) printf("Mismatch at %d\n", i);
    }
}

void test_under_limit_access() {
    printf("[Test] Under physical memory limit\n");
    for (int i = 0; i < 50; ++i) array[i * PAGE_SIZE] = (char)(i % 256);
    for (int i = 0; i < 50; ++i) {
        if (array[i * PAGE_SIZE] != (char)(i % 256)) printf("Mismatch at %d\n", i);
    }
}

void test_stack_growth() {
    printf("[Test] Stack growth\n");
    void recurse(int depth) {
        char local[PAGE_SIZE];
        memset(local, (char)(depth % 256), PAGE_SIZE);
        if (depth > 0) recurse(depth - 1);
    }
    recurse(50);
}

void test_two_array_alternation() {
    printf("[Test] Two array alternation\n");
    for (int i = 0; i < TOTAL_PAGES; ++i) array[i * PAGE_SIZE] = (char)(i % 256);
    for (int i = 0; i < TOTAL_PAGES; ++i) array2[i * PAGE_SIZE] = (char)(i % 256);
    for (int i = 0; i < TOTAL_PAGES; ++i) {
        if (array[i * PAGE_SIZE] != (char)(i % 256)) printf("Mismatch at array[%d]\n", i);
        if (array2[i * PAGE_SIZE] != (char)(i % 256)) printf("Mismatch at array2[%d]\n", i);
    }
}

void test_read_only_verification() {
    printf("[Test] Read-only verification\n");
    for (int i = 0; i < TOTAL_PAGES; ++i) memset(&array[i * PAGE_SIZE], (char)(i % 256), PAGE_SIZE);
    for (int i = 0; i < TOTAL_PAGES; ++i) {
        if (array[i * PAGE_SIZE] != (char)(i % 256)) printf("Mismatch at %d\n", i);
    }
}

void test_page_reuse_after_eviction() {
    printf("[Test] Page reuse after eviction\n");
    array[0] = 123;
    for (int i = 1; i < TOTAL_PAGES; ++i) array[i * PAGE_SIZE] = (char)(i % 256);
    if (array[0] != 123) printf("Eviction corrupted reused page!\n");
}

void test_patterned_write_checksum() {
    printf("[Test] Patterned write and checksum\n");
    size_t expected[TOTAL_PAGES];
    for (int i = 0; i < TOTAL_PAGES; ++i) {
        memset(&array[i * PAGE_SIZE], (char)(i % 256), PAGE_SIZE);
        expected[i] = calculate_checksum(&array[i * PAGE_SIZE]);
    }
    for (int i = 0; i < TOTAL_PAGES; ++i) {
        size_t current = calculate_checksum(&array[i * PAGE_SIZE]);
        if (current != expected[i]) printf("Checksum mismatch at page %d\n", i);
    }
}

int main() {
   /**  test_linear_access();
    test_repeated_access_small_set();
    test_alternating_pages();
    test_write_then_read();
    test_under_limit_access();
    test_stack_growth();**/
    test_two_array_alternation();
    test_read_only_verification();
    test_page_reuse_after_eviction();
    test_patterned_write_checksum();
    printf("[Done] All single-threaded tests completed.\n");
    return 0;
}
