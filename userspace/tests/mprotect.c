#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"

int main()
{
   char* adress= mmap(0,4096*2,PROT_READ|PROT_WRITE,MAP_ANONYMOUS|MAP_PRIVATE,-1,0);
    *adress ='a';
    *(adress+1) ='a';
    *(adress+2) ='\0';
    *(adress+4096) ='a';
    printf("parent adress:%p\n",adress);
    printf("%d",mprotect(adress,4096*2,PROT_READ));

    *(adress+4096) ='a';
    printf("%s\n",adress);
     
    
   

 



  return 0;
}