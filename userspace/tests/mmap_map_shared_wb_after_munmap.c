#include <stdio.h>
#include <string.h>
#include "pthread.h"
#include "fcntl.h"
#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"

int main() {
   
    const char *filename = "/usr/testfile.txt";
     const char *filename2 = "/usr/test.txt";
    char string[6]="Hello\0";
    int fd = open(filename, O_RDWR|O_CREAT); 
   
 
    char *mapped = mmap(NULL, 4096*2, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);

    mapped[0] = 'A';
    mapped[1] = 'A';  

   
     munmap(mapped, 4096*2);
    char buffer[6];
    lseek(fd,0,SEEK_SET);
    read(fd,buffer,6);
    printf("Contents of buffer:%s",buffer);

    
    //close(fd);

   
      

    return 0;
}
