#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <string.h>
#include "nonstd.h"
#include "wait.h"

#define ARRAY_SIZE 2000
char array[4096 * ARRAY_SIZE];

char second_array[4096 * 1000];

int main()
{
    int errors = 0;
    for (size_t i = 4095; i < 4096 * ARRAY_SIZE; i++)
    {
        array[i] = 'a';
    }

    for (size_t i = 4095; i < 4096 * 1000; i++)
    {
        second_array[i] = 'b';
    }

    char local_array[4096 * 10];

    for (size_t i = 4095; i < 4096 * ARRAY_SIZE; i++)
    {
        if (array[i] != 'a')
        {
            errors++;
        }
    }

    for (size_t i = 4095; i < 4096 * 10; i++)
    {
        local_array[i] = 'l';
    }

    for (size_t i = 4095; i < 4096 * 1000; i++)
    {
        if (second_array[i] != 'b')
        {
            errors++;
        }
    }

    for (size_t i = 4095; i < 4096 * 10; i++)
    {
        if (local_array[i] != 'l')
        {
            errors++;
        }
    }

    printf("Errors: %d", errors);
    return 0;
}
