#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"

int main()
{

     char* adress= mmap((void*)0x400,4096*3000,PROT_READ|PROT_WRITE,MAP_ANONYMOUS|MAP_PRIVATE,-1,300);

      for(size_t i =0; i < 4096*3000; i+=4096)
    {
        adress[i]= (char)'a' + i;
    }
  

  
    printf("parent adress:%p\n",adress);
    *adress ='a';
    *(adress+1) ='a';
    *(adress+2) ='\0';
    *(adress+4098) ='a';
    *(adress+4096*2+1) ='a';
    //*(adress+4096*3+1) ='a';
    printf("%s\n",adress);
    printf("%c\n", *(adress+4098));
    printf("%c\n", *(adress+4096*2+1));



     munmap(adress,4096*3);
    
   

 



  return 0;
}
