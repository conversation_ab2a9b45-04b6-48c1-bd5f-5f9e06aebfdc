#include <stdio.h>
#include "pthread.h"
#include "semaphore.h"

sem_t lock;
size_t count = 0;

void say_hello() {
    for(int i = 0;i < 100000;i++)
    {
        sem_wait(&lock);
        count++;
        //printf("Count1: %ld\n",count);
        sem_post(&lock);
    }

}



int main() {
    size_t tid;
    sem_init(&lock,0,1);
    for(size_t i = 0;i < 3;i++)
    {
        pthread_create(&tid, NULL, (void *(*)(void*))say_hello, NULL);

    }
    pthread_join(tid,0);
    printf("Counter: %ld\n",count);
    return 0;
}

