#include <stdio.h>
#include "pthread.h"

pthread_mutex_t lock;
pthread_cond_t cond;
size_t buffer = 10;
size_t arr[10];
size_t count = 0;
size_t consumer = 5;
size_t producer = 5;

void produce() {

    for(size_t i = 0;i < 100;i++)
    {
        pthread_mutex_lock(&lock);
        while(count == buffer) {
            pthread_cond_wait(&cond, &lock);
        }
        arr[count] = i;

        count++;
        printf("produce: %ld\n",i);
        pthread_cond_signal(&cond);
        pthread_mutex_unlock(&lock);
    }
    printf("End Producter\n");
}
void consume() {
    for(size_t i = 0;i < 100;i++)
    {
        pthread_mutex_lock(&lock);
        while(count == 0) {
            pthread_cond_wait(&cond, &lock);
        }

        count--;
        printf("consume: %ld\n",arr[count]);
        pthread_cond_signal(&cond);
        pthread_mutex_unlock(&lock);
    }
    printf("End Consumer\n");
}


int main() {
    size_t tid[producer +consumer];
    pthread_mutex_init(&lock,0);
    pthread_cond_init(&cond,0);
    for(size_t i = 0;i < producer; i++)
    {
        pthread_create(&tid[i], NULL, (void *(*)(void*))produce, NULL);
    }
    for(size_t i = 0;i < producer; i++)
    {
        pthread_create(&tid[producer+i], NULL, (void *(*)(void*))consume, NULL);
    }

    size_t retval;
    for(size_t i = 0;i < (producer + consumer);i++) {
        pthread_join(tid[i], (void *) &retval);
    }
    return 0;
}

