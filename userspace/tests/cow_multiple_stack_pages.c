#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>
#include "wait.h"



#define PAGE_SIZE 4096
#define PAGES     4
#define STACK_ARRAY_SIZE (PAGE_SIZE * PAGES)

int main() {
    // Large local array on the stack (may span multiple pages)
    char stack_buffer[STACK_ARRAY_SIZE];

    // Initialize with 'S' to distinguish from other tests
    for (int i = 0; i < PAGES; i++) {
        stack_buffer[i * PAGE_SIZE] = 'S' + i;  // e.g., S, T, U, V
    }

    int pid = fork();
    if (pid == 0) {
        // Child writes to 2 of the 4 pages
        stack_buffer[PAGE_SIZE]     = 'X'; // Page 1
        stack_buffer[PAGE_SIZE * 2] = 'Y'; // Page 2

        printf("Child stack buffer:\n");
        for (int i = 0; i < PAGES; i++) {
            printf("  Page %d: %c\n", i, stack_buffer[i * PAGE_SIZE]);
        }

        _exit(0);
    } else {
        waitpid(pid, 0, 0);

        printf("Parent stack buffer:\n");
        for (int i = 0; i < PAGES; i++) {
            printf("  Page %d: %c\n", i, stack_buffer[i * PAGE_SIZE]);
        }
    }

    return 0;
}