#include <stdio.h>
#include "pthread.h"
#include "unistd.h"


void func(void* arg){
    void** args = (void**)arg;
    while (1)
    {
        printf("Process: %d, Thread: %ld\n", getPid(), (size_t)args[1]);
        sleep(1);
    }
    
}

int main()
{
    printf("Then press ENTER to exit!\n");
    //sleep(1);

    size_t tid1 = 1;
    size_t tid2 = 2;
    size_t tid3 = 3;
    size_t tid4 = 4;
    size_t tid5 = 5;

    size_t pid = 0;

    pthread_create(&tid1, NULL, (void *(*)(void *))func, (void*[]){(void*)pid, (void*)tid1});
    pthread_create(&tid2, NULL, (void *(*)(void *))func, (void*[]){(void*)pid, (void*)tid2});
    pthread_create(&tid3, NULL, (void *(*)(void *))func, (void*[]){(void*)pid, (void*)tid3});
    pthread_create(&tid4, NULL, (void *(*)(void *))func, (void*[]){(void*)pid, (void*)tid4});
    pthread_create(&tid5, NULL, (void *(*)(void *))func, (void*[]){(void*)pid, (void*)tid5});

    fork();



    getchar();
    pthread_cancel(tid1);
    pthread_cancel(tid2);
    pthread_cancel(tid3);
    pthread_cancel(tid4);
    pthread_cancel(tid5);

    pthread_join(tid1, NULL);
    pthread_join(tid2, NULL);
    pthread_join(tid3, NULL);
    pthread_join(tid4, NULL);
    pthread_join(tid5, NULL);
    
    return 0;
}

