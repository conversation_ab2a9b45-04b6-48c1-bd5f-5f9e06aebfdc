#include <stdio.h>
#include <string.h>
#include "pthread.h"
#include "fcntl.h"
#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"
#include "string.h"


#define FILE_PATH "/usr/testfile.txt"
#define FILE_SIZE 4096

int main() {
    const char* message = "Hello, mmap with writeback!\n";

    // 1. Open file for read/write
    int fd = open(FILE_PATH, O_RDWR|O_CREAT);
   

  
    char* mapped = mmap(NULL, FILE_SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
   

    // 4. Write to the mapped memory
    memcpy(mapped, message,29);

 

    // 6. Unmap the file
    munmap(mapped, FILE_SIZE);

    // 7. Read the file contents into a buffer
    lseek(fd, 0, SEEK_SET);  // rewind to start of file
    char buffer[128] = {0};
    size_t bytes_read = read(fd, buffer, sizeof(buffer) - 1);
    
    printf("%s",buffer);
   

    // 8. Close file
    close(fd);

    return 0;
}
