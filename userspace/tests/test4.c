#include <stdio.h>
#include <stdlib.h>

#define PAGE_SIZE 4096
#define NUM_PAGES (1024 * 8)  // 8K pages = 32MB of virtual memory (adjust if needed)

unsigned int my_seed = 42;

unsigned int my_rand() {
    my_seed = (1103515245 * my_seed + 12345) & 0x7fffffff;
    return my_seed;
}



int main() {
    // Allocate a large block of memory
    char *mem = (char *)malloc(NUM_PAGES * PAGE_SIZE);
    if (!mem) {
        printf("Memory allocation failed\n");
        return 1;
    }

    printf("Starting swapping test: touching %d pages (%d MB)...\n",
           NUM_PAGES, (NUM_PAGES * PAGE_SIZE) / (1024 * 1024));

    volatile int sum = 0;

    while (1) {
        // Write to each page to ensure it gets mapped and dirtied
        for (int i = 0; i < NUM_PAGES; i++) {
            mem[i * PAGE_SIZE] = (char)((i + my_rand()) % 256);
        }

        // Read from each page to keep it active and avoid compiler optimization
        sum = 0;
        for (int i = 0; i < NUM_PAGES; i++) {
            sum += mem[i * PAGE_SIZE];
        }

        // Optional: Print periodically to show progress
        printf("Sum = %d\n", sum);
    }

    // This part is unreachable but good practice
    free(mem);
    return 0;
}