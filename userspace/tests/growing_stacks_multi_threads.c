/*
---
category: fast
tags: growing_stacks
description: "Checks if 19 pages can be created for a big array and and read from and this for multible threads at the same time"
expect_exit_codes:
*/
#include "string.h"
#include "stdio.h"
#include "pthread.h"

#define NUM_THREADS 10
#define PAGE_SIZE 4096
#define PAGES 14
size_t test_success = 0;

void *needPages(void *tid)
{
    int t_num = *(int*)tid;
    char array[PAGE_SIZE * PAGES] = {0};

    for (size_t i = 0; i < PAGES; i++)
    {
        if (array[PAGE_SIZE * i] != 0)
        {
            test_success = -1;
        }
    }
    printf("Thread %d successfully mapped 16 pages\n", t_num);
    return 0;
}

int main()
{
    pthread_t tid[NUM_THREADS];
    size_t t_nums[NUM_THREADS];

    for (size_t i = 0; i < NUM_THREADS; i++)
    {
        t_nums[i] = i;
        pthread_create(&tid[i], NULL, needPages, &t_nums[i]);
    }

    for (size_t i = 0; i < NUM_THREADS; i++)
    {
        pthread_join(tid[i], NULL);
    }

    if (test_success == -1)
    {
        return -1;
    }
    printf("Test successful");
    return 0;
}