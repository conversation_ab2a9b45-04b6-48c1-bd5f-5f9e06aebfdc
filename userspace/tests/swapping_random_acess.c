#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

#define PAGE_SIZE 4096
#define NUM_PAGES 3000

unsigned int my_seed = 42;

unsigned int my_rand() {
    my_seed = (1103515245 * my_seed + 12345) & 0x7fffffff;
    return my_seed;
}




int main() {
    char* pages[NUM_PAGES];
    my_rand();

    for (int i = 0; i < NUM_PAGES; ++i) {
        pages[i] = malloc(PAGE_SIZE);
        pages[i][0] = (char)(i & 0xFF);
    }

    for (int i = 0; i < 100000; ++i) {
        int index =  my_rand() % NUM_PAGES;
        char val = pages[index][0];
        if (val != (char)(index & 0xFF)) {
            printf("Corruption at %d: got %d, expected %d\n", index, val, index & 0xFF);
            return 1;
        }
    }

    printf("Random access swap test passed.\n");
    return 0;
}