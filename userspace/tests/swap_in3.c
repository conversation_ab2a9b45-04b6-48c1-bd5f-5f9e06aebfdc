#include <stdio.h>
#include "pthread.h"
#include "stdlib.h"
#include "nonstd.h"
#include "string.h"
#include "wait.h"

#define PAGE_SIZE 4096
#define TOTAL_PAGES 1200


char array[PAGE_SIZE * TOTAL_PAGES];
char array2[PAGE_SIZE * TOTAL_PAGES];

int main() {
    // limit physical memory

    printf("Filling first half...\n");
    for (size_t i = 0; i < TOTAL_PAGES / 2; ++i) {
        array[i * PAGE_SIZE] = (char)i; // Write to first 500 pages
    }

    printf("Filling second half (should evict first)...\n");
    for (size_t i = TOTAL_PAGES / 2; i < TOTAL_PAGES; ++i) {
        array[i * PAGE_SIZE] = (char)i; // Write to second 500 pages
    }

   

    printf("Reading first half again (should cause swap-ins)...\n");
    for (size_t i = 0; i < TOTAL_PAGES / 2; ++i) {
        if (array[i * PAGE_SIZE] != (char)i) {
            printf("Swap-in failed at page %zu! Expected %d, got %d\n",
                   i, (int)i, array[i * PAGE_SIZE]);
        }
    }

    printf("Reading second half \n");
    for (size_t i = TOTAL_PAGES / 2; i < TOTAL_PAGES; ++i) {
        if (array[i * PAGE_SIZE] != (char)i) {
            printf("Swap-in failed at page %zu! Expected %d, got %d\n",
                   i, (int)i, array[i * PAGE_SIZE]);
        }
    }

    printf("Causing swap out again\n");
    for (size_t i = TOTAL_PAGES / 2; i < TOTAL_PAGES; ++i) {
        array2[i * PAGE_SIZE] = (char)i; // Write to second 500 pages
    }

    printf("Swap-in test complete.\n");
    return 0;
}