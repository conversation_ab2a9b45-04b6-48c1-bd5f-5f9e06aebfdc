#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>
#include "wait.h"




#define PAGE_SIZE 4096
static char big_buf[PAGE_SIZE * 4];

void* dummy_thread(void* arg) {
   
  pthread_setcanceltype(PTHREAD_CANCEL_ASYNCHRONOUS,NULL);

    while (1);  // Just keep it alive
    return NULL;
}

int main() {
    big_buf[0] = 'M';
    big_buf[PAGE_SIZE * 3] = 'N';

    pthread_t thread;
    pthread_create(&thread, NULL, dummy_thread, NULL);

    int pid = fork();
    if (pid == 0) {
        // Child modifies a page
        big_buf[PAGE_SIZE * 2] = 'Z';
        printf("Child: %c %c %c\n", big_buf[0], big_buf[PAGE_SIZE * 2], big_buf[PAGE_SIZE * 3]);
        _exit(0);
    } else {
        waitpid(pid, 0, 0);
        printf("Parent: %c %c %c\n", big_buf[0], big_buf[PAGE_SIZE * 2], big_buf[PAGE_SIZE * 3]);
        pthread_cancel(thread);
       
        pthread_join(thread, NULL);
    }

    return 0;
}