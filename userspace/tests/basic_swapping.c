#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#define PAGE_SIZE 4096
#define NUM_PAGES 1000

int main() {
    char* pages[NUM_PAGES];
    for (int i = 0; i < NUM_PAGES; ++i) {
        pages[i] = malloc(PAGE_SIZE);
        if (!pages[i]) {
            printf("malloc failed at page %d\n", i);
            return 1;
        }
        pages[i][0] = i % 256;  // Touch first byte
    }

    // Access earlier pages again (should cause swap-in)
    for (int i = 0; i < NUM_PAGES; ++i) {
        if (pages[i][0] != i % 256) {
            printf("Memory corruption or failed swap-in at %d\n", i);
            return 1;
        }
    }

    printf("Basic swap-in/out test passed.\n");
    return 0;
}