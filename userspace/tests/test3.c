#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <unistd.h>

#define NUM_THREADS 64
#define PAGES_PER_THREAD 512  // Adjust if this doesn't fill memory
#define PAGE_SIZE 4096

void* thread_func(void* arg) {
    char* mem = malloc(PAGES_PER_THREAD * PAGE_SIZE);
   

    // Touch every page to fault them in
    for (int i = 0; i < PAGES_PER_THREAD; ++i) {
        mem[i * PAGE_SIZE] = (char)i;
    }

    // Immediately free (memory used, but IPT may be empty after)
     printf(" %p", mem);
    free(mem);

    return NULL;
}

int main() {
    pthread_t threads[NUM_THREADS];

    printf("[Main] Launching threads to allocate + release lots of memory.\n");
    for (int i = 0; i < NUM_THREADS; ++i) {
        pthread_create(&threads[i], NULL, thread_func, NULL);
    }

    for (int i = 0; i < NUM_THREADS; ++i) {
        pthread_join(threads[i], NULL);
    }

    printf("[Main] Threads finished. Forcing more allocation to trigger swap.\n");

    // Now try to allocate more to force swapping — but IPT may be empty now
    char* stress = malloc(PAGE_SIZE * 500);
   
    for (int i = 0; i < 500; ++i) {
        stress[i * PAGE_SIZE] = 'X';
    }

    printf("[Main] Done.\n");
    free(stress);

    return 0;
}