#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"


int main() {
    // Allocate one page (usually 4096 bytes) of shared anonymous memory
    size_t size = 4096;
    int *shared_mem = mmap(NULL, size, PROT_READ | PROT_WRITE,
                           MAP_SHARED | MAP_ANONYMOUS, -1, 0);


    // Initialize shared memory
    shared_mem[0] = 42;

    pid_t pid = fork();
    if (pid == 0) {
        // Child process
        printf("Child sees shared_mem[0] = %d\n", shared_mem[0]);
        shared_mem[0] = 99;
        printf("Child sets shared_mem[0] = %d\n", shared_mem[0]);
        _exit(0);  // Use _exit() to avoid flushing stdio twice
    } else {
        // Parent process
        waitpid(pid,NULL,NULL);  // Wait for child to finish
        printf("<PERSON><PERSON> sees shared_mem[0] = %d\n", shared_mem[0]);
    }

    // Clean up
    munmap(shared_mem, size);
    return 0;
}
