#include <unistd.h>
#include <stdio.h>
#include<pthread.h>

pthread_t tid;
pthread_t tid;
 pthread_t tid2;
 pthread_t tid3;
 pthread_t tid4;
 pthread_t tid5;
void*ret_val=0;

 char* array[]={"Hello","World",NULL};

 

void* say_hello() {
  
 
 printf("Hello World");
 
 
  return (void*) 777;
}





int main() {
    
   for(size_t i=0; i< 285; i++)
 {
  
 pthread_create(&tid, NULL, (void *(*)(void*))say_hello, NULL);
 
  
 }
 
//getchar();
 execv("/usr/program.sweb",array); 
 
 



  return 0;
}