
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include "wait.h"

#define PAGE_SIZE 4096
#define NUM_PAGES 500
#define SWAP_TRIGGER_PAGES 1000

char shared_array[NUM_PAGES * PAGE_SIZE];

char swap_trigger[SWAP_TRIGGER_PAGES * PAGE_SIZE];

int main() {
    printf("COW Swapping Test: Initializing shared array...\n");
    
    for (int i = 0; i < NUM_PAGES; i++) {
        shared_array[i * PAGE_SIZE] = (char)(i % 256);
        shared_array[i * PAGE_SIZE + 1] = (char)((i + 128) % 256);
    }
    
    printf("COW Swapping Test: Forking child process...\n");
    pid_t child_pid = fork();
    
    if (child_pid < 0) {
        printf("Fork failed!\n");
        return 1;
    }
    
    if (child_pid == 0) {
        printf("Child: Modifying every other page \n");
        
        for (int i = 0; i < NUM_PAGES; i += 2) {
            shared_array[i * PAGE_SIZE] += 1;
            if (i % 50 == 0) {
                printf("Child: Modified page %d\n", i);
            }
        }
        
        printf("Child: Triggering swapping...\n");
        for (int i = 0; i < SWAP_TRIGGER_PAGES; i++) {
            swap_trigger[i * PAGE_SIZE] = (char)(i % 256);
            if (i % 100 == 0) {
                printf("Child: Allocated page %d to trigger swapping\n", i);
            }
        }
        
        printf("Child: Verifying COW pages after swapping\n");
        int errors = 0;
        
        for (int i = 0; i < NUM_PAGES; i++) {
            char expected;
            if (i % 2 == 0) {
                expected = (char)((i % 256) + 1);
            } else {
                expected = (char)(i % 256);
            }
            
            if (shared_array[i * PAGE_SIZE] != expected) {
                printf("Child: ERROR on page %d - Expected %d, got %d\n", 
                       i, (int)expected, (int)shared_array[i * PAGE_SIZE]);
                errors++;
                if (errors >= 5) break;
            }
            
            if (i % 50 == 0) {
                printf("Child: Verified page %d\n", i);
            }
        }
        
        if (errors == 0) {
            printf("Child: All COW pages correctly swapped in!\n");
            exit(0);
        } else {
            printf("Child: Found %d errors in COW pages\n", errors);
            exit(1);
        }
    } else {
        printf("Parent: Waiting for child to complete...\n");
        
        int status;
        waitpid(child_pid, &status, 0);
        
        if (WIFEXITED(status)) {
            printf("Parent: Child exited with status %d\n", WEXITSTATUS(status));
            
            printf("Parent: Verifying original pages\n");
            int errors = 0;
            
            for (int i = 0; i < NUM_PAGES; i++) {
                char expected = (char)(i % 256);
                
                if (shared_array[i * PAGE_SIZE] != expected) {
                    printf("Parent: ERROR on page %d - Expected %d, got %d\n", 
                           i, (int)expected, (int)shared_array[i * PAGE_SIZE]);
                    errors++;
                    if (errors >= 5) break;
                }
                
                if (i % 50 == 0) {
                    printf("Parent: Verified page %d\n", i);
                }
            }
            
            if (errors == 0) {
                printf("Parent: All original pages correctly preserved!\n");
                return WEXITSTATUS(status);
            } else {
                printf("Parent: Found %d errors in original pages\n", errors);
                return 1;
            }
        } else {
            printf("Parent: Child did not exit normally\n");
            return 1;
        }
    }
}