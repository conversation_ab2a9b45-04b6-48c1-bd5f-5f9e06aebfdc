#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"


int main()
{
    
    char* adress= mmap(0,4096*3,PROT_READ|PROT_WRITE,MAP_ANONYMOUS|MAP_SHARED,-1,0);
     
     size_t pid =fork();
     if(pid==0)
     {
       *adress='h';
       *(adress+1)='s';
       *(adress+2)='\0';
       
       munmap(adress,4096*3);
      
        
     }
     else
     {
      waitpid(pid,NULL,NULL);
       printf("%s",adress);
       munmap(adress,4096*3);
      *(adress+4096)='\0';  

     }
   

 



  return 0;
}
