/*
---
category: fast
tags: cow
description: "Checks if forked and cowed pages are rightfully isolated"
expect_exit_codes: 
*/
#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>
#include "wait.h"

#define PAGE_SIZE 4096
static char buffer[PAGE_SIZE * 2]; // 2 pages

int main() {
    buffer[0] = 'A';         // page 1
    buffer[PAGE_SIZE] = 'B'; // page 2
    char test;

    int pid = fork();
    if (pid == 0) {
        buffer[PAGE_SIZE] = 'Z';  // Modify second page — should COW only this one
        printf("Child: buffer[PAGE_SIZE] = %c\n", buffer[PAGE_SIZE]);
        _exit(0);
    } else {
        waitpid(pid, 0, 0);
        printf("Parent: buffer[PAGE_SIZE] = %c\n", buffer[PAGE_SIZE]);
        test = buffer[PAGE_SIZE];
    }

    if (test == 'B')
    {
        return 0;
    }else{
        return -1;
    }
}