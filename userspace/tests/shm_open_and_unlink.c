#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"
#include <string.h>
#include "fcntl.h"



int main() {
    


     printf("%d\n",    shm_open("test1",O_CREAT|O_RDWR,0));
     printf("%d\n",   shm_open("test2",O_CREAT|O_RDWR,0));
     printf("%d\n",    shm_open("test3",O_CREAT|O_RDWR,0));
     printf("%d\n",  shm_unlink("test2")); 
     printf("%d\n",     shm_unlink("test3")); 
     printf("%d\n",   shm_unlink("test1")); 
     printf("%d\n",   shm_unlink("test5")); 
      printf("%d\n",   shm_unlink((char*) 0x5));
     

    

    
   
    

    return 0;
}