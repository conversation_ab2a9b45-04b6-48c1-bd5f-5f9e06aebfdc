#include <stdio.h>
#include <string.h>
#include "pthread.h"
#include "fcntl.h"
#include "stdlib.h"
#include "mman.h"
#include "stdio.h"
#include "wait.h"
#include "string.h"


int main() {
    const char* filename = "/usr/testfile.txt";
    const size_t size = 4096;
    const char* message = "Data written via mmap with MAP_SHARED";

    // Step 1: Create and size the file
    int fd = open(filename, O_RDWR|O_CREAT);
   

   

    // Step 2: mmap the file with MAP_SHARED
    char* map = mmap(NULL, size, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
    

    // Step 3: Write to the memory and flush
    memcpy(map, message,39);
  

    munmap(map, size);
    close(fd);

    // Step 4: Reopen and read file directly to verify writeback
    fd = open(filename, O_RDONLY);
    

    char buffer[128] = {0};
    ssize_t n = read(fd, buffer, sizeof(buffer) - 1);
  

    close(fd);
    printf("Read from file after mmap write: '%s'\n", buffer);

    return 0;
}