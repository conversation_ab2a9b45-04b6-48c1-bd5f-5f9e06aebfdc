#include <unistd.h>
#include <stdio.h>
#include<pthread.h>
#include "wait.h"


void* thread_func(void* arg) {
    while (1) {
        printf("Secondary thread still alive?!\n");
        sleep(1);
    }
    return NULL;
}

int main() {
    pthread_t tid;
    pthread_create(&tid, NULL, thread_func, NULL);

    sleep(1); 

    char *args[] = {"/usr/program.sweb", "exec replaces everything", NULL};
    execv(args[0], args); 


    _exit(1);
}