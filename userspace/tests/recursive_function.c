#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <unistd.h>

#define NUM_THREADS 200
#define STACK_DEPTH 10  // Try increasing this if needed

void recurse(int depth, int tid) {
    char local_data[4096];  // 1 page per stack frame

    // Touch the local page so it actually gets mapped
    for (int i = 0; i < 4096; i += 128) {
        local_data[i] = (char)(tid + depth);
    }

    if (depth > 0) {
        recurse(depth - 1, tid);
    }
    printf("%p",local_data);
    getchar();
}

void* thread_func(void* arg) {
    int tid = *(int*)arg;
    recurse(STACK_DEPTH, tid);
    return NULL;
}

int main() {
    pthread_t threads[NUM_THREADS];
    int ids[NUM_THREADS];

    printf("Launching %d threads with recursive stack...\n", NUM_THREADS);

    for (int i = 0; i < NUM_THREADS; ++i) {
        ids[i] = i;
       pthread_create(&threads[i], NULL, thread_func, &ids[i]) ;
        
    }

    for (int i = 0; i < NUM_THREADS; ++i) {
        pthread_join(threads[i], NULL);
    }

    printf("Test completed.\n");
    return 0;
}