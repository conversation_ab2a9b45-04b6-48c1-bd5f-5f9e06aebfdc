#include <stdio.h>
#include "pthread.h"
#include "stdlib.h"
#include "nonstd.h"
#include "string.h"
#include "wait.h"

char testpage[4096];

int main()
{
    
    memset(testpage, 'A', 4096);
    size_t vpn = (size_t)testpage / 4096;
    printf("vpn: %zu\n", vpn);

    

    size_t swapped_out_page = swap_page_out(vpn);
     printf("swapped out page: %d\n", (int)swapped_out_page);

    printf("Accessing swapped out page\n");
    char value = (char)testpage[0];

    printf("First char in page: %c should be A\n", value);


    swapped_out_page = swap_page_out(vpn);
    printf("swapped out page: %d\n", (int)swapped_out_page);


    printf("Accessing swapped out page\n");
    value = (char)testpage[0];

    printf("First char in page: %c should be A\n", value);

    return (0);
}