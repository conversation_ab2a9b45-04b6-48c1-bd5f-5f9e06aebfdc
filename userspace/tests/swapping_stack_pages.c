#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <string.h>
#include "nonstd.h"  // for reserve_pages if needed
#include "wait.h"
#define NUM_THREADS 80
#define LOCAL_ARRAY_SIZE (4096 * 10)


char array[4096*8000];
char array2[4096*8000];

void* thread_func(void* arg) {
    int tid = *(int*)arg;

    // Allocate a large local array on the stack
    char local_array[LOCAL_ARRAY_SIZE];

    // Touch memory to ensure page faults
    for (size_t i = 4095; i < LOCAL_ARRAY_SIZE; ++i) {
        local_array[i] = 'A' + (tid % 26);  // Use different char per thread
    }

    // Print the address and first/last bytes for verification
    printf("[Thread %d] array at %p, first='%c', last='%c'\n",
           tid, (void*)local_array, local_array[4095], local_array[LOCAL_ARRAY_SIZE - 1]);

    return NULL;
}

int main() {
    pthread_t threads[NUM_THREADS];
    int tids[NUM_THREADS];

    
   
     for(size_t i =4095; i < 4096*8000; i++)
     {
        array[i]='a';       
     }


    printf("[Main] Launching %d threads with large local arrays...\n", NUM_THREADS);

  for (int i = 0; i < NUM_THREADS; ++i) {
        tids[i] = i;
        pthread_create(&threads[i], NULL, thread_func, &tids[i]);
    }

     
    pid_t pid = fork();

     if(pid==0)
    {  
        printf("Child allocates memory");
        
          for(size_t i =4095; i < 4096*8000; i++)
        {
            array2[i]='a';       
        }
          for(size_t i =4095; i < 4096*8000; i++)
        {
            array[i]='a';       
        }
        printf("%p",array2);
        for (int i = 0; i < NUM_THREADS; ++i) {
        tids[i] = i;
        pthread_create(&threads[i], NULL, thread_func, &tids[i]);
    }
    }
    else 
    {
        waitpid(pid,NULL,NULL);
    }
    
    for (int i = 0; i < NUM_THREADS; ++i) {

        pthread_join(threads[i], NULL);
    }


    printf("[Main] All threads finished.\n");

    return 0;
}
