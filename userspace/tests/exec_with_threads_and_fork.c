#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>
#include "wait.h"


#define NUM_THREADS 600

void* thread_func(void* arg) {
    long tid = (long)arg;
    for (int i = 0; i < 5; i++) {
        //printf("[Thread %ld] Running iteration %d\n", tid, i);
        
    }
    return NULL;
}

void try_exec(const char* path) {
    char* const args[] = { "Hello", "World", NULL };
    printf("[Child] Trying exec: %s\n", path);
    execv(path, args);

    // If we get here, exec failed
    
}

int main() {
    pthread_t threads[NUM_THREADS];
    printf("[Main] Creating %d threads\n", NUM_THREADS);

    for (long i = 0; i < NUM_THREADS; i++) {
       pthread_create(&threads[i], NULL, thread_func, (void*)i);
          
    }

    // Let threads run a bit

    pid_t pid = fork();
    if (pid < 0) {
        
    } else if (pid == 0) {
        // Child process: simulate one thread doing exec

        // CASE 1: Try valid exec (replaces entire process)
        try_exec("/usr/fork_exec.sweb");

        // CASE 2: Try invalid exec (uncomment to test)
        // try_exec("/not/a/real/command");

    } else {
        // Parent continues
        printf("[Parent] Forked child PID: %ld\n", pid);
        int status;
        waitpid(pid, &status, 0);
        printf("[Parent] Child exited with status: %d\n", status);

        // Join threads after child is done
        for (int i = 0; i < NUM_THREADS; i++) {
            pthread_join(threads[i], NULL);
        }

        printf("[Parent] All threads joined. Done.\n");
    }

    return 0;
}