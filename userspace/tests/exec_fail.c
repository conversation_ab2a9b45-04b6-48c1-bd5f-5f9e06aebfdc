
#include <unistd.h>
#include <stdio.h>
#include<pthread.h>
#include "wait.h"


int main() {
    pid_t pid = fork();
    if (pid == 0) {
        
        execv("/this/does/not/exist", NULL);
        printf("exec failed"); 
        _exit(42); 
    }

    int status;
    waitpid(pid, &status, 0);
    if (WIFEXITED(status)) {
        printf("Child exited with code %d\n", WEXITSTATUS(status));
    } else {
        printf("Child did not exit normally!\n");
    }
}