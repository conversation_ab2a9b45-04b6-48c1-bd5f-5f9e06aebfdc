#include <stdio.h>
#include "pthread.h"
#include <string.h>
#include <unistd.h>
#include "wait.h"


#define PAGE_SIZE 4096
static char thread_data[PAGE_SIZE * 2];

void* writer_thread(void* arg) {
    thread_data[0] = 'W';
    thread_data[PAGE_SIZE] = 'X';
    return NULL;
}

int main() {
    pthread_t t;
    pthread_create(&t, NULL, writer_thread, NULL);
    pthread_cancel(t);
    pthread_join(t, NULL);  // Ensure thread writes before fork

    int pid = fork();
    if (pid == 0) {
        thread_data[PAGE_SIZE] = 'Y'; // COW here
        printf("Child sees: %c %c\n", thread_data[0], thread_data[PAGE_SIZE]);
        _exit(0);
    } else {
        waitpid(pid, 0, 0);
        printf("Parent sees: %c %c\n", thread_data[0], thread_data[PAGE_SIZE]);
    }

    return 0;
}