#include <stdio.h>
#include<pthread.h>
#include<string.h>

pthread_t tid;
pthread_t tid2;
pthread_t tid3;
pthread_t tid4;
pthread_t tid5;

void* say_hello() {
  
  getchar();
  return (void*) 777;
}


void* say_hello2() {
  printf("Hello world\n");
  //pthread_join(tid,&ret_val );
 //printf("%d",  pthread_join(tid,&ret_val ));
  
 return (void*) 110;
}


void* say_hello3() {
  printf("Hello world\n");
  //pthread_join(tid2,&ret_val );
   
   
 return  (void*) 777;
}

void* say_hello4() {
  printf("Hello world from thread 4\n");
  //pthread_join(tid4,&ret_val );
   
 return (void*) 777;
}
void say_hello5() {
  printf("Hello world\n");
  getchar();
  
 //printf("%d",  pthread_join(tid2,&ret_val ));
}


int main(int argc, char *argv[]) {

//char a;
//char* t=&a;
//size_t i;


printf("argc:%d\n",argc);
printf("argc:%p\n",argv);




//printf("argc:%ld\n", strlen( argv[0]));
//printf("argc:%s\n", argv[0]);
for(size_t i =0; i < argc; i++)
{

  printf("%s\n",argv[i]);
  
}


 
  return 0;
}
