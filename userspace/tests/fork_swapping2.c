#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include "wait.h"
#include "pthread.h"

#define PAGE_SIZE 4096
#define NUM_PAGES 1000     
#define NUM_CHILDREN 40
#define LOCAL_ARRAY_SIZE (10*PAGE_SIZE)
#define NUM_THREADS 80

char array [NUM_PAGES* PAGE_SIZE];



void* thread_func(void* arg) {
    int tid = *(int*)arg;

    // Allocate a large local array on the stack
    char local_array[LOCAL_ARRAY_SIZE];

    // Touch memory to ensure page faults
    for (size_t i = 4095; i < LOCAL_ARRAY_SIZE; ++i) {
        local_array[i] = 'A' + (tid % 26);  // Use different char per thread
    }

    // Print the address and first/last bytes for verification
    printf("[Thread %d] array at %p, first='%c', last='%c'\n",
           tid, (void*)local_array, local_array[4095], local_array[LOCAL_ARRAY_SIZE - 1]);

    return NULL;
}


int main() {
    

    for (int i = 0; i < NUM_PAGES*PAGE_SIZE; i+=PAGE_SIZE) {
      
       
      array[i] ='a';
    }

    printf("Forking %d children...\n", NUM_CHILDREN);
    pid_t child_pids[NUM_CHILDREN];

    for (int c = 0; c < NUM_CHILDREN; ++c) {
        pid_t pid = fork();

         if (pid == 0) {
            // Child process
         
     
             exit(0);
            
        
         
        } else {
            // Parent process
            child_pids[c] = pid;
        }
    }

    // Parent waits for all children using waitpid
    for (int i = 0; i < NUM_CHILDREN; ++i) {
        int status;
        waitpid(child_pids[i], &status, 0); 
        
      
    }

    printf("[Parent] All children finished.\n");

  
    return 0;
}