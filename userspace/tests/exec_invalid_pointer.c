#include <unistd.h>
#include <stdio.h>
#include<pthread.h>

pthread_t tid;
pthread_t tid;
 pthread_t tid2;
 pthread_t tid3;
 pthread_t tid4;
 pthread_t tid5;
void*ret_val=0;

 char* array[]={"Hello","World",NULL};

 

void* say_hello() {
  
 
 printf("Hello World");
 
 
  return (void*) 777;
}





int main() {
    
  
 char **bad_argv = (char **)0xDEADBEEF; 

 execv("/usr/program.sweb",bad_argv); 
 
 printf("exec failed");
 



  return 0;
}