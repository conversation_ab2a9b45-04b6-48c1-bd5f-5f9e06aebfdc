#pragma once

#include "types.h"
#include "../../../common/include/kernel/syscall-definitions.h"
#include "stdarg.h"
#include "types.h"
#include "sys/syscall.h"

#ifdef __cplusplus
extern "C" {
#endif

#define WEXITED 4
#define WEXITSTATUS(status)   ((status) & 0xFF)
#define WIFEXITED(status) (((status) & 0xFF00) == 0) 

//pid typedefs
#ifndef PID_T_DEFINED
#define PID_T_DEFINED
typedef ssize_t pid_t;
#endif // PID_T_DEFINED

extern pid_t waitpid(pid_t pid, int *status, int options);


#ifdef __cplusplus
}
#endif



