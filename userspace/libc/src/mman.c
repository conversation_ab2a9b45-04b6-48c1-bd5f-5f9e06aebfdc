#include "sys/mman.h"
#include "sys/syscall.h"
#include "../../../common/include/kernel/syscall-definitions.h"



/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
void* mmap(void* start, size_t length, int prot, int flags, int fd,
           off_t offset)
{
   size_t arg[2];
   arg[0]= (size_t)start;
   arg[1]=offset;

  return (void*) __syscall(sc_mmap,(size_t)arg,length,prot,flags,fd);
  

}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int munmap(void* start, size_t length)
{
  return __syscall(sc_munmap,(size_t)start,length,0x0,0x0,0x0);
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int shm_open(const char* name, int oflag, mode_t mode)
{
  return __syscall(sc_shm_open,(size_t)name,oflag,(size_t)mode,0x0,0x0);
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int shm_unlink(const char* name)
{
  return __syscall(sc_shm_unlink,(size_t)name,0x0,0x0,0x0,0x0); 
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int mprotect(void *addr, size_t len, int prot)
{
  return __syscall(sc_mprotect,(size_t)addr,len,prot,0x0,0x0);
}

