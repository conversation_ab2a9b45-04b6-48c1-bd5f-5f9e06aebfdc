#include "pthread.h"
#include "assert.h"
#include "sched.h"
#include "stdio.h"

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_create(pthread_t *thread, const pthread_attr_t *attr,
                   void *(*start_routine)(void *), void *arg)
{
  return __syscall(sc_pthreadcreate, (size_t) thread, (size_t) attr, (size_t) start_routine, (size_t) arg, (size_t) wrapper);
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_spin_init(pthread_spinlock_t *lock, int pshared)
{
    if(lock == 0) return -1;
    if(lock > (unsigned int*)0x0000800000000000ULL) return -1;
    if(pshared) return -1;// not implemented
    *lock = 0;
    return 0;
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */

int pthread_mutex_init(pthread_mutex_t *mutex, const pthread_mutexattr_t *attr)
{
    if(mutex == 0) return -1;
    if(mutex > (pthread_mutex_t *)0x0000800000000000ULL) return -1;
    if(attr != 0) return -1; // not implemented
    pthread_spin_init(mutex->lock,0); // change after implementing pshared
    mutex->locked = 0;
    mutex->next = 0;
    return 0;
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_exit(void *value_ptr)
{
  return __syscall(sc_pthreadexit,(size_t) value_ptr,0x0,0x0,0x0,0x0);
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_cancel(pthread_t thread)
{
  
  return __syscall(sc_pthreadcancel,(size_t) thread, 0x0, 0x0, 0x0, 0x0);
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_join(pthread_t thread, void **value_ptr)
{
  return __syscall(sc_pthread_join,(size_t) thread, (size_t) value_ptr,0x0,0x0,0x0);
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_detach(pthread_t thread)
{
  return __syscall(sc_pthreaddetach, (size_t) thread,0x0,0x0,0x0,0x0);
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_spin_destroy(pthread_spinlock_t *lock)
{
    if(lock == 0) return -1;
    if(lock > (unsigned int*)0x0000800000000000ULL) return -1;
    return 0;
}


/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_spin_lock(pthread_spinlock_t *lock)
{
    if(lock == 0) return -1;
    if(lock > (unsigned int*)0x0000800000000000ULL) return -1;
    size_t old_val = 1;
    do
    {
        asm("xchg %0,%1"
                : "=r" (old_val)
                : "m" (*lock), "0" (old_val)
                : "memory");
    } while(old_val && !sched_yield());
    return old_val;
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_spin_trylock(pthread_spinlock_t *lock)
{
    if(lock == 0) return -1;
    if(lock > (unsigned int*)0x0000800000000000ULL) return -1;
    size_t old_val = 1;
    asm("xchg %0,%1"
            : "=r" (old_val)
            : "m" (*lock), "0" (old_val)
            : "memory");
    if(old_val == 0) return 0;
    else return 16;
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_spin_unlock(pthread_spinlock_t *lock)
{
    if(lock == 0) return -1;
    if(lock > (unsigned int*)0x0000800000000000ULL) return -1;
    size_t old_val = 0;
    asm("xchg %0,%1"
            : "=r" (old_val)
            : "m" (*lock), "0" (old_val)
            : "memory");
    return old_val - 1;
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_mutex_destroy(pthread_mutex_t *mutex)
{
    if(mutex == 0) return -1;
    if(mutex > (pthread_mutex_t *)0x0000800000000000ULL) return -1;
    pthread_spin_destroy(mutex->lock);
    //implement more later for shared memory
    return 0;
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_mutex_lock(pthread_mutex_t *mutex)
{
    if(mutex == 0) return -1;
    if(mutex > (pthread_mutex_t *)0x0000800000000000ULL) return -1;

    pthread_spin_lock(mutex->lock);
    if(mutex->locked == 0)
    {
        mutex->locked = 1;
        pthread_spin_unlock(mutex->lock);
    } else if(mutex->next == 0){

        size_t rsp = 0;
        asm ("mov %%rsp, %0" : "=r" (rsp));
        waiting_thread* pointer =(waiting_thread*) ((rsp & ~0xFFFF) + 4096 * 15 - sizeof(size_t))-1;
        //printf("userspace stackstart at: %p\n",pointer);
        mutex->next = pointer;
        //printf("userspace sleepvar at: %p\n",&pointer->sleep);
        //printf("userspace next at: %p\n",mutex->next);
        pthread_spin_unlock(mutex->lock);
        pointer->sleep = 9;
        sched_yield();
    } else{
        waiting_thread* pointer = mutex->next;
        while(pointer->next != 0)
        {
            pointer = pointer->next;
        }
        size_t rsp = 0;
        asm ("mov %%rsp, %0" : "=r" (rsp));
        pointer->next = (waiting_thread*) ((rsp & ~0xFFFF) + 4096*15 - sizeof(size_t))-1;
        pointer = pointer->next;
        pointer->sleep = 9;
        pthread_spin_unlock(mutex->lock);
        sched_yield();
    }

    return 0;

}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_mutex_unlock(pthread_mutex_t *mutex)
{
    if(mutex == 0) return -1;
    if(mutex > (pthread_mutex_t *)0x0000800000000000ULL) return -1;

    pthread_spin_lock(mutex->lock);
    if(mutex->next != 0)
    {
        mutex->next->sleep = 0;
        mutex->next = mutex->next->next;
    } else
    {
        mutex->locked = 0;
    }


    size_t rsp = 0;
    asm ("mov %%rsp, %0" : "=r" (rsp));
    waiting_thread* pointer =  (waiting_thread*) ((rsp & ~0xFFFF) + 4096*15 - sizeof(size_t))-1;
    pointer->next = 0;
    pthread_spin_unlock(mutex->lock);
    return 0;
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_cond_init(pthread_cond_t *cond, const pthread_condattr_t *attr)
{
    if(cond == 0) return -1;
    if(cond > (pthread_cond_t *)0x0000800000000000ULL) return -1;
    if(attr != 0) return -1; // not implemented

    pthread_spin_init(cond->lock,0); // change after implementing pshared
    cond->next = 0;
    return 0;
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_cond_destroy(pthread_cond_t *cond)
{
    if(cond == 0) return -1;
    if(cond > (pthread_cond_t *)0x0000800000000000ULL) return -1;
    pthread_spin_destroy(cond->lock);
    return 0;
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_cond_signal(pthread_cond_t *cond)
{
    if(cond == 0) return -1;
    if(cond > (pthread_cond_t *)0x0000800000000000ULL) return -1;

    pthread_spin_lock(cond->lock);
    if(cond->next != 0)
    {
        cond->next->sleep = 0;
        cond->next = cond->next->next;
    }

    pthread_spin_unlock(cond->lock);
    return 0;
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_cond_broadcast(pthread_cond_t *cond)
{
    if(cond == 0) return -1;
    if(cond > (pthread_cond_t *)0x0000800000000000ULL) return -1;

    pthread_spin_lock(cond->lock);
    while(cond->next != 0)
    {
        cond->next->sleep = 0;
        cond->next = cond->next->next;
    }

    pthread_spin_unlock(cond->lock);
    return 0;
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_cond_wait(pthread_cond_t *cond, pthread_mutex_t *mutex)
{
    if(cond == 0) return -1;
    if(cond > (pthread_cond_t *)0x0000800000000000ULL) return -1;
    if(mutex == 0) return -1;
    if(mutex > (pthread_mutex_t *)0x0000800000000000ULL) return -1;
    //pthread_mutex_unlock(mutex);

    pthread_spin_lock(cond->lock);
    if(cond->next == 0){

        size_t rsp = 0;
        asm ("mov %%rsp, %0" : "=r" (rsp));
        waiting_thread* pointer = (waiting_thread*) ((rsp & ~0xFFFF) + 4096*15 - sizeof(size_t))-1;
        //printf("userspace stackstart at: %p\n",pointer);
        cond->next = pointer;
        //printf("userspace sleepvar at: %p\n",&pointer->sleep);
        //printf("userspace next at: %p\n",mutex->next);
        pointer->sleep = 9;
        pthread_spin_unlock(cond->lock);
        pthread_mutex_unlock(mutex);
        sched_yield();
    } else{
        waiting_thread* pointer = cond->next;
        while(pointer->next != 0)
        {
            pointer = pointer->next;
        }
        size_t rsp = 0;
        asm ("mov %%rsp, %0" : "=r" (rsp));
        pointer->next = (waiting_thread*) ((rsp & ~0xFFFF) + 4096*15 - sizeof(size_t))-1;
        pointer = pointer->next;
        pointer->sleep = 9;
        pthread_spin_unlock(mutex->lock);
        pthread_mutex_unlock(mutex);
        sched_yield();
    }
    size_t rsp = 0;
    asm ("mov %%rsp, %0" : "=r" (rsp));
    waiting_thread* pointer =  (waiting_thread*) ((rsp & ~0xFFFF) + 4096*15 - sizeof(size_t))-1;
    pointer->next = 0;
    pthread_mutex_lock(mutex);
    return 0;
}


/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_setcancelstate(int state, int *oldstate)
{
  return __syscall(sc_pthreadsetcancelstate,(size_t) state, (size_t) oldstate, 0x0, 0x0, 0x0);
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int pthread_setcanceltype(int type, int *oldtype)
{
  return __syscall(sc_pthreadsetcanceltype,(size_t) type, (size_t) oldtype, 0x0, 0x0, 0x0);
}

int getPid()
{
    return  __syscall(sc_getPid,0x0, 0x0,  0x0, 0x0, 0x0);
  
}

void* wrapper(void *(*start_routine)(void *), void *arg){
  void *result = start_routine(arg);
  pthread_exit(result);
  return result;
}

