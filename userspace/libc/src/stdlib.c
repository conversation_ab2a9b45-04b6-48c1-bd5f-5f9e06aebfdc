#include "stdlib.h"
#include "pthread.h"
#include "unistd.h"
#include "string.h"

typedef struct Memory_Block
{

  char memory_corrup;
  size_t size;
  int free;
  void *data_pointer;

  struct Memory_Block *next;

} Memory_Block;

size_t page_size = 4096;
size_t total_used_mem;
void *initial_programm_break;
char *current_programm_break;
char *currently_used_mem;
char *start_break;
int programm_start;
int first_malloc;
size_t allocate_bytes = 0;


void *malloc(size_t size)
{

 
  struct Memory_Block *current_mem_block = NULL;
  if (size == 0)
  {
    return NULL;
  }

  if (programm_start == 0)
  {

    void *ptr = init(size);

  
    return ptr;
  }

  if (checkmemcorupt() == -1)
  {

    exit(-1);
  }

  current_mem_block = (Memory_Block *)initial_programm_break;

  if (current_mem_block->free == 1 && current_mem_block->size >= size + sizeof(Memory_Block) + 45 && current_mem_block->next == NULL)
  {
    if (((char *)current_mem_block->data_pointer + size) + size - size - sizeof(Memory_Block) < current_programm_break)
    {

      Memory_Block *new_block = (Memory_Block *)((char *)current_mem_block->data_pointer + size);
      new_block->free = 1;
      new_block->data_pointer = ((char *)new_block + sizeof(Memory_Block));
      new_block->size = current_mem_block->size - size - sizeof(Memory_Block);
      new_block->memory_corrup = 'c';

      new_block->next = NULL;

      current_mem_block->next = new_block;

      current_mem_block->size = size;
      current_mem_block->free = 0;
      total_used_mem += size;
    }

   
    return current_mem_block->data_pointer;
  }

  else if (current_mem_block->free == 1 && current_mem_block->size >= size && current_mem_block->size < size + sizeof(Memory_Block) + 45 && current_mem_block->next == NULL)
  {
    current_mem_block->size = size;
    current_mem_block->free = 0;
    total_used_mem += size;
   
    return current_mem_block->data_pointer;
  }

  if (current_mem_block->next != NULL)
  {
    while (current_mem_block->next != NULL)
    {
      if (((char *)current_mem_block->data_pointer + size) + size - size - sizeof(Memory_Block) < current_programm_break)
      {

        if (current_mem_block->free == 1 && current_mem_block->size >= size + sizeof(Memory_Block) + 45)
        {
          Memory_Block *new_block = (Memory_Block *)((char *)current_mem_block->data_pointer + size);
          new_block->free = 1;
          new_block->data_pointer = ((char *)new_block + sizeof(Memory_Block));
          new_block->size = current_mem_block->size - size - sizeof(Memory_Block);
          new_block->memory_corrup = 'c';

          new_block->next = current_mem_block->next;

          current_mem_block->next = new_block;

          current_mem_block->size = size;
          current_mem_block->free = 0;
          total_used_mem += size;
          
          return current_mem_block->data_pointer;
        }
      }

      current_mem_block = current_mem_block->next;
    }
    current_mem_block = (Memory_Block *)initial_programm_break;

    while (current_mem_block->next != NULL)
    {

      if (current_mem_block->free == 1 && current_mem_block->size >= size && current_mem_block->size < size + sizeof(Memory_Block) + 45)
      {
        current_mem_block->size = size;

        current_mem_block->free = 0;
        total_used_mem += size;
       
        return current_mem_block->data_pointer;
      }

      current_mem_block = current_mem_block->next;
    }
  }

  if (current_mem_block->free == 1 && current_mem_block->size >= size + sizeof(Memory_Block) + 45)
  {

    if (((char *)current_mem_block->data_pointer + size) + size - size - sizeof(Memory_Block) < current_programm_break)
    {

      Memory_Block *new_block = (Memory_Block *)((char *)current_mem_block->data_pointer + size);
      new_block->free = 1;
      new_block->data_pointer = ((char *)new_block + sizeof(Memory_Block));
      new_block->size = current_mem_block->size - size - sizeof(Memory_Block);
      new_block->memory_corrup = 'c';

      new_block->next = current_mem_block->next;

      current_mem_block->next = new_block;

      current_mem_block->size = size;
      current_mem_block->free = 0;
      total_used_mem += size;
     
      return current_mem_block->data_pointer;
    }
  }

  else if (current_mem_block->free == 1 && current_mem_block->size >= size && current_mem_block->size < size + sizeof(Memory_Block) + 45)
  {
    current_mem_block->size = size;

    current_mem_block->free = 0;
    total_used_mem += size;
    
    return current_mem_block->data_pointer;
  }

  void *ptr = allocate_mem(size);

 
  return ptr;
}

void free(void *ptr)
{
 
  Memory_Block *start = (Memory_Block *)initial_programm_break;
  // Memory_Block* current_mem_block=(Memory_Block*) ((char*) ptr-sizeof(Memory_Block));

  if (ptr != NULL)
  {

    if (initial_programm_break == (Memory_Block *)current_programm_break)
    {
      
      exit(-1);
    }

    if (checkmemcorupt() == -1)
    {
     
      exit(-1);
    }

    if (start->next == NULL && start->data_pointer != ptr)
    {
      
      exit(-1);
    }

    if (start->data_pointer != ptr && start->next != NULL)
    {

      while (start->next != NULL)
      {

        start = start->next;

        if (start->data_pointer == ptr)
        {
          break;
        }

        if (start->data_pointer != ptr && start->next == NULL)
        {
          
          exit(-1);
        }
      }
    }

    if (start->free == 1)
    {
      
      exit(-1);
    }

    start->free = 1;

    total_used_mem -= start->size;

    setbrktoinitial();
  }

  
}

int atexit(void (*function)(void))
{
  return -1;
}

void *calloc(size_t nmemb, size_t size)
{
  char *array = (char *)malloc(nmemb * size);

  for (size_t i = 0; i < nmemb * size; i++)
  {
    array[i] = 0;
  }

  return array;
}

void *realloc(void *ptr, size_t size)
{
  Memory_Block *current_mem_block = (Memory_Block *)initial_programm_break;
  Memory_Block *old_block;
  if (size == 0)
  {
    return NULL;
  }

  if (programm_start == 0)
  {

    void *ptr = init(size);

    
    return ptr;
  }

  while (current_mem_block->next != NULL)
  {

    if (current_mem_block->data_pointer == ptr)
    {
      old_block = current_mem_block;
      break;
    }
    current_mem_block = current_mem_block->next;
    // current_mem_block=(Memory_Block*) initial_programm_break;

    if (current_mem_block->data_pointer == ptr)
    {
      old_block = current_mem_block;
    }
  }

  if (old_block != NULL)
  {
    if (old_block->free)
    {
      exit(-1);
    }

    void *return_value = findblockrealloc(ptr, size, (void *)old_block);

    if (return_value != NULL)
    {
      return return_value;
    }

    freechunckmerging();

    current_mem_block = (Memory_Block *)initial_programm_break;
    return_value = findblockrealloc(ptr, size, (void *)old_block);

    if (return_value != NULL)
    {
      return return_value;
    }

    void *new_block = malloc(size);
    if (size >= old_block->size)
    {
      memcpy(new_block, ptr, old_block->size);
    }
    else
    {
      memcpy(new_block, ptr, size);
    }
    free(ptr);
    return new_block;
  }
  else
  {
    exit(-1);
  }

  return 0;
}

size_t total_used_memory()
{

  return total_used_mem;
}

size_t get_used_block_size(int type, void *ptr_or_index)
{
  //(void) type;
  //(void) ptr_or_index;

  Memory_Block *current_mem_block = (Memory_Block *)initial_programm_break;

  if (type >= 0)
  {

    size_t index_ = (size_t)ptr_or_index;
    // size_t index_= 19;
    size_t counter = 0;
    if (total_used_mem > 0)
    {
      while (current_mem_block->next != NULL)
      {
        if (counter == index_ && current_mem_block->free == 0)
        {
          return current_mem_block->size;
        }
        if (current_mem_block->free == 0)
        {
          counter += 1;
        }

        current_mem_block = current_mem_block->next;
      }
      if (counter == index_ && current_mem_block->free == 0)
      {
        return current_mem_block->size;
      }
    }
  }

  if (type == 0)
  {
    if (total_used_mem > 0)
    {
      current_mem_block = (Memory_Block *)initial_programm_break;

      while (current_mem_block->next != NULL)
      {

        if (current_mem_block->data_pointer == ptr_or_index && current_mem_block->free == 0)
        {
          return current_mem_block->size;
        }
        current_mem_block = current_mem_block->next;
      }
      if (current_mem_block->data_pointer == ptr_or_index && current_mem_block->free == 0)
      {
        return current_mem_block->size;
      }
    }
  }

  return 0;
}

void *init(size_t size)
{

  if (first_malloc == 0)
  {
    initial_programm_break = sbrk(0);
    first_malloc = 1;
  }

  Memory_Block *current_mem_block = (Memory_Block *)initial_programm_break;
  // initial_programm_break= (void*) current_mem_block;
  // initial_programm_break= (char*) current_mem_block;

  // current_programm_break=((char*) initial_programm_break+size+sizeof(Memory_Block));

  if (size + sizeof(Memory_Block) < 4096)
  {
    current_programm_break = ((char *)initial_programm_break + 4096 + sizeof(Memory_Block));
  }
  else
  {
    while (page_size < size + sizeof(Memory_Block))
    {
      page_size += page_size;
    }

    current_programm_break = ((char *)initial_programm_break + page_size);
  }
  currently_used_mem = ((char *)initial_programm_break + size + sizeof(Memory_Block));
  // start_break= (char*)initial_programm_break;

  /**if(current_programm_break > (((char*)(initial_programm_break)+ (size_t) 1024*1024*1024*64)))
 {
   return NULL;
 }**/

  if (brk(current_programm_break) == -1)
  {
    return NULL;
  }
  // snp::sbrk(available_memory);
  // snp::sbrk(available_memory);
  // total_used_mem=
  // current_mem_block= (Memory_Block*)initial_programm_break ;
  // initial_programm_break=initial_programm_break+sizeof(Memory_Block)+size;
  // available_memory-=size;
  // available_memory -=size+ sizeof(Memory_Block);

  /**if(size==72704)
  {

    current_mem_block->memory_corrup='c';
     current_mem_block->size= size;
    current_mem_block->free=1;
    current_mem_block->next=NULL;
    current_mem_block->data_pointer=(char*) current_mem_block+sizeof(Memory_Block);

  }**/
  // else
  //{
  current_mem_block->memory_corrup = 'c';
  current_mem_block->size = size;
  current_mem_block->free = 0;
  current_mem_block->next = NULL;
  current_mem_block->data_pointer = (char *)current_mem_block + sizeof(Memory_Block);
  //}
  // printf("%p")
  // initial_programm_break= (size_t*)initial_programm_break+sizeof(Memory_Block)+size;

  total_used_mem += size;
  programm_start = 1;
  return (char *)current_mem_block + sizeof(Memory_Block);
}

void *allocate_mem(size_t size)
{

  Memory_Block *current_mem_block = (Memory_Block *)initial_programm_break;

  Memory_Block *prev = current_mem_block;

  currently_used_mem += size + sizeof(Memory_Block);

  /**  if(current_programm_break <= currently_used_mem)
    {
    while(current_programm_break<= currently_used_mem )
    {
     current_programm_break+=page_size*10;
    }


if(snp::brk(current_programm_break)==-1)
 {


    return NULL;
 }

    }**/

  current_mem_block = (Memory_Block *)initial_programm_break;

  while ((current_mem_block->next != NULL))
  {
    current_mem_block = current_mem_block->next;
  }

  prev = current_mem_block;

  if (current_programm_break <= (((char *)current_mem_block) + sizeof(Memory_Block) + current_mem_block->size + sizeof(Memory_Block) + size))

  {
    while (current_programm_break <= (((char *)current_mem_block) + sizeof(Memory_Block) + current_mem_block->size) + sizeof(Memory_Block) + size)
    {
      // allocate_bytes=4096;
      current_programm_break += 4096 * 4;
    }

    /**if(current_programm_break > (((char*)(initial_programm_break)+ (size_t) 1024*1024*1024*64)))
    {
      return NULL;
    }**/

    if (brk(current_programm_break) == -1)
    {

      return NULL;
    }
  }

  current_mem_block = (Memory_Block *)(((char *)current_mem_block) + sizeof(Memory_Block) + current_mem_block->size);

  current_mem_block->data_pointer = (char *)current_mem_block + sizeof(Memory_Block);

  current_mem_block->free = 0;
  current_mem_block->memory_corrup = 'c';

  current_mem_block->size = size;
  currently_used_mem += size + sizeof(Memory_Block);
  current_mem_block->next = NULL;
  prev->next = current_mem_block;

  total_used_mem += size;

  return (char *)current_mem_block + sizeof(Memory_Block);
}

void setbrktoinitial()
{

  if (current_programm_break > (char *)initial_programm_break)
  {
    Memory_Block *current_mem_block = (Memory_Block *)initial_programm_break;
    if (current_mem_block->free == 1)
    {
      while (current_mem_block->free == 1)
      {

        if (current_mem_block->next == NULL && current_mem_block->free == 1)
        {
          brk(initial_programm_break);
          current_programm_break = (char *)initial_programm_break;
          currently_used_mem = (char *)initial_programm_break;
          programm_start = 0;
          total_used_mem = 0;
          break;
        }

        current_mem_block = current_mem_block->next;
      }
    }
  }
}

void freechunckmerging()
{

  Memory_Block *current_mem_block = (Memory_Block *)initial_programm_break;
  Memory_Block *prev = (Memory_Block *)initial_programm_break;

  if (current_mem_block->next != NULL)
  {

    while (current_mem_block->next != NULL)
    {

      prev = current_mem_block;
      current_mem_block = current_mem_block->next;
      // current_mem_block=(Memory_Block*) initial_programm_break;

      if (prev->free == 1 && current_mem_block->free == 1)
      {
        // brk(initial_programm_break);
        prev->size += current_mem_block->size + sizeof(Memory_Block);
        // prev->next =NULL;
        if (current_mem_block->next != NULL)
        {
          prev->next = current_mem_block->next;
        }
        else
        {
          prev->next = NULL;
        }

        // current_mem_block=NULL;
        // break;
        current_mem_block = prev;
      }

      // current_mem_block=(Memory_Block*) initial_programm_break;
    }

    if (prev->free == 1 && current_mem_block->free == 1 && prev != current_mem_block)
    {
      prev->size += current_mem_block->size + sizeof(Memory_Block);
      if (current_mem_block->next != NULL)
      {
        prev->next = current_mem_block->next;
      }
      else
      {
        prev->next = NULL;
      }

      current_mem_block = (Memory_Block *)initial_programm_break;
    }
  }
}

int checkmemcorupt()
{
  Memory_Block *current_mem_block = (Memory_Block *)initial_programm_break;

  if (current_mem_block->memory_corrup == 'c' && current_mem_block->next == NULL)
  {
    return 0;
  }

  while (current_mem_block->next != NULL)
  {
    if (current_mem_block->memory_corrup != 'c')
    {
      return -1;
    }

    current_mem_block = current_mem_block->next;
  }
  if (current_mem_block->memory_corrup != 'c')
  {
    return -1;
  }

  return 0;
}

void *findblockrealloc(void *ptr, size_t size, void *previous_block)
{
  Memory_Block *current_mem_block = (Memory_Block *)initial_programm_break;
  Memory_Block *old_block = (Memory_Block *)previous_block;
  if (size >= old_block->size)
  {
    while (current_mem_block->next != NULL)
    {

      if (current_mem_block->free == 1 && current_mem_block->size >= size)
      {
        current_mem_block->free = 0;
        memcpy(current_mem_block->data_pointer, ptr, old_block->size);
        free(ptr);
        return current_mem_block->data_pointer;
      }

      current_mem_block = current_mem_block->next;
    }
  }
  else
  {
    while (current_mem_block->next != NULL)
    {

      if (current_mem_block->free == 1 && current_mem_block->size >= size)
      {
        current_mem_block->free = 0;
        memcpy(current_mem_block->data_pointer, ptr, size);
        free(ptr);
        return current_mem_block->data_pointer;
      }

      current_mem_block = current_mem_block->next;
    }
  }
  return 0;
}


void reserve_pages(size_t amount)
{
    __syscall(sc_reserve_pages,amount,0x0,0x0,0x0,0x0);
}