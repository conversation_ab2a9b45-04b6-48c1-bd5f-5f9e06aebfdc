#include "nonstd.h"
#include "sys/syscall.h"
#include "../../../common/include/kernel/syscall-definitions.h"
#include "stdlib.h"

int createprocess(const char* path, int sleep)
{
  return __syscall(sc_createprocess, (long) path, sleep, 0x00, 0x00, 0x00);
}

int swap_page_out(size_t vpn)
{
  return __syscall(sc_swap_page_out, vpn, 0x0, 0x00, 0x00, 0x00);
}

int swap_page_in(size_t vpn)
{
  return __syscall(sc_swap_page_in, vpn, 0x0, 0x00, 0x00, 0x00);
}

extern int main();

void _start()
{
  exit(main());
}
