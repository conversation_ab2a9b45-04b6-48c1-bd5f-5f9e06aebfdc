#include "semaphore.h"
#include "pthread.h"
#include "sched.h"

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int sem_wait(sem_t *sem)
{
    if(sem == 0) return -1;
    if(sem > (sem_t *)0x0000800000000000ULL) return -1;

    pthread_spin_lock(sem->lock);
    if(sem->locked > 0)
    {
        sem->locked--;
        pthread_spin_unlock(sem->lock);
    } else if(sem->next == 0){

        size_t rsp = 0;
        asm ("mov %%rsp, %0" : "=r" (rsp));
        waiting_thread* pointer = (waiting_thread*) ((rsp & ~0xFFFF) + 4096*15 - sizeof(size_t))-1;
        //printf("userspace stackstart at: %p\n",pointer);
        sem->next = pointer;
        //printf("userspace sleepvar at: %p\n",&pointer->sleep);
        //printf("userspace next at: %p\n",mutex->next);
        pointer->sleep = 9;
        pthread_spin_unlock(sem->lock);
        sched_yield();
    } else{
        waiting_thread* pointer = sem->next;
        while(pointer->next != 0)
        {
            pointer = pointer->next;
        }
        size_t rsp = 0;
        asm ("mov %%rsp, %0" : "=r" (rsp));
        pointer->next = (waiting_thread*) ((rsp & ~0xFFFF) + 4096*15 - sizeof(size_t))-1;
        pointer = pointer->next;
        pointer->sleep = 9;
        pthread_spin_unlock(sem->lock);
        sched_yield();
    }

    size_t rsp = 0;
    asm ("mov %%rsp, %0" : "=r" (rsp));
    waiting_thread* pointer =  (waiting_thread*) ((rsp & ~0xFFFF) + 4096*15 - sizeof(size_t))-1;
    pointer->next = 0;
    return 0;
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int sem_trywait(sem_t *sem)
{
  return -1;
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int sem_init(sem_t *sem, int pshared, unsigned value)
{
    if(sem == 0) return -1;
    if(sem > (sem_t *)0x0000800000000000ULL) return -1;
    if(pshared != 0) return -1; // not implemented

    pthread_spin_init(sem->lock,0); // change after implementing pshared
    sem->locked = value;
    sem->next = 0;
    return 0;
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int sem_destroy(sem_t *sem)
{
    if(sem == 0) return -1;
    if(sem > (sem_t *)0x0000800000000000ULL) return -1;
    pthread_spin_destroy(sem->lock);
    //implement more later for shared memory
    return 0;
}

/**
 * function stub
 * posix compatible signature - do not change the signature!
 */
int sem_post(sem_t *sem)
{
    if(sem == 0) return -1;
    if(sem > (sem_t *)0x0000800000000000ULL) return -1;

    pthread_spin_lock(sem->lock);
    if(sem->next != 0)
    {
        sem->next->sleep = 0;
        sem->next = sem->next->next;
    } else
    {
        sem->locked++;
    }



    pthread_spin_unlock(sem->lock);
    return 0;
}


