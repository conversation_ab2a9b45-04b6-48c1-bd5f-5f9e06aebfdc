#   _______            __   __ __ __
#  |       .-----.----|  |_|__|  |  .---.-.-----.
#  |.|   | |  _  |   _|   _|  |  |  |  _  |__ --|
#  `-|.  |-|_____|__| |____|__|__|__|___._|_____|
#    |:  |
#    |::.|   Configuration file
#    `---'

threads: 8

# Timeouts
bootup_timeout_secs: 20
default_test_timeout_secs: 100

# Syscall numbers to detect bootup and test completion
sc_tortillas_bootup: 400
sc_tortillas_finished: 401

# The analyze config field contains entries that describe
# how tortillas parses sweb logs.
analyze:

# Exit codes
  - name: exit_codes # Unique identifier
    scope: SYSCALL # as in `debug(SYSCALL, ...)`
    pattern: 'Syscall::EXIT: called, exit_code: (\d+)' # match exit code
    mode: exit_codes # special mode that checks exit codes
    set_status: FAILED # set status FAILED, if unexpected exit codes

# Userspace asserts
  - name: userspace_asserts
    scope: SYSCALL
    pattern: 'Syscall::write: (Assertion failed:.*)'
    mode: add_as_error # add matches as errors
    set_status: FAILED

# Locking errors
  - name: lock_logs
    scope: LOCK
    pattern: '(.*)'
    mode: add_as_error

# Command not understood
  - name: command_not_understood
    scope: SYSCALL
    pattern: 'Syscall::write: (Unknown command)'
    mode: add_as_error
    set_status: FAILED

# Kernel panics
  - name: kernel_panics
    scope: 'KERNEL PANIC'
    pattern: '(.*)'
    mode: add_as_error
    set_status: PANIC

# Backtraces
  - name: backtrace
    scope: BACKTRACE
    pattern: '(.*)'
    mode: add_as_error_join
