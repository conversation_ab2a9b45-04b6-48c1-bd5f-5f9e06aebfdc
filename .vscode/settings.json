{
    "cmake.buildDirectory": "/tmp/sweb",
    "debug.onTaskErrors": "showErrors",
    "cmake.configureSettings": {
        "DDEBUG":"1",
    },
    "cmake.buildTask": true,
    "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools",
    "cmake.generator": "Unix Makefiles",
    "cmake.configureOnOpen": true,
    "files.associations": {
        "random": "cpp",
        "numbers": "cpp",
        "array": "cpp",
        "deque": "cpp",
        "list": "cpp",
        "string": "cpp",
        "unordered_map": "cpp",
        "unordered_set": "cpp",
        "vector": "cpp",
        "string_view": "cpp",
        "initializer_list": "cpp"
    }
}