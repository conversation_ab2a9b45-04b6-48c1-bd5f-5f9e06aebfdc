#!/bin/sh

SOURCE_DIR="$1"
OUTPUT_FILE="$2"

generate () {
  echo "// Automatically generated by gen_to_string.sh at $(date)"
  echo "// DO NOT EDIT"
  echo
  echo '#include "data.hh"'
  echo '#include "to_hex.hh"'
  echo
  echo 'ELFPP_BEGIN_NAMESPACE'
  echo
  PYTHON=python
  command -v $PYTHON > /dev/null 2>&1
  if [ $? -ne 0 ]; then
    PYTHON=python3
  fi

  $PYTHON "$SOURCE_DIR"/enum-print.py -u --hex --no-type --mask shf --mask pf \
          -x loos -x hios -x loproc -x hiproc < "$SOURCE_DIR"/data.hh
  echo 'ELFPP_END_NAMESPACE'
}

generate > "$OUTPUT_FILE"
